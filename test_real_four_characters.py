#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试真实的四个角色功能 - 使用"未发生"项目的角色
"""

import os
import sys

# 添加项目路径到sys.path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from pyz.工作流.A7_Pollinations工作流 import 生成角色参考固定词

def test_未发生项目四个角色():
    """测试未发生项目的四个角色功能"""
    print("=" * 60)
    print("测试未发生项目的四个角色功能")
    print("=" * 60)
    
    # 模拟参考图URL
    模拟URL = "https://example.com/four_characters.png"
    
    # 根据实际文件名的角色组合
    真实角色组合 = [
        ("母亲", "单个角色"),
        ("父亲", "单个角色"),
        ("挑水老人", "单个角色"),
        ("迷路的孩子", "单个角色"),
        ("陈默", "单个角色"),
        ("母亲,父亲", "两个角色"),
        ("母亲,父亲,挑水老人", "三个角色"),
        ("母亲,父亲,挑水老人,迷路的孩子", "四个角色 - 按照文件名顺序"),
        ("父亲,母亲,迷路的孩子,挑水老人", "四个角色 - 不同顺序"),
        ("母亲,父亲,挑水老人,迷路的孩子,陈默", "五个角色"),
    ]
    
    for 角色名, 描述 in 真实角色组合:
        print(f"\n🧪 测试{描述}: {角色名}")
        
        固定词 = 生成角色参考固定词(角色名, 模拟URL)
        if 固定词:
            print(f"✅ 生成固定词: {固定词}")
            
            # 验证四个角色的特殊情况
            if "四个角色" in 描述:
                角色列表 = [角色.strip() for 角色 in 角色名.split(',')]
                print(f"📋 角色列表: {角色列表}")
                
                # 验证位置描述
                if "upper left" in 固定词 and "upper right" in 固定词 and "lower left" in 固定词 and "lower right" in 固定词:
                    print("✅ 包含所有四个位置描述")
                    
                    # 验证角色名
                    所有角色都在 = all(角色 in 固定词 for 角色 in 角色列表)
                    if 所有角色都在:
                        print("✅ 包含所有角色名")
                        
                        # 显示具体的位置分配
                        print(f"📍 位置分配:")
                        print(f"   左上角: {角色列表[0]}")
                        print(f"   右上角: {角色列表[1]}")
                        print(f"   左下角: {角色列表[2]}")
                        print(f"   右下角: {角色列表[3]}")
                    else:
                        print("❌ 缺少部分角色名")
                else:
                    print("❌ 缺少位置描述")
        else:
            print("❌ 未生成固定词")

def test_四个角色文件名解析():
    """测试四个角色文件名解析"""
    print("\n" + "=" * 60)
    print("测试四个角色文件名解析")
    print("=" * 60)
    
    # 模拟真实的四个角色文件名
    真实文件名 = "左上角母亲_右上角父亲_左下角挑水老人_右下角迷路的孩子.png"
    
    print(f"🧪 测试文件名: {真实文件名}")
    
    # 使用与实际代码相同的解析逻辑
    import re
    文件中的角色 = []
    
    # 匹配模式：支持多种位置描述
    角色匹配 = re.findall(r'(?:左边|中间|右边|左上角|右上角|左下角|右下角)([^_\.]+)', 真实文件名)
    if 角色匹配:
        文件中的角色 = 角色匹配
        print(f"✅ 解析出角色: {文件中的角色}")
        print(f"📊 解析出 {len(文件中的角色)} 个角色")
        
        # 验证角色顺序
        期望角色 = ["母亲", "父亲", "挑水老人", "迷路的孩子"]
        if 文件中的角色 == 期望角色:
            print("✅ 角色顺序正确")
        else:
            print(f"❌ 角色顺序不匹配，期望: {期望角色}")
    else:
        print("❌ 解析失败")

def test_完整提示词构建():
    """测试完整提示词构建"""
    print("\n" + "=" * 60)
    print("测试完整提示词构建")
    print("=" * 60)
    
    # 模拟完整的提示词构建过程
    原始提示词 = "A family scene with parents and children in a rural setting"
    角色名 = "母亲,父亲,挑水老人,迷路的孩子"
    参考图URL = "https://example.com/family_scene.png"
    
    print(f"📝 原始提示词: {原始提示词}")
    print(f"🎭 角色组合: {角色名}")
    
    # 生成角色参考固定词
    角色参考固定词 = 生成角色参考固定词(角色名, 参考图URL)
    
    # 构建完整提示词
    完整提示词 = 原始提示词
    if 角色参考固定词:
        完整提示词 = f"{角色参考固定词}, {完整提示词}"
    
    # 模拟添加风格词到末尾
    风格词 = "Chinese animation style, high quality, detailed"
    完整提示词 = f"{完整提示词}, {风格词}"
    
    print(f"🎯 最终提示词: {完整提示词}")
    print(f"📊 提示词长度: {len(完整提示词)} 字符")
    
    # 验证结构
    if 角色参考固定词:
        if 完整提示词.startswith(角色参考固定词):
            print("✅ 角色参考固定词在最前面")
        else:
            print("❌ 角色参考固定词位置错误")
        
        if 完整提示词.endswith(风格词):
            print("✅ 风格词在最后面")
        else:
            print("❌ 风格词位置错误")

if __name__ == "__main__":
    print("🚀 开始测试未发生项目的四个角色功能")
    
    # 测试四个角色固定词生成
    test_未发生项目四个角色()
    
    # 测试文件名解析
    test_四个角色文件名解析()
    
    # 测试完整提示词构建
    test_完整提示词构建()
    
    print("\n" + "=" * 60)
    print("✅ 测试完成")
    print("=" * 60)
