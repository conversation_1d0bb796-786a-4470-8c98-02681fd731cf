#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试角色名翻译功能
"""

import os
import sys

# 添加项目路径到sys.path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from pyz.工作流.A7_Pollinations工作流 import 翻译角色名, 生成角色参考固定词

def test_角色名翻译():
    """测试角色名翻译功能"""
    print("=" * 60)
    print("测试角色名翻译功能")
    print("=" * 60)
    
    # 测试角色名
    测试角色 = [
        "艾米丽",
        "露西", 
        "斑斑",
        "母亲",
        "父亲",
        "挑水老人",
        "迷路的孩子",
        "陈默",
        "小明",
        "小红",
        "小绿",
        "小蓝",
        "小黄",
        "老师",  # 不在映射表中，需要翻译
        "医生",  # 不在映射表中，需要翻译
        "警察",  # 不在映射表中，需要翻译
    ]
    
    for 角色名 in 测试角色:
        print(f"\n🧪 翻译角色名: {角色名}")
        try:
            英文名 = 翻译角色名(角色名)
            if 英文名 != 角色名:
                print(f"✅ 翻译成功: {角色名} → {英文名}")
            else:
                print(f"⚠️ 保持原名: {角色名}")
        except Exception as e:
            print(f"❌ 翻译失败: {e}")

def test_翻译后的固定词生成():
    """测试翻译后的固定词生成"""
    print("\n" + "=" * 60)
    print("测试翻译后的固定词生成")
    print("=" * 60)
    
    模拟URL = "https://example.com/image.png"
    
    测试用例 = [
        ("艾米丽", "单个角色"),
        ("艾米丽,露西", "两个角色"),
        ("艾米丽,露西,斑斑", "三个角色"),
        ("母亲,父亲,挑水老人,迷路的孩子", "四个角色"),
        ("艾米丽,露西,斑斑,小明,小红", "五个角色"),
    ]
    
    for 角色名, 描述 in 测试用例:
        print(f"\n🧪 测试{描述}: {角色名}")
        
        # 显示原始角色名
        原始角色列表 = [角色.strip() for 角色 in 角色名.split(',')]
        print(f"📝 原始角色: {原始角色列表}")
        
        # 显示翻译后的角色名
        翻译后角色列表 = [翻译角色名(角色.strip()) for 角色 in 角色名.split(',')]
        print(f"🌐 翻译后角色: {翻译后角色列表}")
        
        # 生成固定词
        固定词 = 生成角色参考固定词(角色名, 模拟URL)
        if 固定词:
            print(f"✅ 生成固定词: {固定词}")
            
            # 验证是否使用了英文角色名
            使用英文 = all(英文角色 in 固定词 for 英文角色 in 翻译后角色列表)
            if 使用英文:
                print("✅ 固定词中使用了英文角色名")
            else:
                print("❌ 固定词中仍使用中文角色名")
        else:
            print("❌ 未生成固定词")

def test_完整提示词构建():
    """测试完整提示词构建（包含翻译）"""
    print("\n" + "=" * 60)
    print("测试完整提示词构建（包含翻译）")
    print("=" * 60)
    
    # 测试案例
    测试案例 = [
        {
            "角色名": "母亲,父亲,挑水老人,迷路的孩子",
            "原始提示词": "A family scene in a rural village",
            "描述": "四个角色的家庭场景"
        },
        {
            "角色名": "艾米丽,露西",
            "原始提示词": "Two girls having a conversation",
            "描述": "两个女孩对话"
        }
    ]
    
    for 案例 in 测试案例:
        角色名 = 案例["角色名"]
        原始提示词 = 案例["原始提示词"]
        描述 = 案例["描述"]
        
        print(f"\n🧪 测试{描述}")
        print(f"📝 原始提示词: {原始提示词}")
        print(f"🎭 角色组合: {角色名}")
        
        # 显示翻译对比
        原始角色列表 = [角色.strip() for 角色 in 角色名.split(',')]
        翻译后角色列表 = [翻译角色名(角色.strip()) for 角色 in 角色名.split(',')]
        
        print(f"🔄 翻译对比:")
        for 原始, 翻译 in zip(原始角色列表, 翻译后角色列表):
            print(f"   {原始} → {翻译}")
        
        # 生成角色参考固定词
        参考图URL = "https://example.com/reference.png"
        角色参考固定词 = 生成角色参考固定词(角色名, 参考图URL)
        
        # 构建完整提示词
        完整提示词 = 原始提示词
        if 角色参考固定词:
            完整提示词 = f"{角色参考固定词}, {完整提示词}"
        
        # 添加风格词
        风格词 = "anime style, high quality"
        完整提示词 = f"{完整提示词}, {风格词}"
        
        print(f"🎯 最终提示词: {完整提示词}")
        print(f"📊 提示词长度: {len(完整提示词)} 字符")

if __name__ == "__main__":
    print("🚀 开始测试角色名翻译功能")
    
    # 测试角色名翻译
    test_角色名翻译()
    
    # 测试翻译后的固定词生成
    test_翻译后的固定词生成()
    
    # 测试完整提示词构建
    test_完整提示词构建()
    
    print("\n" + "=" * 60)
    print("✅ 测试完成")
    print("=" * 60)
