# 导入所有拆分的方法
# 用于在主文件中一次性导入所有方法

# 导入所有方法文件
from .语音分镜ui import 语音分镜ui
from .功能按钮 import 功能按钮
from .绘图基础设置 import 绘图基础设置
from .绘图进阶参数 import 绘图进阶参数
from .隐藏组件 import 隐藏组件
from .创建ui import 创建ui
from .提示 import 提示
from .调整图片风格 import 调整图片风格
from .绘图设置按钮 import 绘图设置按钮
from .部署云端镜像 import 部署云端镜像
from .海外模式点击事件 import 海外模式点击事件
from .刷新本地配置 import 刷新本地配置
from .更换模型 import 更换模型
from .多GPU点击事件 import 多GPU点击事件
from .检测系统GPU import 检测系统GPU
from .修改vae节点 import 修改vae节点
from .更换VAE import 更换VAE
from .调整SD参数 import 调整SD参数
from .特效点击事件 import 特效点击事件
from .视频设置按钮 import 视频设置按钮
from .创建绘图任务布局 import 创建绘图任务布局
from .下拉框点击事件 import 下拉框点击事件
from .推理增强点击事件 import 推理增强点击事件
from .图片辅助点击事件 import 图片辅助点击事件
from .存储工作流 import 存储工作流
from .删除选中工作流 import 删除选中工作流
from .绘图速度点击事件 import 绘图速度点击事件
from .浏览器绘图方案选择事件 import 浏览器绘图方案选择变化事件, 浏览器绘图无头模式点击事件, 浏览器绘图并发数变化事件
from .SD下拉框点击事件 import SD下拉框点击事件
from .任务模式切换事件 import 任务模式切换事件
from .on_slider_change import on_slider_change
from .创建滑动条 import 创建滑动条
from .软件Key设置 import 软件Key设置, update_models_for_platform, on_platform_changed, update_platform_status, open_platform_config
from .绘图模型按钮 import 绘图模型按钮
from .缩放 import 缩放
from .语音分镜下拉框点击事件 import 语音分镜下拉框点击事件
from .创建语音分镜组 import 创建语音分镜组
from .update_characters import update_characters
from .update_roles_and_styles import update_roles_and_styles
from .创建微软语音控件 import 创建微软语音控件
from .创建文视语音控件 import 创建文视语音控件
from .获取语音配置 import 获取语音配置
from .获取参考音 import 获取参考音
from .获取tts模型 import 获取tts模型
from .上传参考音频 import 上传参考音频
from .创建语音任务布局 import 创建语音任务布局
from .创建视频底部布局 import 创建视频底部布局
from .on_third_button_clicked import on_third_button_clicked
from .url_clicked import url_clicked
from .声音点击事件 import 声音点击事件
from .print_values import print_values
from .closeEvent import closeEvent
from .保存所有配置 import 保存所有配置
from .加载工作流选项 import 加载工作流选项
from .工作流选择事件 import 工作流选择事件
from .启动多GPU_ComfyUI服务 import 启动多GPU_ComfyUI服务
from .更换Pollinations模型 import 更换Pollinations模型
from .更换Pollinations风格 import 更换Pollinations风格
from .更新Pollinations密钥 import 更新Pollinations密钥, 切换Pollinations密钥显示
from .更新Pollinations显示状态 import 更新Pollinations显示状态
from .Pollinations点击事件 import Pollinations点击事件
from .创建推理模板组 import 创建推理模板组
from .加载推理模板选项 import 加载推理模板选项
from .open_template_manager import open_template_manager
from .on_template_changed import on_template_changed
from .创建翻译设置组 import 创建翻译设置组, on_translation_platform_changed, 加载翻译模型选项
from .on_translation_method_changed import on_translation_method_changed
from .on_translation_model_changed import on_translation_model_changed
from .创建智能场景匹配组 import 创建智能场景匹配组
from .加载场景匹配模型选项 import 加载场景匹配模型选项
from .on_scene_matching_enabled_changed import on_scene_matching_enabled_changed
from .on_scene_matching_platform_changed import on_scene_matching_platform_changed
from .on_scene_matching_model_changed import on_scene_matching_model_changed
from .select_background import select_background
from .clear_background import clear_background
from .apply_background import apply_background
from .update_background_combo import update_background_combo
from .background_combo_changed import background_combo_changed
from .选择背景图片 import 选择背景图片
from .清除背景图片 import 清除背景图片
from .应用背景图片 import 应用背景图片
from .常规设置 import 常规设置, 创建简单其他设置组, 创建其他设置组, 创建文字大小设置, 创建背景设置, 创建图床管理器组_直接, 打开图床管理器对话框, 安装图床管理器依赖
from .几何设置辅助 import 安全设置几何, 安全设置最大尺寸, 安全设置最小尺寸, 获取安全缩放值, 抑制几何警告
from .创建Pollinations风格设置组 import 创建Pollinations风格设置组
from .创建Pollinations风格控件 import 创建Pollinations风格控件
from .Pollinations风格设置辅助方法 import (
    加载Pollinations风格模型选项, on_pollinations_style_enabled_changed,
    on_pollinations_style_model_changed, update_pollinations_style_ui_state,
    load_current_pollinations_style_settings, save_current_pollinations_style_settings,
    save_pollinations_style_settings, reset_pollinations_style_settings,
    get_default_positive_prompts, get_default_negative_prompts,
    import_pollinations_style_settings, export_pollinations_style_settings
)
from .云端模型管理方法 import _初始化Pollinations模型列表, 刷新Pollinations模型列表, open_pollinations_model_manager
from .获取Pollinations风格提示词 import (
    获取Pollinations风格提示词, 应用Pollinations风格提示词, 获取Pollinations反面提示词,
    检查Pollinations风格设置, 获取所有Pollinations风格设置, 重置Pollinations风格设置
)
from .强制修复Pollinations状态 import 强制修复Pollinations状态, 检查Pollinations状态一致性, 诊断Pollinations状态
from .创建图床管理器组 import 创建图床管理器组, 图床自动同步状态变化
from .图床管理器点击事件 import 图床管理器点击事件

def 添加所有方法到类(cls):
    """将所有拆分的方法添加到类中"""
    # 添加所有方法到类
    cls.语音分镜ui = 语音分镜ui
    cls.功能按钮 = 功能按钮
    cls.绘图基础设置 = 绘图基础设置
    cls.绘图进阶参数 = 绘图进阶参数
    cls.隐藏组件 = 隐藏组件
    cls.创建ui = 创建ui
    cls.提示 = 提示
    cls.调整图片风格 = 调整图片风格
    cls.绘图设置按钮 = 绘图设置按钮
    cls.部署云端镜像 = 部署云端镜像
    cls.海外模式点击事件 = 海外模式点击事件
    cls.刷新本地配置 = 刷新本地配置
    cls.更换模型 = 更换模型
    cls.多GPU点击事件 = 多GPU点击事件
    cls.检测系统GPU = 检测系统GPU
    cls.修改vae节点 = 修改vae节点
    cls.更换VAE = 更换VAE
    cls.调整SD参数 = 调整SD参数
    cls.特效点击事件 = 特效点击事件
    cls.视频设置按钮 = 视频设置按钮
    cls.创建绘图任务布局 = 创建绘图任务布局
    cls.下拉框点击事件 = 下拉框点击事件
    cls.推理增强点击事件 = 推理增强点击事件
    cls.图片辅助点击事件 = 图片辅助点击事件
    cls.存储工作流 = 存储工作流
    cls.删除选中工作流 = 删除选中工作流
    cls.绘图速度点击事件 = 绘图速度点击事件
    cls.浏览器绘图方案选择变化事件 = 浏览器绘图方案选择变化事件
    cls.浏览器绘图无头模式点击事件 = 浏览器绘图无头模式点击事件
    cls.浏览器绘图并发数变化事件 = 浏览器绘图并发数变化事件
    cls.SD下拉框点击事件 = SD下拉框点击事件
    cls.任务模式切换事件 = 任务模式切换事件
    cls.on_slider_change = on_slider_change
    cls.创建滑动条 = 创建滑动条
    cls.软件Key设置 = 软件Key设置
    cls.update_models_for_platform = update_models_for_platform
    cls.on_platform_changed = on_platform_changed
    cls.update_platform_status = update_platform_status
    cls.open_platform_config = open_platform_config
    cls.绘图模型按钮 = 绘图模型按钮
    cls.缩放 = 缩放
    cls.语音分镜下拉框点击事件 = 语音分镜下拉框点击事件
    cls.创建语音分镜组 = 创建语音分镜组
    cls.update_characters = update_characters
    cls.update_roles_and_styles = update_roles_and_styles
    cls.创建微软语音控件 = 创建微软语音控件
    cls.创建文视语音控件 = 创建文视语音控件
    cls.获取语音配置 = 获取语音配置
    cls.获取参考音 = 获取参考音
    cls.获取tts模型 = 获取tts模型
    cls.上传参考音频 = 上传参考音频
    cls.创建语音任务布局 = 创建语音任务布局
    cls.创建视频底部布局 = 创建视频底部布局
    cls.on_third_button_clicked = on_third_button_clicked
    cls.url_clicked = url_clicked
    cls.声音点击事件 = 声音点击事件
    cls.print_values = print_values
    cls.closeEvent = closeEvent
    cls.保存所有配置 = 保存所有配置
    cls.加载工作流选项 = 加载工作流选项
    cls.工作流选择事件 = 工作流选择事件
    cls.启动多GPU_ComfyUI服务 = 启动多GPU_ComfyUI服务
    cls.更换Pollinations模型 = 更换Pollinations模型
    cls.更换Pollinations风格 = 更换Pollinations风格
    cls.更新Pollinations显示状态 = 更新Pollinations显示状态
    cls.Pollinations点击事件 = Pollinations点击事件
    cls.创建推理模板组 = 创建推理模板组
    cls.加载推理模板选项 = 加载推理模板选项
    cls.open_template_manager = open_template_manager
    cls.on_template_changed = on_template_changed
    cls.创建翻译设置组 = 创建翻译设置组
    cls.on_translation_platform_changed = on_translation_platform_changed
    cls.加载翻译模型选项 = 加载翻译模型选项
    cls.on_translation_method_changed = on_translation_method_changed
    cls.on_translation_model_changed = on_translation_model_changed
    cls.创建智能场景匹配组 = 创建智能场景匹配组
    cls.加载场景匹配模型选项 = 加载场景匹配模型选项
    cls.on_scene_matching_enabled_changed = on_scene_matching_enabled_changed
    cls.on_scene_matching_platform_changed = on_scene_matching_platform_changed
    cls.on_scene_matching_model_changed = on_scene_matching_model_changed
    cls.select_background = select_background
    cls.clear_background = clear_background
    cls.apply_background = apply_background
    cls.update_background_combo = update_background_combo
    cls.background_combo_changed = background_combo_changed
    cls.选择背景图片 = 选择背景图片
    cls.清除背景图片 = 清除背景图片
    cls.应用背景图片 = 应用背景图片
    cls.常规设置 = 常规设置
    cls.创建简单其他设置组 = 创建简单其他设置组
    cls.创建其他设置组 = 创建其他设置组
    cls.创建文字大小设置 = 创建文字大小设置
    cls.创建背景设置 = 创建背景设置
    cls.创建图床管理器组_直接 = 创建图床管理器组_直接
    cls.打开图床管理器对话框 = 打开图床管理器对话框
    cls.安装图床管理器依赖 = 安装图床管理器依赖
    cls.安全设置几何 = 安全设置几何
    cls.安全设置最大尺寸 = 安全设置最大尺寸
    cls.安全设置最小尺寸 = 安全设置最小尺寸
    cls.获取安全缩放值 = 获取安全缩放值
    cls.抑制几何警告 = 抑制几何警告

    # Pollinations风格设置相关方法
    cls.创建Pollinations风格设置组 = 创建Pollinations风格设置组
    cls.创建Pollinations风格控件 = 创建Pollinations风格控件
    cls.加载Pollinations风格模型选项 = 加载Pollinations风格模型选项
    cls.on_pollinations_style_enabled_changed = on_pollinations_style_enabled_changed
    cls.on_pollinations_style_model_changed = on_pollinations_style_model_changed
    cls.update_pollinations_style_ui_state = update_pollinations_style_ui_state
    cls.load_current_pollinations_style_settings = load_current_pollinations_style_settings
    cls.save_current_pollinations_style_settings = save_current_pollinations_style_settings
    cls.save_pollinations_style_settings = save_pollinations_style_settings
    cls.reset_pollinations_style_settings = reset_pollinations_style_settings
    cls.get_default_positive_prompts = get_default_positive_prompts
    cls.get_default_negative_prompts = get_default_negative_prompts
    cls.import_pollinations_style_settings = import_pollinations_style_settings
    cls.export_pollinations_style_settings = export_pollinations_style_settings
    cls.获取Pollinations风格提示词 = 获取Pollinations风格提示词
    cls.应用Pollinations风格提示词 = 应用Pollinations风格提示词
    cls.获取Pollinations反面提示词 = 获取Pollinations反面提示词
    cls.检查Pollinations风格设置 = 检查Pollinations风格设置
    cls.获取所有Pollinations风格设置 = 获取所有Pollinations风格设置
    cls.重置Pollinations风格设置 = 重置Pollinations风格设置
    cls.强制修复Pollinations状态 = 强制修复Pollinations状态
    cls.检查Pollinations状态一致性 = 检查Pollinations状态一致性
    cls.诊断Pollinations状态 = 诊断Pollinations状态

    # 云端模型管理方法
    cls._初始化Pollinations模型列表 = _初始化Pollinations模型列表
    cls.刷新Pollinations模型列表 = 刷新Pollinations模型列表
    cls.open_pollinations_model_manager = open_pollinations_model_manager

    # Pollinations API密钥方法
    cls.更新Pollinations密钥 = 更新Pollinations密钥
    cls.切换Pollinations密钥显示 = 切换Pollinations密钥显示

    # 图床管理器方法
    cls.创建图床管理器组 = 创建图床管理器组
    cls.图床自动同步状态变化 = 图床自动同步状态变化
    cls.图床管理器点击事件 = 图床管理器点击事件

    return cls
