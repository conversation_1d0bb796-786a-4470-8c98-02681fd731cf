#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
云端参考图专用工作流 - 专门用于云端模型的参考图生成
当启用云端模型时，参考图生成将自动切换到此工作流
"""

import os
import time
import shutil
from pyz.任务运行文件 import A0_config

class 云端参考图专用工作流:
    """云端参考图专用工作流生成器"""

    def __init__(self):
        self.config = A0_config.config

    def 检测是否使用云端模型(self):
        """检测当前是否启用了云端模型"""
        return self.config.get('使用Pollinations', False)

    def 获取软件设置的正面提示词(self):
        """获取软件设置界面中的正面提示词"""
        try:
            # 首先尝试从Pollinations风格设置中获取
            from pyz.UI文件.软件设置UI_methods.获取Pollinations风格提示词 import 获取Pollinations风格提示词

            # 获取当前云端模型的正面提示词
            云端模型 = self.config.get('pollinations_model', 'flux')
            正面词 = 获取Pollinations风格提示词(云端模型, 'positive')

            if 正面词:
                print(f"   📝 使用Pollinations风格设置的正面提示词 (模型: {云端模型})")
                return 正面词

        except Exception as e:
            print(f"   ⚠️  获取Pollinations风格提示词失败: {e}")

        # 回退到传统配置键名
        正面词 = (self.config.get('正提示词', '') or
                self.config.get('正面词', '') or
                '').strip()

        if not 正面词:
            # 使用默认的高质量正面提示词
            正面词 = 'masterpiece, best quality, ultra detailed, 8k resolution, photorealistic, professional photography, perfect lighting, vibrant colors, sharp focus, high contrast, detailed textures, cinematic composition, award winning photography'
            print(f"   📝 使用默认正面提示词")
        else:
            print(f"   📝 使用配置文件中的正面提示词")

        return 正面词

    def 获取软件设置的反面提示词(self):
        """获取软件设置界面中的反面提示词"""
        try:
            # 首先尝试从Pollinations风格设置中获取
            from pyz.UI文件.软件设置UI_methods.获取Pollinations风格提示词 import 获取Pollinations风格提示词

            # 获取当前云端模型的反面提示词
            云端模型 = self.config.get('pollinations_model', 'flux')
            反面词 = 获取Pollinations风格提示词(云端模型, 'negative')

            if 反面词:
                print(f"   📝 使用Pollinations风格设置的反面提示词 (模型: {云端模型})")
                return 反面词

        except Exception as e:
            print(f"   ⚠️  获取Pollinations风格提示词失败: {e}")

        # 回退到传统配置键名
        反面词 = (self.config.get('反提示词', '') or
                self.config.get('反面词', '') or
                '').strip()

        if not 反面词:
            # 使用增强的默认反面提示词 - 与本地版本保持一致
            反面词 = ('watermark, signature, text overlay, logo, copyright mark, artist signature, '
                    'multiple hands, extra hands, bad hands, malformed hands, fused fingers, extra fingers, missing fingers, '
                    'blurry, blur, out of focus, soft focus, motion blur, '
                    'missing limbs, extra limbs, extra arms, extra legs, amputee, dismembered, '
                    'deformed anatomy, bad anatomy, distorted anatomy, malformed body, twisted limbs, '
                    'low quality, worst quality, bad quality, poor quality, low resolution, '
                    'multiple people, crowd, background clutter, messy background, '
                    'deformed face, asymmetrical face, distorted features, malformed features, '
                    'inconsistent character, different person, face swap, '
                    'cropped, cut off, partial body, incomplete figure, '
                    'artifacts, compression artifacts, jpeg artifacts, noise, grain, '
                    'overexposed, underexposed, oversaturated, undersaturated, '
                    'cartoon, anime, illustration, painting, sketch, drawing, '
                    'nsfw, nude, sexual content')
            print(f"   📝 使用增强的默认反面提示词")
        else:
            print(f"   📝 使用配置文件中的反面提示词")

        return 反面词

    def 获取通用质量固定词(self):
        """获取通用质量固定词，放在提示词最前面"""
        return "masterpiece, best quality, ultra detailed, 8k resolution, high resolution, extremely detailed, photorealistic, professional photography, perfect lighting, vibrant colors, sharp focus, high contrast, detailed textures, cinematic composition, award winning photography, studio lighting, professional portrait, crystal clear, ultra sharp, perfect exposure, color grading, depth of field, bokeh effect, professional quality, commercial grade, gallery worthy, museum quality, fine art photography, exceptional detail, flawless composition, perfect clarity, stunning visual, breathtaking quality, world class, premium quality, top tier, elite level, superior craftsmanship, artistic excellence"

    def 转换负面词为not格式(self, 负面词):
        """
        将负面提示词转换为not格式

        参数:
            负面词: 原始负面提示词字符串

        返回:
            转换后的not格式字符串
        """
        if not 负面词 or not 负面词.strip():
            return ""

        # 分割负面词
        词汇列表 = [词.strip() for 词 in 负面词.split(',') if 词.strip()]

        # 转换为not格式
        not格式词汇 = [f"not {词}" for 词 in 词汇列表]

        # 用逗号连接
        return ", ".join(not格式词汇)

    def 优化云端参考图提示词(self, 提示词, 角色名=""):
        """
        优化云端参考图提示词 - 强制全身像正面照
        使用软件设置中的正面提示词，并添加角色相关增强
        注意：提示词在传入前已经处理过翻译，这里直接使用
        """
        # 1. 通用质量固定词（最前面）
        通用质量词 = self.获取通用质量固定词()

        # 2. 获取软件设置中的正面提示词
        软件正面词 = self.获取软件设置的正面提示词()

        if not 提示词:
            提示词 = "beautiful character"

        # 3. 全身像正面照强制要求词汇（云端优先级最高）
        全身正面要求词 = "full body shot, full body portrait, whole body, complete figure, front view, frontal view, facing camera, facing forward, standing pose, front facing, looking at camera"

        # 4. 角色相关增强
        if 角色名:
            角色增强词 = f"character design of {角色名}, "
        else:
            角色增强词 = "character design, "

        # 5. 获取负面提示词并转换为not格式
        原始负面词 = self.获取软件设置的反面提示词()
        not格式负面词 = self.转换负面词为not格式(原始负面词)

        # 6. 组合优化提示词：通用质量词 + 全身正面要求 + 角色增强 + 用户提示词 + 软件设置的正面词 + not格式负面词
        提示词部分 = [通用质量词, 全身正面要求词, f"{角色增强词}{提示词}", 软件正面词]

        # 添加not格式负面词到最后
        if not格式负面词:
            提示词部分.append(not格式负面词)

        # 组合所有部分
        优化提示词 = ", ".join(提示词部分)

        # 清理重复词汇
        词汇列表 = [词.strip() for 词 in 优化提示词.split(',')]
        去重词汇 = []
        for 词 in 词汇列表:
            if 词 and 词 not in 去重词汇:
                去重词汇.append(词)

        print(f"✅ 云端参考图提示词优化完成，包含not格式负面词")
        return ', '.join(去重词汇)


    def 生成云端参考图工作流(self, 提示词="", 角色名=""):
        """
        生成云端参考图专用工作流配置
        注意：负面提示词已经转换为not格式并添加到主提示词中，不再使用negative参数
        """
        print("☁️  生成云端参考图专用工作流")

        # 优化提示词（已包含not格式的负面词）
        优化提示词 = self.优化云端参考图提示词(提示词, 角色名)

        # 获取软件设置中的云端模型配置
        云端模型 = self.config.get('pollinations_model', 'flux')
        # 使用适合全身像的尺寸比例，而不是软件设置中的尺寸
        图片宽度 = 768  # 适合全身像的宽度
        图片高度 = 1152  # 适合全身像的高度（3:2比例）
        云端风格 = self.config.get('pollinations_style', '')

        # 使用软件设置中的云端配置，针对参考图进行微调
        云端配置 = {
            "增强": True,
            "安全模式": True,
            "风格": 云端风格,  # 使用软件设置中的风格
            "CFG系数": 7.5,   # 参考图专用CFG，适中的值
            "采样步数": 30,   # 参考图需要更多步数保证质量
            "重绘幅度": 0.1,  # 较低的重绘幅度保持一致性
            "质量": "high"    # 高质量模式
        }

        print(f"☁️  云端模型: {云端模型} (来自软件设置)")
        print(f"📐 图片尺寸: {图片宽度}x{图片高度} (来自软件设置)")
        print(f"🎨 云端风格: {云端风格} (来自软件设置)")
        print(f"✨ 优化提示词: {优化提示词}")
        print(f"🎭 角色名: {角色名}")
        print(f"⚙️  云端配置: {云端配置}")
        print(f"📝 负面提示词已转换为not格式并添加到主提示词中")

        # 返回云端工作流配置
        return {
            "工作流类型": "云端参考图",
            "云端模型": 云端模型,
            "提示词": 优化提示词,
            "图片宽度": 图片宽度,
            "图片高度": 图片高度,
            "角色名": 角色名,
            "参考图专用": True,
            "云端配置": 云端配置
        }

    def 执行云端参考图生成(self, 工作流配置, 项目路径=""):
        """
        执行云端参考图生成

        Args:
            工作流配置: 云端工作流配置
            项目路径: 项目保存路径

        Returns:
            生成结果: {'success': bool, 'path': str, 'seed': str, 'message': str}
        """
        try:
            print("☁️  开始执行云端参考图生成")

            # 导入云端生成函数
            from pyz.工作流.A7_Pollinations工作流 import 生成Pollinations图片

            # 获取参数
            提示词 = 工作流配置.get('提示词', '')
            角色名 = 工作流配置.get('角色名', '')
            图片宽度 = 工作流配置.get('图片宽度', 800)
            图片高度 = 工作流配置.get('图片高度', 600)
            云端模型 = 工作流配置.get('云端模型', 'flux')
            云端配置 = 工作流配置.get('云端配置', {})

            print(f"☁️  执行参数:")
            print(f"   模型: {云端模型}")
            print(f"   尺寸: {图片宽度}x{图片高度}")
            print(f"   角色: {角色名}")
            print(f"   提示词: {提示词[:100]}...")
            print(f"   注意: 负面词已转换为not格式并包含在主提示词中")

            # 调用云端生成（不传递反面词和角色名，因为参考图是从零开始生成的）
            生成结果 = 生成Pollinations图片(
                提示词=提示词,
                模型名称=云端模型,
                图片宽度=图片宽度,
                图片高度=图片高度,
                图片数量=1,  # 参考图只生成一张
                项目路径=项目路径,
                当前编号=1,
                已有数量=0,
                备选图片组=None,
                反面词="",  # 不使用反面词，因为已经转换为not格式并添加到主提示词中
                增强=云端配置.get('增强', True),
                安全模式=云端配置.get('安全模式', True),
                风格=云端配置.get('风格', ''),
                种子=None,  # 让云端随机生成
                CFG系数=云端配置.get('CFG系数', 7.5),
                采样步数=云端配置.get('采样步数', 30),
                重绘幅度=云端配置.get('重绘幅度', 0.1),
                角色名=""  # 参考图生成不需要角色名，避免获取参考图URL
            )

            if 生成结果 and len(生成结果) > 0:
                # 获取生成的图片信息
                图片信息 = 生成结果[0]
                原始路径 = 图片信息.get('path', '')
                种子值 = 图片信息.get('seep', '')  # 修复：使用正确的键名 'seep'

                # print(f"🔍 调试 - 云端参考图工作流图片信息: {图片信息}")
                # print(f"🌱 云端参考图工作流种子值: {种子值}")

                if 原始路径 and os.path.exists(原始路径):
                    # 移动到角色参考图片文件夹
                    最终路径 = self.保存参考图到角色文件夹(原始路径, 角色名, 种子值, 项目路径)

                    if 最终路径:
                        return {
                            'success': True,
                            'path': 最终路径,
                            'seed': 种子值,
                            'message': '云端参考图生成成功'
                        }
                    else:
                        return {
                            'success': False,
                            'path': '',
                            'seed': '',
                            'message': '参考图保存失败'
                        }
                else:
                    return {
                        'success': False,
                        'path': '',
                        'seed': '',
                        'message': '云端生成的图片文件不存在'
                    }
            else:
                return {
                    'success': False,
                    'path': '',
                    'seed': '',
                    'message': '云端生成失败'
                }

        except Exception as e:
            print(f"❌ 云端参考图生成异常: {e}")
            import traceback
            traceback.print_exc()
            return {
                'success': False,
                'path': '',
                'seed': '',
                'message': f'生成异常: {str(e)}'
            }

    def 保存参考图到角色文件夹(self, 原始路径, 角色名, 种子值, 项目路径):
        """
        将生成的参考图保存到角色专用文件夹

        Args:
            原始路径: 原始图片路径
            角色名: 角色名称
            种子值: 生成种子
            项目路径: 项目根路径

        Returns:
            最终保存路径
        """
        try:
            if not 项目路径:
                print("❌ 项目路径为空，无法保存参考图")
                return None

            # 创建角色参考图片文件夹
            参考图文件夹 = os.path.join(项目路径, '角色参考图片', 角色名)
            os.makedirs(参考图文件夹, exist_ok=True)

            # 生成新的文件名 - 统一使用seed格式
            if 种子值:
                新文件名 = f"{角色名}_seed{种子值}.png"
                # print(f"✅ 使用种子值命名: {新文件名}")
            else:
                import time
                新文件名 = f"{角色名}_cloud_{int(time.time())}.png"
                # print(f"⚠️ 未获取到种子值，使用时间戳命名: {新文件名}")

            新文件路径 = os.path.join(参考图文件夹, 新文件名)

            # 移动文件
            shutil.move(原始路径, 新文件路径)
            print(f"📸 云端参考图已保存到: {新文件路径}")

            return 新文件路径

        except Exception as e:
            print(f"❌ 保存参考图失败: {e}")
            return None

# 便捷函数
def 获取云端参考图专用工作流(提示词="", 角色名=""):
    """
    获取云端参考图专用工作流

    Args:
        提示词: 角色描述提示词
        角色名: 角色名称

    Returns:
        dict: 云端工作流配置
    """
    工作流生成器 = 云端参考图专用工作流()
    return 工作流生成器.生成云端参考图工作流(提示词, 角色名)

def 执行云端参考图生成任务(提示词="", 角色名="", 项目路径=""):
    """
    执行云端参考图生成任务

    Args:
        提示词: 角色描述提示词
        角色名: 角色名称
        项目路径: 项目保存路径

    Returns:
        生成结果字典
    """
    工作流生成器 = 云端参考图专用工作流()
    工作流配置 = 工作流生成器.生成云端参考图工作流(提示词, 角色名)
    return 工作流生成器.执行云端参考图生成(工作流配置, 项目路径)

# 导出函数
__all__ = [
    '云端参考图专用工作流',
    '获取云端参考图专用工作流',
    '执行云端参考图生成任务'
]
