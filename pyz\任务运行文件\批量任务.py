# Decompiled with PyLingual (https://pylingual.io)
# Internal filename: 任务运行文件\批量任务.py
# Bytecode version: 3.10.0rc2 (3439)
# Source timestamp: 1970-01-01 00:00:00 UTC (0)

import gc
import json
import math
import os as jduvudu312us_usjlq
import random
import re
import threading
import time
from tkinter import messagebox
import requests
import winsound
from PIL import Image
from PyQt5 import QtCore
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QPixmap, QIcon
from PyQt5.QtWidgets import QTableWidgetItem
from pydub import AudioSegment
from pyz.任务运行文件 import A5_加载本地配置, A2_网络请求, A0_config
from pyz.任务运行文件.A0_通用函数 import 获取SD列队, 生成表格数据, upload_file
from pyz.任务运行文件.A17_视频转绘 import 视频转绘, 合成长视频
from pyz.任务运行文件.A20_ai数字人 import xuniren
from pyz.任务运行文件.A23_推理增强 import dify_推理
from pyz.任务运行文件.A30_视频分镜 import 视频分割
from pyz.任务运行文件.A3_jiamijiemi import 加密数据
from pyz.任务运行文件.A5_关键词反推 import 反推关键词_wb
from pyz.任务运行文件.A5_推理关键词 import 关键词推理
from pyz.任务运行文件.云端SD_MJ import SDAPI
from pyz.任务运行文件.文视GPT推理 import 智普AI翻译
from pyz.任务运行文件.视频抽帧 import 视频抽帧, 图片去重
from pyz.生成视频 import A13_audio_task
from pyz.生成视频.A6_draft_content import 生成剪映文件
from pyz.生成视频.A7_draft_content_ys import 生成剪映文件_原声

def test_time(func1):
    def train(self):
        start_time = time.time()
        res = func1(self)
        end_time = time.time()
        print('用时:', int(end_time - start_time), '秒')
        return res
    return train

class Runthread(QtCore.QThread):
    _signal = QtCore.pyqtSignal(tuple)

    def __init__(self, main_win):
        super(Runthread, self).__init__()
        self.mood = main_win
        self._signal.connect(self.mood.call_backlog)
        self.threads = []
    
    def clean_punctuation(self, text):
        """
        严谨的标点符号清理和规范化
        
        Args:
            text: 需要清理的文本
            
        Returns:
            清理后的文本
        """
        if not text or not isinstance(text, str):
            return text
            
        # 去除首尾空白
        text = text.strip()
        
        # 移除多余的引号和括号
        text = re.sub(r'^["\'\'\"]+|["\'\'\"]+$', '', text)
        
        # 规范化逗号：移除逗号前的空格，确保逗号后有且仅有一个空格
        text = re.sub(r'\s*,\s*', ', ', text)
        
        # 移除多余的连续空格
        text = re.sub(r'\s+', ' ', text)
        
        # 移除句末多余的逗号和空格
        text = re.sub(r',\s*$', '', text)
        
        # 移除开头的逗号和空格
        text = re.sub(r'^,\s*', '', text)
        
        # 移除其他不必要的标点符号组合
        text = re.sub(r'[,，]\s*[,，]', ', ', text)  # 连续逗号
        text = re.sub(r'\s*[。.]+\s*$', '', text)  # 句末句号
        
        return text.strip()

    def 合成语音(self, 表格数据):
        self.mood.yu = False
        A13_audio_task.语音合成(self, 表格数据, is_batch=True)

    def 视频分镜(self):
        try:
            self.mood.任务总数 = len(self.mood.项目配置['data']) * 2
            self.mood.当前进度 = 0
            if 视频抽帧(self.mood, self):
                if not self.mood.导入剪映:
                    图片去重(self.mood, self)
                if not jduvudu312us_usjlq.path.exists(A0_config.改文件名(self.mood.项目配置['音频路径'])):
                    video = AudioSegment.from_file(A0_config.改文件名(A0_config.改文件名(self.mood.项目配置['视频路径'])), format='mp4')
                    video.export(A0_config.改文件名(self.mood.项目配置['音频路径']), format='wav')
                self.mood.running = False
                self.mood.zhuxian = False
                self._signal.emit(('视频分镜', '表格数据', '视频分镜'))
            else:  # inserted
                self.mood.stop_run = True
        except Exception as e:
            print(self.mood.当前任务, '处理异常', e)
            提示 = f'{self.mood.当前任务}处理异常: {e}'
            self._signal.emit((提示, '弹窗', '弹窗'))

    def 改写文案(self, 表格数据, row_data):
        要求 = f"\n## Features\n\n改写要求:\n1. 帮我改写上面[改写内容],要求意思相同,语义通顺,\n2. 只需要把改写后的结果回答给我,不要加其他任何的额外描述、注释或者说明\n\n## Rules\n【改写后文本】不要加其他任何的额外描述、注释或者说明\n\n## Examples\n用户: \n感受着她的心跳和体温逐渐上升,脸颊也慢慢变红\nAI: \n【改写后文本】我能清楚的感受到，她心跳和体温都在逐渐上升，脸颊也逐渐变得彤红\n\n## Workflow\n1.帮我改写上面[改写内容],要求意思相同,语义通顺,\n2.将改写后的内容在【改写后文本】输出。\n\n## Initialization\n请严格遵守<Rules>，严格执行<Workflow> ，下面是[改写内容]:\n{表格数据['原内容']}\n"
        for i in range(5):
            if not self.mood.running:
                break
                return
            if A0_config.config['海外模式'] and (not A0_config.NOVIP):
                回答 = 智普AI翻译(self.mood, row_data['txt'])
            else:  # inserted
                回答 = A2_网络请求.网络请求(self.mood, 要求, '改文', self).关键词推理()
            if 回答 and 'unk' not in 回答:
                for data in 回答.split('\n'):
                    if '【改写后文本】' in data:
                        回答 = data
                替换词 = ['。', '\"', '\'', '【改写后文本】']
                for 词 in 替换词:
                    回答 = 回答.replace(词, '')
                print(表格数据['原内容'], '改写后', 回答)
                row_data['txtxiugai'] = 回答
                表格数据['改写内容'] = 回答
                self.mood.保存配置()
                self._signal.emit((回答, 表格数据, '文案改写'))
                return
            else:  # inserted
                print('网络请求失败')
        else:  # inserted
            self.mood.当前进度 = self.mood.当前进度 + 1

    def 翻译文案(self, 表格数据, row_data):
        for i in range(5):
            if not self.mood.running:
                return
            中文内容 = 表格数据['中文内容']
            英文文案 = 表格数据['英文内容']
            成功 = True
            if bool(re.search('[\\u4e00-\\u9fa5]', 英文文案)):
                回答 = 智普AI翻译(self.mood, 英文文案)
                if 回答:
                    表格数据['英文内容'] = 回答
                    row_data['txt_en'] = 回答
                    self._signal.emit((回答, 表格数据, '更新进度'))
                else:  # inserted
                    print('网络请求失败')
                    成功 = False
            if not bool(re.search('[\\u4e00-\\u9fa5]', 中文内容)):
                回答 = 智普AI翻译(self.mood, 中文内容, yuyan='中文')
                print(回答)
                if 回答:
                    替换词 = ['#', '*', '-', '_']
                    for 词 in 替换词:
                        回答 = 回答.replace(词, '')
                    表格数据['中文内容'] = 回答
                    row_data['txt'] = 回答
                    self._signal.emit((回答, 表格数据, '翻译文案'))
                else:  # inserted
                    print('网络请求失败')
                    成功 = False
            if 成功:
                self.mood.当前进度 = self.mood.当前进度 + 1
                self.mood.保存配置()
                return
            print('网络请求失败')

    def 批量改写(self):
        try:
            列表数据 = self.mood.项目配置['data']
            threads = []
            for row, row_data in enumerate(列表数据):
                if not self.mood.running:
                    print('改写任务停止')
                    self.mood.停止 = True
                    break
                表格数据 = {'当前行': row, '原内容': row_data['txt'], '改写内容': row_data['txtxiugai']}
                if 表格数据['原内容']!= 表格数据['改写内容']:
                    self._signal.emit(('改写完成', 表格数据, '文案改写'))
                    continue
                self._signal.emit(('正在改写', 表格数据, '文案改写'))
                if A0_config.NOVIP:
                    self.改写文案(表格数据, row_data)
                else:  # inserted
                    thread = threading.Thread(target=self.改写文案, args=(表格数据, row_data))
                    threads.append(thread)
                    thread.start()
                    time.sleep(1)
            for thread in threads:
                thread.join()
            print('文案改写完成')
        except Exception as e:
            print(self.mood.当前任务, '处理异常', e)
            提示 = f'{self.mood.当前任务}处理异常: {e}'
            self._signal.emit((提示, '弹窗', '弹窗'))

    def 批量翻译(self):
        try:
            列表数据 = self.mood.项目配置['data']
            threads = []
            for row, row_data in enumerate(列表数据):
                if not self.mood.running:
                    print('改写任务停止')
                    self.mood.停止 = True
                    break
                txt = row_data['txt']
                txt_en = row_data.get('txt_en')
                表格数据 = {'当前行': row, '中文内容': txt, '英文内容': txt_en}
                if bool(re.search('[\\u4e00-\\u9fa5]', txt)) and (not bool(re.search('[\\u4e00-\\u9fa5]', txt_en))):
                    continue
                if A0_config.NOVIP:
                    self.翻译文案(表格数据, row_data)
                else:  # inserted
                    thread = threading.Thread(target=self.翻译文案, args=(表格数据, row_data))
                    threads.append(thread)
                    thread.start()
                    time.sleep(1)
            for thread in threads:
                thread.join()
            print('海外模式文案翻译完成')
        except Exception as e:
            print(self.mood.当前任务, '处理异常', e)
            提示 = f'{self.mood.当前任务}处理异常: {e}'
            self._signal.emit((提示, '弹窗', '弹窗'))

    def 批量推理(self):
        from pyz.任务运行文件.A18_comfyui import Comfyui
        try:
            from pyz.任务运行文件.A18_comfyui import Comfyui
            列表数据 = self.mood.项目配置['data']
            if self.mood.任务模式 == '原创模式':
                关键词推理(self.mood, self, 列表数据).获取表格中故事内容()
                return
            if A0_config.config['SD版本'] == 'WebUI':
                for row, row_data in enumerate(self.mood.项目配置['data']):
                    if not self.mood.running:
                        print('推理任务停止')
                        self.mood.停止 = True
                        break
                    if row_data['prompt']!= '':
                        self.mood.当前进度 = self.mood.当前进度 + 1
                        continue
                    表格数据 = {'当前行': row, '原图片': A0_config.改文件名(self.mood.项目配置['data'][row]['img_yuantu'])}
                    if jduvudu312us_usjlq.path.exists(表格数据['原图片']):
                        关键词 = f"{Comfyui(self, self).反推关键词_fy(表格数据['原图片'])}"
                        print(row, ' 反推关键词_wb:', 关键词)
                        if not 关键词:
                            messagebox.showinfo('提示', f'第{row + 1}行图片关无法反推关键词,请合并数据或手动添加3个以上关键词')
                            关键词 = 'High quality, ultra-high definition, high-definition'
                        row_data['prompt'] = 关键词
                        row_data['prompt_cn'] = 关键词  # 同时设置中文提示词
                        self._signal.emit((关键词, 表格数据, '关键词'))
                    else:  # inserted
                        print(表格数据['原图片'], '图片不存在')
                    self.mood.当前进度 = self.mood.当前进度 + 1
            else:  # inserted
                for row, row_data in enumerate(self.mood.项目配置['data']):
                    if not self.mood.running:
                        print('推理任务停止')
                        self.mood.停止 = True
                        break
                    if row_data['prompt']!= '':
                        self.mood.当前进度 = self.mood.当前进度 + 1
                        continue
                    表格数据 = {'当前行': row, '原图片': A0_config.改文件名(self.mood.项目配置['data'][row]['img_yuantu'])}
                    if jduvudu312us_usjlq.path.exists(表格数据['原图片']):
                        关键词 = f"{Comfyui(self, self).反推关键词_fy(表格数据['原图片'])}"
                        关键词 = 关键词.split('\n')[0]
                        print(row, ' 反推关键词_wb:', 关键词)
                        if not 关键词:
                            关键词 = 'High quality, ultra-high definition, high-definition'
                        row_data['prompt'] = 关键词
                        row_data['prompt_cn'] = 关键词  # 同时设置中文提示词
                        self._signal.emit((关键词, 表格数据, '关键词'))
                    else:  # inserted
                        print(表格数据['原图片'], '图片不存在')
                    self.mood.当前进度 = self.mood.当前进度 + 1
            self.mood.保存配置()
        except Exception as e:
            print(self.mood.当前任务, '处理异常', e)
            提示 = f'{self.mood.当前任务}处理异常: {e}'
            self._signal.emit((提示, '弹窗', '弹窗'))

    def 批量推理_视频_列队(self, 表格数据, row_data):
        try:
            imgid = upload_file(row_data['img']['path'])
            回答 = dify_推理(text='', num=1, character='性别标识', mod=self, mode='视频推理', tl_text='单行内容', img=imgid)
            if 回答:
                print('视频关键词推理成功', 回答)
                row_data['prompt_video'] = 回答
                cleaned_answer = self.clean_punctuation(回答)
                self._signal.emit((cleaned_answer + ', ', 表格数据, '视频关键词'))
                self.mood.保存配置()
            else:  # inserted
                print('视频关键词推理失败')
                self._signal.emit(('推理失败', 表格数据, '视频关键词'))
        except Exception as e:
            print(self.mood.当前任务, '处理异常', e)
            提示 = f'{self.mood.当前任务}处理异常: {e}'
            self._signal.emit((提示, '弹窗', '弹窗'))
        finally:  # inserted
            self.mood.当前进度 += 1

    def 批量推理_视频(self):
        self.threads = []
        try:
            列表数据 = self.mood.项目配置['data']
            for row, row_data in enumerate(列表数据):
                if not self.mood.running:
                    print('视频关键词推理停止')
                    self.mood.停止 = True
                    break
                表格数据 = {'当前行': row}
                if A0_config.改文件名(row_data['img']['path'])!= '':
                    if jduvudu312us_usjlq.path.exists(A0_config.改文件名(row_data['img']['path'])) and (not row_data.get('prompt_video')):
                        self._signal.emit(('正在推理', 表格数据, '视频关键词'))
                        thread = threading.Thread(target=self.批量推理_视频_列队, args=(表格数据, row_data))
                        self.threads.append(thread)
                        thread.start()
                        time.sleep(3)
                    else:  # inserted
                        if row_data.get('prompt_video'):
                            print(row_data['img']['path'], '关键词已存在')
                            self.mood.当前进度 += 1
                        else:  # inserted
                            print(row_data['img']['path'], '图片不存在')
                            self.mood.当前进度 += 1
                            self._signal.emit(('推理失败', 表格数据, '视频关键词'))
                else:  # inserted
                    print(row_data['img']['path'], '图片不存在')
                    self.mood.当前进度 += 1
                    self._signal.emit(('推理失败', 表格数据, '视频关键词'))
        except Exception as e:
            print(self.mood.当前任务, '处理异常', e)
            提示 = f'{self.mood.当前任务}处理异常: {e}'
            self._signal.emit((提示, '弹窗', '弹窗'))
        finally:  # inserted
            for thread in self.threads:
                thread.join()

    def 批量放大(self):
        from pyz.任务运行文件.A18_comfyui import Comfyui
        from pyz.任务运行文件.A32_waifu2x放大 import waifu2x_upscale_single
        self.threads = []
        self.排队数量 = 0
        try:
            列表数据 = self.mood.项目配置['data']
            for row, row_data in enumerate(列表数据):
                列队数 = A0_config.config['SDurl数量']
                if not self.mood.running:
                    print('放大任务停止')
                    self.mood.停止 = True
                    break
                if A0_config.改文件名(row_data['img']['path'])!= '' and jduvudu312us_usjlq.path.exists(A0_config.改文件名(row_data['img']['path'])):
                    image = Image.open(A0_config.改文件名(row_data['img']['path']))
                    width, height = image.size
                    image.close()
                    if width >= int(A0_config.config['图片长度']) * int(A0_config.config['修复放大倍数']):
                        self.mood.当前进度 = self.mood.当前进度 + 1
                        continue
                    表格数据 = 生成表格数据(self, row_data, row)
                    self._signal.emit(('正在放大', 表格数据, '放大图片'))

                    # 新增：Waifu2x免费在线放大
                    if A0_config.config['放大模式'] == 'Waifu2x放大':
                        print(f"🎨 使用Waifu2x免费在线放大 - 第{row+1}张")
                        放大返回 = waifu2x_upscale_single(表格数据['图片路径'], int(A0_config.config['修复放大倍数']))
                        self.处理放大返回(放大返回, row_data, 表格数据)
                    elif A0_config.config['SD版本'] == 'WebUI':
                        if A0_config.config['放大模式'] == '重绘放大':
                            放大返回 = self.mood.高清放大(表格数据, width, height, self)
                        else:  # inserted
                            放大返回 = self.mood.图片放大(表格数据['图片路径'], int(A0_config.config['修复放大倍数']))
                        self.处理放大返回(放大返回, row_data, 表格数据)
                    else:  # inserted
                        if A0_config.config['放大模式'] == '重绘放大':
                            while self.排队数量 > 列队数 * 2 and self.mood.running:
                                time.sleep(1)
                            url = 获取SD列队()
                            print(url, 表格数据.get('当前编号'))
                            thread = threading.Thread(target=self.多线程放大, args=(表格数据, row_data), kwargs={'url': url})
                            self.threads.append(thread)
                            thread.start()
                            self.排队数量 += 1
                            time.sleep(2)
                        else:  # inserted
                            放大返回 = Comfyui(self.mood).图片直接放大(表格数据['图片路径'], width, height)
                            self.处理放大返回(放大返回, row_data, 表格数据)
                else:  # inserted
                    self.mood.当前进度 = self.mood.当前进度 + 1
        except Exception as e:
            print(self.mood.当前任务, '处理异常', e)
            提示 = f'{self.mood.当前任务}处理异常: {e}'
            self._signal.emit((提示, '弹窗', '弹窗'))
        finally:  # inserted
            for thread in self.threads:
                thread.join()

    def 处理放大返回(self, 放大返回, row_data, 表格数据):
        if 放大返回:
            row_data['img']['path'] = 放大返回
            没有放大 = True
            for data in row_data['img_beixuan']:
                if '高清' in data['path']:
                    没有放大 = False
            if 没有放大:
                row_data['img_beixuan'].append({'path': 放大返回, 'seep': 表格数据['随机种子']})
            表格数据['图片路径'] = 放大返回
            self.mood.保存配置()
            self._signal.emit(('放大完成', 表格数据, '图片'))
        else:  # inserted
            self._signal.emit(('放大失败', 表格数据, '图片'))
        self.mood.当前进度 = self.mood.当前进度 + 1

    def 多线程放大(self, 表格数据, row_data, url=None):
        from pyz.任务运行文件.A18_comfyui import Comfyui
        from pyz.任务运行文件.A18_comfyui import Comfyui
        api = SDAPI(self.mood)
        返回数据 = api.图片放大(row_data['图片路径'], int(A0_config.config['修复放大倍数']))
        if not 返回数据:
            返回数据 = Comfyui(self.mood).图片重绘放大(row_data)
        self.处理放大返回(返回数据, row_data, 表格数据)

    def 批量绘图(self):
        # 检查是否启用浏览器绘图方案A
        try:
            from pyz.任务运行文件.浏览器绘图方案.interceptor import 批量绘图拦截器
            拦截, 信息 = 批量绘图拦截器(self.mood)
            if 拦截:
                print(f"🎨 {信息}")
                # 如果拦截成功，不执行原有批量绘图流程
                return
            else:
                print(f"🔄 {信息}")
        except Exception as e:
            print(f"❌ 浏览器绘图方案A拦截器检查失败: {e}")

        # 检查是否启用浏览器绘图方案B
        try:
            from pyz.任务运行文件.浏览器绘图方案B.interceptor import 批量绘图拦截器 as 批量绘图拦截器B
            拦截B, 信息B = 批量绘图拦截器B(self.mood)
            if 拦截B:
                print(f"🎨 {信息B}")
                # 如果拦截成功，不执行原有批量绘图流程
                return
            else:
                print(f"🔄 {信息B}")
        except Exception as e:
            print(f"❌ 浏览器绘图方案B拦截器检查失败: {e}")

        # 检查是否启用浏览器绘图方案C
        try:
            from pyz.任务运行文件.浏览器绘图方案C.interceptor import 拦截绘图流程C
            # 构建行号列表（批量绘图需要所有行）
            总行数 = len(self.mood.项目配置.get('data', []))
            行号列表 = list(range(总行数))

            拦截C, 信息C = 拦截绘图流程C(self.mood, 'batch', rows=行号列表)
            if 拦截C:
                print(f"🎨 {信息C}")
                # 如果拦截成功，不执行原有批量绘图流程
                return
            else:
                print(f"🔄 {信息C}")
        except Exception as e:
            print(f"❌ 浏览器绘图方案C拦截器检查失败: {e}")

        self.threads = []
        self.排队数量 = 0
        if 'MJ' in A0_config.config['SD版本'] or 'GPT' in A0_config.config['SD版本']:
            dis_json = A0_config.config['MJ请求参数']
            authorization = A0_config.config['MJ频道密钥']
            if not dis_json or not authorization:
                提示 = '请先配置MJ密钥和请求参数, 不会操作点击左侧使用教程--常见问题'
                print(提示)
                self._signal.emit((提示, '弹窗', '弹窗'))
                return
        try:
            if '极速模型' in A0_config.config['当前选中模型'] and A0_config.config['NunchakuFluxDiTLoader'] and (A0_config.config['SD版本'] == 'ComfyUI'):
                self.mood.任务总数 += len(self.mood.项目配置['data']) * (int(A0_config.config['每张图片数量']) - 1)
                循环次数 = int(A0_config.config['每张图片数量'])
            else:
                循环次数 = 1
            for row, row_data in enumerate(self.mood.项目配置['data']):
                列队数 = A0_config.config['SDurl数量']
                try:
                    if 'img' not in row_data:
                        row_data['img'] = {'path': '', 'seep': random.randint(1000000000, 9999999999)}
                    elif 'seep' not in row_data['img']:
                        row_data['img']['seep'] = random.randint(1000000000, 9999999999)
                    if 'img_beixuan' not in row_data:
                        row_data['img_beixuan'] = []

                    if not self.mood.running:
                        print('画图任务停止')
                        self.mood.停止 = True
                        return
                    # 如果prompt为空，尝试使用角色基础形象词
                    if row_data['prompt'] == '' and self.mood.任务模式 == '原创模式':
                        # 获取角色信息
                        角色名 = row_data.get('fenjing', '').strip()
                        if 角色名 and 角色名 in self.mood.项目配置.get('fenjing', {}):
                            角色信息 = self.mood.项目配置['fenjing'][角色名]
                            基础形象词 = 角色信息.get('guanjianci', '').strip()
                            if 基础形象词:
                                # 🛡️ 修复重复拼接：清理基础形象词中的重复内容
                                清理后形象词 = self.清理重复内容(基础形象词)
                                # 使用清理后的角色基础形象词作为临时prompt
                                row_data['prompt'] = 清理后形象词
                                print(f"第{row+1}行prompt为空，使用角色'{角色名}'的基础形象词进行绘图")
                                if 清理后形象词 != 基础形象词:
                                    print(f"[修复] 检测到并清理了重复内容，原长度:{len(基础形象词)}，清理后长度:{len(清理后形象词)}")
                            else:
                                print(f"第{row+1}行prompt为空且角色'{角色名}'无基础形象词，跳过绘图")
                                self.mood.当前进度 = self.mood.当前进度 + 循环次数
                                continue
                        else:
                            print(f"第{row+1}行prompt为空且未找到角色信息，跳过绘图")
                            self.mood.当前进度 = self.mood.当前进度 + 循环次数
                            continue

                    表格数据 = 生成表格数据(self, row_data, row)
                    self._signal.emit(('正在绘图', 表格数据, '图片'))
                    print(f"[调试] 当前SD版本: {A0_config.config['SD版本']}")
                    print(f"[调试] 判断分支条件:")
                    print(f"  - 是否包含MJ: {'MJ' in A0_config.config['SD版本']}")
                    print(f"  - 是否包含GPT: {'GPT' in A0_config.config['SD版本']}")
                    print(f"  - 是否包含Flux: {'flux' in 表格数据.get('当前模型', '').lower()}")
                    print(f"  - 是否包含ComfyUI: {'ComfyUI' in A0_config.config['SD版本']}")

                    if 'MJ' in A0_config.config['SD版本'] or 'GPT' in A0_config.config['SD版本']:
                        print(f"[调试] 进入MJ/GPT分支 - 行{row}")
                        while self.排队数量 > 5 and self.mood.running:
                            time.sleep(1)
                        if '自备' in A0_config.config['SD版本']:
                            print(row, '本地MJ排队画图')
                        else:
                            print(row, ' MJ多线程画图')
                        thread = threading.Thread(target=self.多线程画图, args=(表格数据, row_data))
                        self.threads.append(thread)
                        thread.start()
                        if '慢速' in A0_config.config['SD版本'] or 'GPT' in A0_config.config['SD版本']:
                            time.sleep(35)
                        else:
                            time.sleep(10)
                        if '自备' in A0_config.config['SD版本']:
                            self.排队数量 += 1
                    elif 'ComfyUI' in A0_config.config['SD版本']:
                        print(f"[调试] 进入ComfyUI分支 - 行{row}")
                        if 'flux' in 表格数据.get('当前模型', '').lower():
                            print(f"[调试] 检测到Flux模型，使用Flux分支逻辑")
                            while self.排队数量 > 5 and self.mood.running:
                                time.sleep(1)
                            print(row, f" {A0_config.config['SD版本']} Flux多线程画图")
                            thread = threading.Thread(target=self.多线程画图, args=(表格数据, row_data))
                            self.threads.append(thread)
                            thread.start()
                            time.sleep(5) # 适当延长等待时间以适应Flux模型
                        else:
                            print(row, 'ComfyUI排队画图')
                            while self.排队数量 > 列队数 * 2 and self.mood.running:
                                time.sleep(1)
                            url = 获取SD列队()
                            print(f"[调试] 获取到URL: {url}")
                            print(url, 表格数据.get('当前编号'))
                            for inx in range(循环次数):
                                print(f"[调试] 启动ComfyUI线程 {inx+1}/{循环次数}")
                                thread = threading.Thread(target=self.多线程画图, args=(表格数据, row_data), kwargs={'url': url})
                                self.threads.append(thread)
                                thread.start()
                                self.排队数量 += 1
                            time.sleep(2)
                    else:
                        print(f"[调试] 进入默认分支(WebUI) - 行{row}")
                        self.多线程画图(表格数据, row_data)
                except Exception as e:
                    print(f'批量绘图错误: {e}')
                    提示 = f'批量绘图错误: {e}'
                    self._signal.emit((提示, '弹窗', '弹窗'))
                    self.mood.当前进度 = self.mood.当前进度 + 循环次数
            print('图片任务完成')
        except Exception as e:
            print(self.mood.当前任务, '处理异常', e)
            提示 = f'{self.mood.当前任务}处理异常: {e}'
            self._signal.emit((提示, '弹窗', '弹窗'))
        finally:
            for thread in self.threads:
                thread.join()

    def 清理重复内容(self, 文本内容):
        """
        清理文本中的重复内容
        """
        if not 文本内容:
            return 文本内容

        import re

        # 第一步：清理重复的词汇（如 "vibrant colors, detailed character design" 重复出现）
        词汇列表 = [词.strip() for 词 in 文本内容.split(',')]
        去重词汇 = []

        for 词汇 in 词汇列表:
            if not 词汇:
                continue
            # 检查是否已存在相同或相似的词汇
            是否重复 = False
            for 已有词汇 in 去重词汇:
                if 词汇.lower() == 已有词汇.lower():
                    是否重复 = True
                    print(f"[清理] 发现重复词汇: {词汇}")
                    break
            if not 是否重复:
                去重词汇.append(词汇)

        # 重新组合去重后的词汇
        去重文本 = ', '.join(去重词汇)

        # 第二步：按句号分割处理长句重复
        句子列表 = 去重文本.split('.')
        去重句子 = []

        for 句子 in 句子列表:
            句子 = 句子.strip()
            if not 句子:
                continue

            # 检查是否与已有句子重复（长度超过30字符才检查）
            if len(句子) > 30:
                是否重复 = False
                for 已有句子 in 去重句子:
                    # 检查完全重复或包含关系
                    if 句子 == 已有句子 or 句子 in 已有句子 or 已有句子 in 句子:
                        是否重复 = True
                        print(f"[清理] 发现重复句子: {句子[:50]}...")
                        break

                if not 是否重复:
                    去重句子.append(句子)
            else:
                # 短句子直接添加（避免过度清理）
                去重句子.append(句子)

        # 重新组合文本
        清理后文本 = '. '.join(去重句子).strip()

        # 第三步：清理多余的标点符号和空格
        清理后文本 = re.sub(r'\.+', '.', 清理后文本)  # 多个句号合并
        清理后文本 = re.sub(r'\s+', ' ', 清理后文本)  # 多个空格合并
        清理后文本 = re.sub(r'\.+$', '', 清理后文本)  # 移除末尾句号
        清理后文本 = re.sub(r',\s*,', ',', 清理后文本)  # 移除连续逗号
        清理后文本 = re.sub(r'^\s*,\s*', '', 清理后文本)  # 移除开头逗号

        return 清理后文本

    def 多线程画图_重试(self, 表格数据, row_data, url=None, max_retries=6):
        """
        带重试机制的多线程画图方法，支持502、503等错误的自动重试

        Args:
            表格数据: 图片生成所需的数据
            row_data: 行数据
            url: 请求URL
            max_retries: 最大重试次数，默认6次
        """
        import time

        for attempt in range(max_retries):
            try:
                # 检查任务是否已停止（只在第一次尝试时检查）
                if attempt == 0 and not self.mood.running:
                    print(f"[调试] 任务已停止，跳过绘图")
                    return

                # 根据重试次数调整延迟
                if attempt > 0:
                    delay = min(9 + (attempt * 3), 30)  # 9s, 12s, 15s, 18s, 21s, 24s (最大30s)
                    print(f"[重试] 第{attempt+1}次尝试绘图 (行{表格数据.get('当前编号', '未知')})，等待{delay}秒...")
                    time.sleep(delay)
                else:
                    print(f"[绘图] 开始绘图任务 (行{表格数据.get('当前编号', '未知')})")

                # 调用原始的绘图逻辑
                result = self._执行绘图逻辑(表格数据, row_data, url, attempt, max_retries)

                if result == 'success':
                    print(f"[成功] 绘图任务完成")
                    return  # 成功则直接返回
                elif result == 'retry':
                    print(f"[重试] 绘图失败，准备重试 (尝试 {attempt+1}/{max_retries})")
                    continue  # 继续重试
                else:  # result == 'fail'
                    print(f"[失败] 绘图任务最终失败")
                    return

            except Exception as e:
                error_str = str(e).lower()
                print(f"[错误] 多线程画图异常 (尝试 {attempt+1}/{max_retries}): {e}")

                # 检查是否是需要重试的错误类型
                retry_errors = [
                    '502', '503', 'bad gateway', 'service unavailable',
                    'timeout', 'timed out', 'connection error', 'network error',
                    'server error', 'internal server error', 'gateway timeout',
                    'read timeout', 'connect timeout'
                ]

                should_retry = any(error in error_str for error in retry_errors)

                if should_retry and attempt < max_retries - 1:
                    print(f"[重试] 检测到可重试错误，将继续重试 (剩余 {max_retries - attempt - 1} 次)")
                    continue
                else:
                    # 不可重试的错误或已达到最大重试次数
                    print(f"[错误] {'不可重试的错误' if not should_retry else '所有重试均失败'}")
                    表格数据['图片路径'] = ''
                    self._signal.emit(('绘图失败', 表格数据, '图片'))
                    return

    def _执行绘图逻辑(self, 表格数据, row_data, url, attempt, max_retries):
        """
        执行实际的绘图逻辑

        Returns:
            'success': 绘图成功
            'retry': 绘图失败但可以重试
            'fail': 绘图失败且不应重试
        """
        try:
            # 确保img字段存在
            if 'img' not in row_data:
                row_data['img'] = {'path': '', 'seep': random.randint(1000000000, 9999999999)}
            elif 'seep' not in row_data['img']:
                row_data['img']['seep'] = random.randint(1000000000, 9999999999)
            # 确保img_beixuan字段存在
            if 'img_beixuan' not in row_data:
                row_data['img_beixuan'] = []

            已有数量 = len(self.mood.项目配置['data'][表格数据['当前行']]['img_beixuan'])
            print(f"[调试] 准备调用文生图函数 - 已有图片数量: {已有数量}, 尝试次数: {attempt+1}/{max_retries}")

            # 检查是否启用云端模型
            if A0_config.config.get('使用Pollinations', False):
                print(f"[调试] 检测到启用Pollinations云端模型，使用云端绘图模式")

                # 🎯 优化：使用重现度最高的多角色种子组合算法
                人物分镜_原始 = 表格数据.get('人物分镜', [])
                if isinstance(人物分镜_原始, str):
                    人物分镜_原始 = [人物分镜_原始] if 人物分镜_原始 else []

                if len(人物分镜_原始) > 1:
                    print(f"[种子优化] 检测到多角色场景: {人物分镜_原始}")
                    # 使用新的多角色种子管理模块
                    from pyz.任务运行文件.多角色种子管理 import 计算多角色组合种子
                    组合种子 = 计算多角色组合种子(人物分镜_原始, self.mood.项目配置)
                    if 组合种子:
                        表格数据['组合种子'] = 组合种子
                        print(f"[种子优化] 使用多角色组合种子: {组合种子}")
                    else:
                        print(f"[种子优化] 组合种子计算失败，使用默认处理")
                else:
                    print(f"[种子优化] 单角色场景，使用原有种子逻辑")

                画图返回 = self.mood.文生图(表格数据, self, url=url)
                # 云端模型的返回处理逻辑
                if 画图返回 and len(画图返回) > 0:
                    # 云端模型成功生成图片
                    表格数据['图片路径'] = 画图返回[0]['path']  # 使用第一张图片作为主图
                    表格数据['备选图片路径'] = 画图返回
                    row_data['img']['path'] = 画图返回[0]['path']
                    row_data['img']['seep'] = 画图返回[0]['seep']
                    row_data['img_beixuan'] = 画图返回
                    self.mood.保存配置()
                    self._signal.emit(('绘图完成', 表格数据, '图片'))
                    print(f"[成功] 云端模型绘图成功 - 生成了{len(画图返回)}张图片")
                    return 'success'
                else:
                    print(f"[错误] 云端模型绘图失败 (尝试 {attempt+1}/{max_retries})")
                    if attempt == max_retries - 1:
                        表格数据['图片路径'] = ''
                        self._signal.emit(('绘图失败', 表格数据, '图片'))
                        return 'fail'
                    return 'retry'
            else:
                # 本地模型的处理逻辑
                print(f"[调试] 使用本地模型绘图模式")

                # 🎯 优化：本地模型也使用多角色组合种子
                人物分镜_原始 = 表格数据.get('人物分镜', [])
                if isinstance(人物分镜_原始, str):
                    人物分镜_原始 = [人物分镜_原始] if 人物分镜_原始 else []

                if len(人物分镜_原始) > 1:
                    print(f"[种子优化] 本地模型检测到多角色场景: {人物分镜_原始}")
                    # 使用新的多角色种子管理模块
                    from pyz.任务运行文件.多角色种子管理 import 计算多角色组合种子
                    组合种子 = 计算多角色组合种子(人物分镜_原始, self.mood.项目配置)
                    if 组合种子:
                        表格数据['组合种子'] = 组合种子
                        print(f"[种子优化] 本地模型使用多角色组合种子: {组合种子}")
                    else:
                        print(f"[种子优化] 本地模型组合种子计算失败，使用默认处理")
                else:
                    print(f"[种子优化] 本地模型单角色场景，使用原有种子逻辑")

                画图返回 = self.mood.文生图(表格数据, self, url=url)
                if 画图返回 and len(画图返回) > 已有数量:
                    表格数据['图片路径'] = 画图返回[已有数量]['path']
                    表格数据['备选图片路径'] = 画图返回
                    row_data['img']['path'] = 画图返回[已有数量]['path']
                    row_data['img']['seep'] = 画图返回[已有数量]['seep']
                    row_data['img_beixuan'] = 画图返回
                    self.mood.保存配置()
                    self._signal.emit(('绘图完成', 表格数据, '图片'))
                    print(f"[成功] 本地模型绘图成功")
                    return 'success'
                else:
                    print(f"[错误] 本地模型绘图失败 (尝试 {attempt+1}/{max_retries})")
                    if attempt == max_retries - 1:
                        表格数据['图片路径'] = ''
                        self._signal.emit(('绘图失败', 表格数据, '图片'))
                        return 'fail'
                    return 'retry'

        except Exception as e:
            print(f"[错误] 绘图逻辑执行异常: {e}")
            raise e  # 重新抛出异常让上层处理

    def 多线程画图(self, 表格数据, row_data, url=None):
        """
        多线程画图方法，现在使用带重试机制的版本
        """
        try:
            print(f"[调试] 多线程画图开始 - 行{表格数据['当前行']}")
            print(f"[调试] 使用URL: {url}")
            print(f"[调试] 表格数据提示词: {表格数据['提示词'][:100]}...")

            # 调用带重试机制的绘图方法
            self.多线程画图_重试(表格数据, row_data, url)

        except Exception as e:
            print('多线程画图外层异常', e)
            表格数据['图片路径'] = ''
            self._signal.emit(('绘图失败', 表格数据, '图片'))
        finally:
            self.排队数量 -= 1
            self.mood.当前进度 = self.mood.当前进度 + 1


    def 批量语音(self):
        try:
            threads = []
            self.请求 = False
            for row, row_data in enumerate(self.mood.项目配置['data']):
                while self.请求:
                    time.sleep(1)
                if not self.mood.running:
                    print('批量语音任务停止')
                    self.mood.停止 = True
                    break
                if self.mood.任务模式 == '原创模式':
                    内容 = row_data['txt']
                    if A0_config.config['海外模式'] and row_data.get('txt_en'):
                        内容 = row_data['txt_en']
                        if bool(re.search('[\\u4e00-\\u9fa5]', 内容)):
                            内容 = 智普AI翻译(self.mood, 内容)
                else:  # inserted
                    内容 = row_data['txtxiugai']
                表格数据 = {'当前行': row, '内容': 内容, '语音路径': A0_config.改文件名(row_data['yuyin'])}
                if jduvudu312us_usjlq.path.exists(A0_config.改文件名(row_data['yuyin'])):
                    print(A0_config.改文件名(row_data['yuyin']), '存在,生成下一条')
                    self.mood.当前进度 = self.mood.当前进度 + 1
                    self._signal.emit(('语音完成', 表格数据, '语音'))
                else:  # inserted
                    self._signal.emit(('语音合成中', 表格数据, '语音'))
                    if f'{A0_config.name_gpt}' in A0_config.config['语音平台'] or A0_config.NOVIP:
                        if '9234' in A0_config.config['CV链接'] and (not A0_config.NOVIP):
                            thread = threading.Thread(target=self.合成语音, args=(表格数据,))
                            threads.append(thread)
                            thread.start()
                            time.sleep(1)
                        else:  # inserted
                            self.合成语音(表格数据)
                    else:  # inserted
                        thread = threading.Thread(target=self.合成语音, args=(表格数据,))
                        threads.append(thread)
                        thread.start()
                        time.sleep(5)
            for thread in threads:
                thread.join()
            print('语音合成完成')
        except Exception as e:
            print(self.mood.当前任务, '处理异常', e)
            提示 = f'{self.mood.当前任务}处理异常: {e}'
            self._signal.emit((提示, '弹窗', '弹窗'))

    def 合成视频(self):
        try:
            if not self.mood.running:
                print('合成视频任务停止')
            else:  # inserted
                if A0_config.config['剪映草稿文件夹'] == '':
                    提示 = '请先设置剪映草稿路径,设置方法:\n打开\"剪映\"--右上角\"设置\"找到\"全局设置\"\n将\"草稿位置\"的路径复制到\"文视AI--软件设置--剪映草稿\"输入框中'
                    print(提示)
                    self._signal.emit((提示, '弹窗', '弹窗'))
                else:  # inserted
                    if self.mood.任务模式 == '原创模式':
                        if jduvudu312us_usjlq.path.exists(A0_config.改文件名(self.mood.项目配置['音频路径'])):
                            生成剪映文件_原声(self.mood, self)
                        else:  # inserted
                            生成剪映文件(self.mood, self)
                    else:  # inserted
                        用原声 = True
                        for index, data in enumerate(self.mood.项目配置['data']):
                            if index >= len(self.mood.项目配置['data']) - 1:
                                break
                            if data['txt']!= data['txtxiugai']:
                                用原声 = False
                        if 用原声 and jduvudu312us_usjlq.path.exists(A0_config.改文件名(self.mood.项目配置['音频路径'])):
                            生成剪映文件_原声(self.mood, self)
                        else:  # inserted
                            生成剪映文件(self.mood, self)
        except Exception as e:
            print(self.mood.当前任务, '处理异常', e)
            提示 = f'{self.mood.当前任务}处理异常: {e}'
            self._signal.emit((提示, '弹窗', '弹窗'))

    def 一键成片(self):
        if self.mood.端口 != 'yun':
            self.批量推理()
        self.批量绘图()
        if self.mood.任务模式 == '原创模式':
            if not jduvudu312us_usjlq.path.exists(A0_config.改文件名(self.mood.项目配置['音频路径'])):
                self.批量语音()
        if self.mood.任务模式 == '克隆模式':
            合成语音 = False
            for row, row_data in enumerate(self.mood.项目配置['data']):
                if row_data['txt']!= row_data['txtxiugai']:
                    合成语音 = True
            if 合成语音:
                self.批量语音()
        self.合成视频()

    def 加载项目(self):
        try:
                    # print(f'[调试] {self.mood.当前项目} 加载中. . .')
        # print(f'[调试] 任务模式: {self.mood.任务模式}')
            self.mood.重置表格()
            列表数据 = self.mood.项目配置['data']
            # print(f'[调试] 配置数据条数: {len(列表数据)}')
            if self.mood.任务模式 == '原创模式':
                for index, row in enumerate(列表数据):
                    if not self.mood.running:
                        print('加载任务停止')
                        self.mood.停止 = True
                        break
                    txt = row.get('txt', '')
                    img_path = row.get('img', {}).get('path', '')
                    fenjing = row.get('fenjing', '')
                    prompt = row.get('miaoshu', '')
                    prompt_video = row.get('prompt_video', '')

                    新图尺寸 = ''
                    if img_path:
                        if jduvudu312us_usjlq.path.exists(A0_config.改文件名(img_path)):
                            image = Image.open(A0_config.改文件名(img_path))
                            新图尺寸 = f'{image.width}x{image.height}'
                    图片行 = self.mood.图片行
                    备选图片数组 = []
                    if img_path:
                        if jduvudu312us_usjlq.path.exists(img_path):
                            image_path = self.mood.获取图片数组(index)
                            组件高度 = self.mood.缩放(160)
                            单个高度 = 组件高度 - 5
                            if len(image_path) > 5:
                                单个高度 = int((组件高度 - 5) / 2)
                            备选图片数组 = []
                            try:
                                if 'img_beixuan' in row and isinstance(row['img_beixuan'], list):
                                    for image_path in row['img_beixuan']:
                                        if jduvudu312us_usjlq.path.exists(A0_config.改文件名(image_path['path'])):
                                            pixmap = QPixmap(A0_config.改文件名(image_path['path']))
                                            if pixmap.height() > 0:
                                                width = int(单个高度 * pixmap.width() / pixmap.height())
                                                scaled_pixmap = pixmap.scaledToWidth(width)
                                                备选图片数组.append(scaled_pixmap)
                            except Exception as e:
                                print('错误', e)
                        else:
                            备选图片数组 = ''
                    else:
                        备选图片数组 = ''
                    data = (index, txt, fenjing, prompt, 图片行, img_path, 备选图片数组, 新图尺寸, prompt_video)
                    # print(f'[调试] 发送信号 - 行{index}: 数据长度={len(data)}')
                    self._signal.emit(('视频分镜', data, '加载项目'))
                    time.sleep(0.001)
            else:
                for index, row in enumerate(列表数据):
                    if not self.mood.running:
                        print('加载任务停止')
                        self.mood.停止 = True
                        break
                    txt = row.get('txt', '')
                    img_path = row.get('img', {}).get('path', '')
                    fenjing = row.get('fenjing', '')
                    prompt = row.get('prompt', '')
                    prompt_video = row.get('prompt_video', '')
                    新图尺寸 = ''
                    if img_path and jduvudu312us_usjlq.path.exists(A0_config.改文件名(img_path)):
                        try:
                            image = Image.open(A0_config.改文件名(img_path))
                            新图尺寸 = f'{image.width}x{image.height}'
                        except Exception as e:
                            print(f"加载图片尺寸失败: {e}")
                    图片行 = self.mood.图片行
                    备选图片数组 = []
                    if img_path and jduvudu312us_usjlq.path.exists(A0_config.改文件名(img_path)):
                        image_paths = self.mood.获取图片数组(index)
                        组件高度 = self.mood.缩放(160)
                        单个高度 = 组件高度 - 5
                        if len(image_paths) > 5:
                            单个高度 = int((组件高度 - 5) / 2)
                        if 'img_beixuan' in row and isinstance(row['img_beixuan'], list):
                            for image_data in row['img_beixuan']:
                                beixuan_path = image_data.get('path', '')
                                if beixuan_path and jduvudu312us_usjlq.path.exists(A0_config.改文件名(beixuan_path)):
                                    try:
                                        pixmap = QPixmap(A0_config.改文件名(beixuan_path))
                                        if pixmap.height() > 0:
                                            width = int(单个高度 * pixmap.width() / pixmap.height())
                                            scaled_pixmap = pixmap.scaledToWidth(width)
                                            备选图片数组.append(scaled_pixmap)
                                    except Exception as e:
                                        print(f"加载备选图片失败: {e}")
                    data = (index, txt, fenjing, prompt, 图片行, img_path, 备选图片数组, 新图尺寸, prompt_video)
                    # print(f'[调试] 发送信号 - 行{index}: 数据长度={len(data)}')
                    self._signal.emit(('视频分镜', data, '加载项目'))
                    time.sleep(0.001)
        except Exception as e:
            import traceback
            print(f"加载项目时发生严重错误: {e}")
            print(traceback.format_exc())
            提示 = f'{self.mood.当前任务}处理异常: {e}'
            self._signal.emit((提示, '弹窗', '弹窗'))

    def 修改模型(self, 任务, 模型):
        print(任务, 模型)
        try:
            A5_加载本地配置.加载配置(self.mood).设置模型(任务, 模型)
        except Exception as e:
            print(f'{任务}失败, 请检查SD是否正常启动: {e}')
            提示 = f'{任务}失败,, 请检查SD是否正常启动: {e}'
            self._signal.emit((提示, '弹窗', '弹窗'))
        self._signal.emit(('视频分镜', 'data', '换模型'))

    @test_time
    def run(self):
        self.mood.tanchuang = True
        self.mood.当前进度 = 0
        self.mood.语音字数 = 0
        try:
            VIP任务 = ['批量改写任务', '批量推理任务', '批量绘图任务', '批量语音任务', '批量视频任务', '批量放大任务', '视频推理任务', '一键成片任务', '批量计划任务', '视频转绘任务', '虚拟人任务', '虚拟人任务_出图', '虚拟人任务_语音', '海外模式切换']
            # 离线模式跳过VIP检查
            if False:  # 禁用VIP检查，允许离线使用所有功能
                提示 = f'{self.mood.当前任务}为VIP专属功能, 请先升级为VIP, \n具体会员权益请看软件左侧 \"会员充值\" 查看'
                print(提示)
                self._signal.emit((提示, '弹窗', '弹窗'))
                return
            else:  # inserted
                if self.mood.当前任务 == '视频分镜':
                    self.视频分镜()
                else:  # inserted
                    if self.mood.当前任务 == '批量改写任务':
                        self.批量改写()
                    else:  # inserted
                        if self.mood.当前任务 == '海外模式切换':
                            self.批量翻译()
                        else:  # inserted
                            if self.mood.当前任务 == '批量放大任务':
                                self.批量放大()
                            else:  # inserted
                                if self.mood.当前任务 == '批量推理任务':
                                    self.批量推理()
                                else:  # inserted
                                    if self.mood.当前任务 == '视频推理任务':
                                        self.批量推理_视频()
                                    else:  # inserted
                                        if self.mood.当前任务 == '批量绘图任务':
                                            self.批量绘图()
                                        else:  # inserted
                                            if self.mood.当前任务 == '批量视频任务':
                                                self.批量视频()
                                            else:  # inserted
                                                if self.mood.当前任务 == '批量语音任务':
                                                    self.批量语音()
                                                else:  # inserted
                                                    if self.mood.当前任务 == '合成视频任务':
                                                        self.合成视频()
                                                    else:  # inserted
                                                        if self.mood.当前任务 == '一键成片任务':
                                                            self.一键成片()
                                                        else:  # inserted
                                                            if self.mood.当前任务 == '加载项目任务':
                                                                self.加载项目()
                                                            else:  # inserted
                                                                if self.mood.当前任务 == '镜头分割任务':
                                                                    imgs, videos, audios = 视频分割(self)
                                                                    for index, (img, vid, aud) in enumerate(zip(imgs, videos, audios)):
                                                                        self.mood.项目配置['data'].append({'index': index + 1, 'txt': '', 'txt_en': '', 'txtxiugai': '', 'img_yuantu': img, 'yuyin': aud, 'img': {'path': '', 'seep': (-1)}, 'fenjing': '', 'miaoshu': '', 'changjing': '', 'prompt': '', 'prompt_video': '', 'video': '', 'video_beixuan': [], 'img_beixuan': 0, 'mid_time': 0, '中景': '旁白', 'img_end': jduvudu312us_usjlq.path.join(self.mood.原始图片文件夹, f'{index + 1}_end.png'), 'img_cn': img, 'video_cn': vid, 'yinxiao': '', 'yunjing': ''})
                                                                    self.mood.保存配置()
                                                                    self.mood.running = False
                                                                    self.mood.zhuxian = False
                                                                    self._signal.emit(('镜头分割任务', '镜头分割任务', '镜头分割任务'))
                                                                else:  # inserted
                                                                    if self.mood.当前任务 == '批量计划任务':
                                                                        rowCount = self.mood.计划列表.rowCount()
                                                                        if rowCount <= 0:
                                                                            return
                                                                        if self.mood.running and (self.mood.当前任务 == '合成视频任务' or self.mood.当前任务 == '批量计划任务' or self.mood.当前任务 == '一键成片任务'):
                                                                            messagebox.showinfo('提示', '视频合成成功,可在剪映草稿中查看并导出视频')
                                                                        self.mood.当前进度 = self.mood.任务总数
                                                                        self.mood.停止 = True
                                                                        self.mood.running = False
                                                                        self.mood.zhuxian = False
                                                                        self.mood.stop_run = True
                                                                        self.mood.subscription_key = ''
                                                                        self.mood.subscription_token = ''
                                                                        self.mood.region = ''
                                                                        self._signal.emit(('row', '已完成', '任务完成'))
                                                                        gc.collect()
                                                                        if self.mood.语音字数 > 0:
                                                                            # 离线模式下跳过TTS统计
                                                                            print(f'离线模式: 跳过TTS统计，字数: {self.mood.语音字数}')
                                                                        if self.mood.任务模式 == '视频转绘' and A0_config.config['SD版本'] == 'WebUI':
                                                                            提示 = '当前功能仅指定ComfyUI可用, 请在使用教程文档中下载, 云端请使用专用镜像'
                                                                            print(提示)
                                                                            self._signal.emit((提示, '弹窗', '弹窗'))
                                                                        for row in range(rowCount):
                                                                            if not self.mood.running:
                                                                                break
                                                                            self.mood.当前项目 = self.mood.计划列表.cellWidget(row, 1).currentText()
                                                                            print(self.mood.计划列表.cellWidget(row, 1).currentText())
                                                                            状态 = self.mood.计划列表.item(row, 9).text()
                                                                            if 状态 == '已完成':
                                                                                continue
                                                                            A0_config.config['当前选中模型'] = self.mood.计划列表.cellWidget(row, 2).currentText()
                                                                            A0_config.config['当前选中VAE'] = self.mood.计划列表.cellWidget(row, 3).currentText()
                                                                            A0_config.config['图片采样方法'] = self.mood.计划列表.cellWidget(row, 4).currentText()
                                                                            A0_config.config['调度器'] = self.mood.计划列表.cellWidget(row, 5).currentText()
                                                                            A0_config.config['提示词相关性'] = self.mood.计划列表.item(row, 6).text()
                                                                            A0_config.config['图片采样步数'] = self.mood.计划列表.item(row, 7).text()
                                                                            A0_config.config['修复重绘幅度'] = self.mood.计划列表.item(row, 8).text()
                                                                            self._signal.emit((row, '制作中', '批量计划任务'))
                                                                            if self.mood.当前项目 == '':
                                                                                messagebox.showinfo('提示', '请先新建任务后继续')
                                                                                break
                                                                            if self.mood.任务模式 == '原创模式':
                                                                                self.mood.当前项目文件夹 = jduvudu312us_usjlq.path.join(self.mood.项目文件夹, '原创项目', self.mood.当前项目)
                                                                            elif self.mood.任务模式 == '克隆模式':
                                                                                self.mood.当前项目文件夹 = jduvudu312us_usjlq.path.join(self.mood.项目文件夹, '克隆项目', self.mood.当前项目)
                                                                            elif self.mood.任务模式 == '视频转绘':
                                                                                self.mood.当前项目文件夹 = jduvudu312us_usjlq.path.join(self.mood.项目文件夹, '转绘项目', self.mood.当前项目)
                                                                            elif self.mood.任务模式 == 'AI虚拟人':
                                                                                self.mood.当前项目文件夹 = jduvudu312us_usjlq.path.join(self.mood.项目文件夹, '虚拟人项目', self.mood.当前项目)
                                                                            self.mood.图片文件夹 = jduvudu312us_usjlq.path.join(self.mood.当前项目文件夹, '图片文件')
                                                                            self.mood.视频文件夹 = jduvudu312us_usjlq.path.join(self.mood.当前项目文件夹, '视频文件')
                                                                            self.mood.原始图片文件夹 = jduvudu312us_usjlq.path.join(self.mood.当前项目文件夹, '原始图片')
                                                                            self.mood.音频文件夹 = jduvudu312us_usjlq.path.join(self.mood.当前项目文件夹, '音频文件')
                                                                            jduvudu312us_usjlq.makedirs(self.mood.当前项目文件夹, exist_ok=True)
                                                                            jduvudu312us_usjlq.makedirs(self.mood.图片文件夹, exist_ok=True)
                                                                            jduvudu312us_usjlq.makedirs(self.mood.视频文件夹, exist_ok=True)
                                                                            jduvudu312us_usjlq.makedirs(self.mood.原始图片文件夹, exist_ok=True)
                                                                            jduvudu312us_usjlq.makedirs(self.mood.音频文件夹, exist_ok=True)
                                                                            self.mood.项目配置文件 = jduvudu312us_usjlq.path.join(self.mood.当前项目文件夹, f'{self.mood.当前项目}.json')
                                                                            with open(self.mood.项目配置文件, 'r', encoding='utf-8') as file:
                                                                                self.mood.项目配置 = json.load(file)
                                                                            if self.mood.任务模式 == '原创模式' or self.mood.任务模式 == '克隆模式' or self.mood.任务模式 == '视频转绘':
                                                                                self.mood.任务总数 = 2 * len(self.mood.项目配置['data'])
                                                                                if self.mood.任务模式 == '原创模式' and (not jduvudu312us_usjlq.path.exists(A0_config.改文件名(self.mood.项目配置['音频路径']))):
                                                                                    self.mood.任务总数 = 3 * len(self.mood.项目配置['data'])
                                                                                else:
                                                                                    if self.mood.任务模式 == '克隆模式':
                                                                                        合成语音 = False
                                                                                        for row, row_data in enumerate(self.mood.项目配置['data']):
                                                                                            if row_data['txt']!= row_data['txtxiugai']:
                                                                                                合成语音 = True
                                                                                        if 合成语音:
                                                                                            self.mood.任务总数 = 3 * len(self.mood.项目配置['data'])
                                                                                    self.mood.当前进度 = 0
                                                                                    if A0_config.config['SD版本'] == 'WebUI':
                                                                                        模型 = self.mood.计划列表.cellWidget(row, 2).currentText()
                                                                                        vae = self.mood.计划列表.cellWidget(row, 3).currentText()
                                                                                        self.修改模型('换模型', 模型)
                                                                                        self.修改模型('换VAE', vae)
                                                                                    self.一键成片()
                                                                                    if self.mood.running:
                                                                                        self._signal.emit((row, '已完成', '批量计划任务'))
                                                                    elif self.mood.当前任务 == '视频转绘任务' and A0_config.config['SD版本'] == 'WebUI':
                                                                        提示 = '当前功能仅指定ComfyUI可用, 请在使用教程文档中下载, 云端请使用专用镜像'
                                                                        print(提示)
                                                                        self._signal.emit((提示, '弹窗', '弹窗'))
                                                                        if self.mood.running:
                                                                            制作结果 = 视频转绘(self)
                                                                            if 制作结果 and (not self.mood.转绘调试):
                                                                                合成长视频(self)
                                                                                jduvudu312us_usjlq.startfile(A0_config.改文件名(self.mood.项目配置['path']))
                                                                            elif 制作结果:
                                                                                jduvudu312us_usjlq.startfile(A0_config.改文件名(self.mood.项目配置['paths'][0]))
                                                                    elif self.mood.当前任务 == '更换SD模型':
                                                                        self.修改模型('换模型', A0_config.config['当前选中模型'])
                                                                    elif self.mood.当前任务 == '更换VAE模型':
                                                                        self.修改模型('换VAE', A0_config.config['当前选中VAE'])
                                                                    elif self.mood.当前任务 == '下载网络视频' and self.mood.下载网络视频1(self):
                                                                        self.mood.open_folder(self.mood.网络视频文件夹)
                # 播放完成提示音（已完成的任务才播放）
                任务 = ['批量改写任务', '批量放大任务', '视频推理任务', '批量绘图任务', '批量视频任务', '批量推理任务', '批量语音任务', '一键成片任务', '视频转绘任务', '批量计划任务', '下载网络视频', '合成视频任务', '虚拟人任务', '虚拟人任务_出图', '虚拟人任务_语音']
                if self.mood.当前任务 in 任务:
                    try:
                        self.bofang()
                    except:
                        pass
        except Exception as e:
            提示 = f'{self.mood.当前任务}运行异常: {e}'
            print(提示)
            self._signal.emit((提示, '弹窗', '弹窗'))
        else:
            if self.mood.running and (self.mood.当前任务 == '合成视频任务' or self.mood.当前任务 == '批量计划任务' or self.mood.当前任务 == '一键成片任务'):
                messagebox.showinfo('提示', '视频合成成功,可在剪映草稿中查看并导出视频')
            self.mood.当前进度 = self.mood.任务总数
            self.mood.停止 = True
            self.mood.running = False
            self.mood.zhuxian = False
            self.mood.stop_run = True
            self.mood.subscription_key = ''
            self.mood.subscription_token = ''
            self.mood.region = ''
            # 发送任务完成信号
            self._signal.emit(('任务完成', '', '任务完成'))

    def bofang(self):
        winsound.PlaySound('finish.wav', winsound.SND_FILENAME)



