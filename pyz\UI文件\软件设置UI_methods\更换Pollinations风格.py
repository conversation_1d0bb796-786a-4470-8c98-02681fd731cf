# 更换Pollinations风格方法
# 从软件设置UI.py拆分出来的方法

from pyz.任务运行文件 import A0_config

def 更换Pollinations风格(self):
    """更换Pollinations风格"""
    选中文本 = self.Pollinations风格.currentText()

    # 🎨 处理分类系统的风格名称
    if 选中文本.startswith('━━━') and 选中文本.endswith('━━━'):
        # 如果选中的是分类分隔符，不做任何操作
        print(f"选中了分类分隔符，忽略: {选中文本}")
        return

    # 移除缩进空格，获取真实的风格名称
    真实风格名 = 选中文本.strip()

    # 保存到配置
    A0_config.config['pollinations_style'] = 真实风格名

    # 保存配置到文件
    A0_config.修改配置()

    print(f"✅ 切换到Pollinations风格: {真实风格名} (已保存)")

    # 🎨 更新正面和反面提示词文本框
    try:
        from pyz.UI文件.软件设置UI_methods.Pollinations风格分类 import 获取风格提示词

        if 真实风格名 == '无风格':
            # 清空提示词
            if hasattr(self, '正面风格提示词'):
                self.正面风格提示词.setPlainText('')
            if hasattr(self, '反面风格提示词'):
                self.反面风格提示词.setPlainText('')
            print("已清空风格提示词")
        else:
            # 获取并设置风格提示词
            风格提示词 = 获取风格提示词(真实风格名)
            if 风格提示词:
                # 更新正面提示词文本框
                if hasattr(self, '正面风格提示词'):
                    self.正面风格提示词.setPlainText(风格提示词)
                    print(f"已更新正面提示词: {风格提示词[:50]}...")

                # 设置默认反面提示词（通用的）
                if hasattr(self, '反面风格提示词'):
                    默认反面提示词 = "low quality, blurry, distorted, watermark, signature, text, logo, worst quality, low resolution, pixelated, artifacts, noise, oversaturated, undersaturated, overexposed, underexposed, bad anatomy, deformed, mutated"
                    self.反面风格提示词.setPlainText(默认反面提示词)
                    print("已设置默认反面提示词")
            else:
                print(f"未找到风格 '{真实风格名}' 的提示词")

    except Exception as e:
        print(f"更新风格提示词失败: {e}")
        import traceback
        traceback.print_exc()
