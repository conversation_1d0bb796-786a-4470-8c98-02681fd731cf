#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试导入修复
"""

import os
import sys

# 添加项目路径到sys.path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_简单导入():
    """测试简单导入"""
    print("=" * 60)
    print("测试导入修复")
    print("=" * 60)
    
    try:
        # 测试PyQt5组件导入
        from PyQt5.QtWidgets import QLineEdit, QLabel, QComboBox, QCheckBox, QPushButton
        print("✅ PyQt5组件导入成功")
        
        # 测试绘图模型按钮方法导入
        from pyz.UI文件.软件设置UI_methods.绘图模型按钮 import 绘图模型按钮
        print("✅ 绘图模型按钮方法导入成功")
        
        # 测试更新密钥方法导入
        from pyz.UI文件.软件设置UI_methods.更新Pollinations密钥 import 更新Pollinations密钥, 切换Pollinations密钥显示
        print("✅ 更新Pollinations密钥方法导入成功")
        
        # 测试QLineEdit常量
        password_mode = QLineEdit.Password
        normal_mode = QLineEdit.Normal
        print(f"✅ QLineEdit常量可用: Password={password_mode}, Normal={normal_mode}")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_配置功能():
    """测试配置功能"""
    print("\n" + "=" * 60)
    print("测试配置功能")
    print("=" * 60)
    
    try:
        from pyz.任务运行文件 import A0_config
        
        # 测试密钥配置
        原密钥 = A0_config.config.get('pollinations_api_key', '')
        print(f"📋 当前密钥状态: {'已设置' if 原密钥 else '未设置'}")
        
        # 测试设置密钥
        测试密钥 = "test_key_for_kontext"
        A0_config.config['pollinations_api_key'] = 测试密钥
        
        # 验证设置
        验证密钥 = A0_config.config.get('pollinations_api_key', '')
        if 验证密钥 == 测试密钥:
            print("✅ 密钥设置功能正常")
        else:
            print("❌ 密钥设置功能异常")
        
        # 恢复原设置
        A0_config.config['pollinations_api_key'] = 原密钥
        
        return True
        
    except Exception as e:
        print(f"❌ 配置功能测试失败: {e}")
        return False

def test_API参数构建():
    """测试API参数构建"""
    print("\n" + "=" * 60)
    print("测试API参数构建")
    print("=" * 60)
    
    try:
        from pyz.任务运行文件 import A0_config
        
        # 设置测试密钥
        测试密钥 = "sk-test123456789"
        原密钥 = A0_config.config.get('pollinations_api_key', '')
        A0_config.config['pollinations_api_key'] = 测试密钥
        
        # 模拟API参数构建
        params = {}
        
        # 添加token（模拟工作流中的逻辑）
        api_key = A0_config.config.get('pollinations_api_key', '').strip()
        if api_key:
            params['token'] = api_key
            print(f"🔑 添加token参数: {api_key[:4]}...{api_key[-4:]}")
        
        # 添加其他参数
        params.update({
            'model': 'kontext',
            'width': 1280,
            'height': 960,
            'seed': 123456
        })
        
        print(f"📊 构建的参数:")
        for key, value in params.items():
            if key == 'token':
                print(f"   - {key}: {'*' * len(str(value))} (已隐藏)")
            else:
                print(f"   - {key}: {value}")
        
        # 检查token位置
        参数键列表 = list(params.keys())
        if 'token' in 参数键列表 and 参数键列表.index('token') == 0:
            print("✅ token参数在第一位")
        else:
            print("❌ token参数位置错误")
        
        # 恢复原设置
        A0_config.config['pollinations_api_key'] = 原密钥
        
        return True
        
    except Exception as e:
        print(f"❌ API参数构建测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始简单导入测试")
    
    # 测试导入
    导入成功 = test_简单导入()
    
    # 测试配置
    配置成功 = test_配置功能()
    
    # 测试API参数
    API成功 = test_API参数构建()
    
    print("\n" + "=" * 60)
    print("测试结果")
    print("=" * 60)
    
    print(f"📦 导入测试: {'✅ 通过' if 导入成功 else '❌ 失败'}")
    print(f"⚙️ 配置测试: {'✅ 通过' if 配置成功 else '❌ 失败'}")
    print(f"🌐 API测试: {'✅ 通过' if API成功 else '❌ 失败'}")
    
    if all([导入成功, 配置成功, API成功]):
        print("\n🎉 修复成功！")
        print("\n💡 现在您可以:")
        print("1. 在软件设置中看到'API密钥'输入框")
        print("2. 输入您的Pollinations API密钥")
        print("3. 使用kontext模型而不会出现认证错误")
        print("4. 享受高级模型功能")
        
        print("\n🔗 获取API密钥:")
        print("   https://auth.pollinations.ai")
    else:
        print("\n⚠️ 部分功能可能仍有问题")
    
    print("=" * 60)
