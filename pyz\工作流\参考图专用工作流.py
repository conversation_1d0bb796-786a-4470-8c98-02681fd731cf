#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
参考图专用工作流
专门为生成参考图优化的工作流，不受软件设置中选择的工作流影响
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from pyz.任务运行文件 import A0_config

class 参考图专用工作流:
    """参考图专用工作流生成器"""

    def __init__(self):
        self.config = A0_config.config

    def 生成随机种子(self):
        """生成随机种子"""
        import random
        return random.randint(1000000000, 9999999999)

    def 获取可用采样器(self):
        """获取当前ComfyUI支持的采样器"""
        try:
            采样方法列表 = self.config.get('采样方法', [])

            # 优先选择的采样器列表（按优先级排序）
            优先采样器 = [
                "dpmpp_2m",
                "dpmpp_2s_ancestral",
                "euler_ancestral",
                "euler",
                "dpm_fast"
            ]

            # 选择第一个可用的采样器
            for 采样器 in 优先采样器:
                if 采样器 in 采样方法列表:
                    return 采样器

            # 如果优先采样器都不可用，使用列表中的第一个
            if 采样方法列表:
                return 采样方法列表[0]

            # 默认采样器
            return "euler"

        except Exception as e:
            print(f"⚠️  获取采样器失败，使用默认: {e}")
            return "euler"

    def 检测模型类型(self, 模型名):
        """检测模型类型"""
        模型名_lower = 模型名.lower()

        # 先检查SD1.5模型（排除误判）
        if "1.5" in 模型名_lower or "[1.5" in 模型名_lower:
            return "SD15"

        # SDXL模型检测
        if any(keyword in 模型名_lower for keyword in ["xl", "sdxl", "protovision"]):
            return "SDXL"

        # 特殊处理：推文大模型不带1.5标识的是SDXL版本
        if "推文大模型" in 模型名_lower and "1.5" not in 模型名_lower:
            return "SDXL"

        # Flux模型检测
        if "flux" in 模型名_lower:
            return "Flux"

        # Kolors模型已删除，统一使用SDXL处理
        if "kolors" in 模型名_lower:
            return "SDXL"

        # 默认SD1.5
        return "SD15"

    def 检测是否使用云端模型(self):
        """检测当前是否启用了云端模型"""
        return self.config.get('使用Pollinations', False)

    def 生成参考图工作流(self, 模型名="", 提示词="", 角色名=""):
        """
        生成专用的参考图工作流
        优化参数：更注重人物特征、面部清晰度、角色一致性
        支持云端模型自动切换
        """

        # 检查是否启用云端模型
        使用云端模型 = self.检测是否使用云端模型()

        if 使用云端模型:
            print("☁️  使用云端参考图专用工作流")
            print("---------------------------------------------")
            print("📋 云端工作流特点: 专为云端参考图生成优化")
            print("   - 云端AI模型支持")
            print("   - 优化的提示词处理")
            print("   - 角色一致性增强")
            print("   - 无需本地GPU资源")

            return self.生成云端参考图工作流(提示词, 角色名)
        else:
            print("🎨 使用本地参考图专用工作流")
            print("---------------------------------------------")
            print("📋 本地工作流特点: 专为参考图生成优化")
            print("   - 高清晰度面部特征")
            print("   - 优化的采样参数")
            print("   - 角色一致性增强")
            print("   - 不受软件设置工作流影响")

            # 如果没有指定模型，使用当前配置的模型
            if not 模型名:
                模型名 = self.config.get('当前选中模型', 'v1-5-pruned-emaonly.safetensors')

            # 检测模型类型
            模型类型 = self.检测模型类型(模型名)
            print(f"🔍 检测到模型类型: {模型类型}")
            print(f"📋 目标模型: {模型名}")

            # 获取可用采样器
            可用采样器 = self.获取可用采样器()
            print(f"🔧 使用采样器: {可用采样器}")

            # 优化提示词 - 专门为参考图生成（已包含not格式负面词）
            优化提示词 = self.优化参考图提示词(提示词, 角色名)

            print(f"✨ 优化后提示词: {优化提示词}")
            print(f"📝 负面词已转换为not格式并包含在主提示词中")

            # 根据模型类型生成对应工作流（不再传递负面提示词）
            if 模型类型 == "SDXL":
                return self.生成SDXL参考图工作流(模型名, 优化提示词, 可用采样器)
            elif 模型类型 == "Flux":
                return self.生成Flux参考图工作流(模型名, 优化提示词, 可用采样器)
            else:
                return self.生成SD15参考图工作流(模型名, 优化提示词, 可用采样器)

    def 获取通用质量固定词(self):
        """获取通用质量固定词，放在提示词最前面"""
        return "masterpiece, best quality, ultra detailed, 8k resolution, high resolution, extremely detailed, photorealistic, professional photography, perfect lighting, vibrant colors, sharp focus, high contrast, detailed textures, cinematic composition, award winning photography, studio lighting, professional portrait, crystal clear, ultra sharp, perfect exposure, color grading, depth of field, bokeh effect, professional quality, commercial grade, gallery worthy, museum quality, fine art photography, exceptional detail, flawless composition, perfect clarity, stunning visual, breathtaking quality, world class, premium quality, top tier, elite level, superior craftsmanship, artistic excellence"

    def 转换负面词为not格式(self, 负面词):
        """
        将负面提示词转换为not格式

        参数:
            负面词: 原始负面提示词字符串

        返回:
            转换后的not格式字符串
        """
        if not 负面词 or not 负面词.strip():
            return ""

        # 分割负面词
        词汇列表 = [词.strip() for 词 in 负面词.split(',') if 词.strip()]

        # 转换为not格式
        not格式词汇 = [f"not {词}" for 词 in 词汇列表]

        # 用逗号连接
        return ", ".join(not格式词汇)

    def 优化参考图提示词(self, 原始提示词, 角色名=""):
        """优化参考图提示词 - 包含翻译和强化功能，强制全身像正面照"""

        # 1. 通用质量固定词（最前面）
        通用质量词 = self.获取通用质量固定词()

        # 2. 如果原始提示词包含中文，进行翻译
        翻译后提示词 = self.翻译中文提示词(原始提示词)

        # 3. 全身像正面照强制要求词汇（优先级最高）
        全身正面要求词 = [
            "full body shot", "full body portrait", "whole body", "complete figure",
            "front view", "frontal view", "facing camera", "facing forward",
            "standing pose", "front facing", "looking at camera"
        ]

        # 4. 基础参考图优化词汇
        参考图关键词 = [
            "clear face", "detailed facial features", "detailed body",
            "consistent character", "character reference", "character sheet"
        ]

        # 5. 正面强调词汇 - 提升生成质量
        正面强调词 = [
            "perfect anatomy", "beautiful eyes", "symmetrical face",
            "realistic skin texture", "natural lighting", "perfect proportions",
            "detailed clothing", "clear background"
        ]

        # 6. 如果有角色名，添加角色相关词汇
        if 角色名:
            参考图关键词.extend([f"({角色名})", "same person", "character consistency"])

        # 7. 获取负面提示词并转换为not格式
        原始负面词 = self.获取参考图负面提示词()
        not格式负面词 = self.转换负面词为not格式(原始负面词)

        # 8. 按优先级组合所有词汇：通用质量词 + 全身正面要求 + 用户提示词 + 参考图关键词 + 正面强调词 + not格式负面词
        提示词部分 = [通用质量词]
        提示词部分.extend(全身正面要求词)

        if 翻译后提示词:
            提示词部分.append(翻译后提示词)

        提示词部分.extend(参考图关键词)
        提示词部分.extend(正面强调词)

        # 添加not格式负面词到最后
        if not格式负面词:
            提示词部分.append(not格式负面词)

        # 组合最终提示词
        优化提示词 = ", ".join(提示词部分)

        print(f"✅ 本地参考图提示词优化完成，包含not格式负面词")
        return 优化提示词

    def 翻译中文提示词(self, 提示词):
        """翻译中文提示词为英文"""
        if not 提示词:
            return ""

        # 检查是否包含中文字符
        import re
        if not re.search(r'[\u4e00-\u9fff]', 提示词):
            # 不包含中文，直接返回
            return 提示词

        print(f"   🌐 检测到中文提示词，开始翻译...")

        try:
            # 尝试使用SiliconFlow翻译
            from pyz.任务运行文件.A7_SiliconFlow推理 import SiliconFlowAPI

            api = SiliconFlowAPI()
            翻译提示 = f"""请将以下中文角色描述翻译成适合AI绘画的英文提示词，要求：
1. 保持原意准确
2. 使用AI绘画常用词汇
3. 适合作为参考图生成的描述
4. 只返回翻译结果，不要其他内容

中文描述：{提示词}"""

            messages = [{"role": "user", "content": 翻译提示}]
            翻译结果 = api._make_request(messages)

            if 翻译结果:
                print(f"   ✅ 翻译成功: {翻译结果}")
                return 翻译结果.strip()
            else:
                print(f"   ⚠️  SiliconFlow翻译失败，使用简单映射")
                return self.简单中英映射(提示词)

        except Exception as e:
            print(f"   ⚠️  翻译服务异常: {e}，使用简单映射")
            return self.简单中英映射(提示词)

    def 简单中英映射(self, 中文描述):
        """简单的中英文映射，作为翻译失败时的备选方案"""
        映射表 = {
            "男性": "male", "女性": "female", "男人": "man", "女人": "woman",
            "帅气": "handsome", "美丽": "beautiful", "可爱": "cute",
            "长发": "long hair", "短发": "short hair", "黑发": "black hair",
            "微笑": "smile", "笑容": "smile", "严肃": "serious",
            "年轻": "young", "中年": "middle-aged", "老年": "elderly",
            "高": "tall", "矮": "short", "瘦": "thin", "胖": "fat",
            "穿着": "wearing", "衣服": "clothes", "西装": "suit",
            "眼镜": "glasses", "帽子": "hat"
        }

        英文描述 = 中文描述
        for 中文, 英文 in 映射表.items():
            英文描述 = 英文描述.replace(中文, 英文)

        return 英文描述

    def 获取参考图负面提示词(self):
        """获取参考图专用负面提示词 - 包含完整的质量控制项"""
        return ("watermark, signature, text overlay, logo, copyright mark, artist signature, "
                "multiple hands, extra hands, bad hands, malformed hands, fused fingers, extra fingers, missing fingers, "
                "blurry, blur, out of focus, soft focus, motion blur, "
                "missing limbs, extra limbs, extra arms, extra legs, amputee, dismembered, "
                "deformed anatomy, bad anatomy, distorted anatomy, malformed body, twisted limbs, "
                "low quality, worst quality, bad quality, poor quality, low resolution, "
                "multiple people, crowd, background clutter, messy background, "
                "deformed face, asymmetrical face, distorted features, malformed features, "
                "inconsistent character, different person, face swap, "
                "cropped, cut off, partial body, incomplete figure, "
                "artifacts, compression artifacts, jpeg artifacts, noise, grain, "
                "overexposed, underexposed, oversaturated, undersaturated, "
                "cartoon, anime, illustration, painting, sketch, drawing, "
                "nsfw, nude, sexual content")

    def 生成SDXL参考图工作流(self, 模型名, 提示词, 采样器="dpmpp_2m"):
        """生成SDXL参考图专用工作流（负面词已包含在主提示词中）"""
        return {
            # 模型加载器
            "4": {
                "inputs": {
                    "ckpt_name": 模型名
                },
                "class_type": "CheckpointLoaderSimple",
                "_meta": {"title": "模型加载器"}
            },

            # 空白潜在图像 - SDXL全身像适配尺寸
            "5": {
                "inputs": {
                    "width": 832,
                    "height": 1216,
                    "batch_size": 1
                },
                "class_type": "EmptyLatentImage",
                "_meta": {"title": "空白潜在图像"}
            },

            # 正向提示词编码器（包含not格式负面词）
            "6": {
                "inputs": {
                    "text": 提示词,
                    "clip": ["4", 1]
                },
                "class_type": "CLIPTextEncode",
                "_meta": {"title": "正向提示词（含not格式负面词）"}
            },

            # 空的负向提示词编码器（不使用负面词）
            "7": {
                "inputs": {
                    "text": "",
                    "clip": ["4", 1]
                },
                "class_type": "CLIPTextEncode",
                "_meta": {"title": "空负向提示词"}
            },

            # KSampler - 参考图优化参数
            "3": {
                "inputs": {
                    "seed": self.生成随机种子(),  # 使用随机种子
                    "steps": 25,  # 增加步数提高质量
                    "cfg": 8.0,   # 适中的CFG
                    "sampler_name": 采样器,
                    "scheduler": "karras",
                    "denoise": 1.0,
                    "model": ["4", 0],
                    "positive": ["6", 0],
                    "negative": ["7", 0],
                    "latent_image": ["5", 0]
                },
                "class_type": "KSampler",
                "_meta": {"title": "采样器"}
            },

            # VAE解码器
            "8": {
                "inputs": {
                    "samples": ["3", 0],
                    "vae": ["4", 2]
                },
                "class_type": "VAEDecode",
                "_meta": {"title": "VAE解码器"}
            },

            # 保存图像
            "9": {
                "inputs": {
                    "filename_prefix": "参考图_SDXL_",
                    "images": ["8", 0]
                },
                "class_type": "SaveImage",
                "_meta": {"title": "保存图像"}
            }
        }

    def 生成SD15参考图工作流(self, 模型名, 提示词, 采样器="dpmpp_2m"):
        """生成SD1.5参考图专用工作流（负面词已包含在主提示词中）"""
        return {
            # 模型加载器
            "4": {
                "inputs": {
                    "ckpt_name": 模型名
                },
                "class_type": "CheckpointLoaderSimple",
                "_meta": {"title": "模型加载器"}
            },

            # 空白潜在图像 - SD1.5全身像适配尺寸
            "5": {
                "inputs": {
                    "width": 384,
                    "height": 640,
                    "batch_size": 1
                },
                "class_type": "EmptyLatentImage",
                "_meta": {"title": "空白潜在图像"}
            },

            # 正向提示词编码器（包含not格式负面词）
            "6": {
                "inputs": {
                    "text": 提示词,
                    "clip": ["4", 1]
                },
                "class_type": "CLIPTextEncode",
                "_meta": {"title": "正向提示词（含not格式负面词）"}
            },

            # 空的负向提示词编码器（不使用负面词）
            "7": {
                "inputs": {
                    "text": "",
                    "clip": ["4", 1]
                },
                "class_type": "CLIPTextEncode",
                "_meta": {"title": "空负向提示词"}
            },

            # KSampler - 参考图优化参数
            "3": {
                "inputs": {
                    "seed": self.生成随机种子(),  # 使用随机种子
                    "steps": 30,  # SD1.5需要更多步数
                    "cfg": 7.5,   # SD1.5标准CFG
                    "sampler_name": 采样器,
                    "scheduler": "karras",
                    "denoise": 1.0,
                    "model": ["4", 0],
                    "positive": ["6", 0],
                    "negative": ["7", 0],
                    "latent_image": ["5", 0]
                },
                "class_type": "KSampler",
                "_meta": {"title": "采样器"}
            },

            # VAE解码器
            "8": {
                "inputs": {
                    "samples": ["3", 0],
                    "vae": ["4", 2]
                },
                "class_type": "VAEDecode",
                "_meta": {"title": "VAE解码器"}
            },

            # 保存图像
            "9": {
                "inputs": {
                    "filename_prefix": "参考图_SD15_",
                    "images": ["8", 0]
                },
                "class_type": "SaveImage",
                "_meta": {"title": "保存图像"}
            }
        }

    def 生成Flux参考图工作流(self, 模型名, 提示词):
        """生成Flux参考图专用工作流（负面词已包含在主提示词中）"""
        # Flux模型的特殊配置
        return self.生成SDXL参考图工作流(模型名, 提示词)



    def 生成云端参考图工作流(self, 提示词="", 角色名=""):
        """
        生成云端参考图专用工作流
        使用专门的云端参考图工作流
        """
        print("☁️  调用云端参考图专用工作流")

        # 导入云端参考图专用工作流
        from pyz.工作流.云端参考图专用工作流 import 获取云端参考图专用工作流

        return 获取云端参考图专用工作流(提示词, 角色名)

# 便捷函数
def 获取参考图专用工作流(模型名="", 提示词="", 角色名=""):
    """
    获取参考图专用工作流

    Args:
        模型名: 目标模型名称
        提示词: 角色描述提示词
        角色名: 角色名称

    Returns:
        dict: ComfyUI工作流配置
    """
    工作流生成器 = 参考图专用工作流()
    return 工作流生成器.生成参考图工作流(模型名, 提示词, 角色名)

# 导出函数
__all__ = [
    '参考图专用工作流',
    '获取参考图专用工作流'
]
