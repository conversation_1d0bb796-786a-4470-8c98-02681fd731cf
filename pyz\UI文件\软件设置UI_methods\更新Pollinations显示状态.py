# 更新Pollinations显示状态方法
# 从软件设置UI.py拆分出来的方法

from pyz.任务运行文件 import A0_config

def 更新Pollinations显示状态(self):
    """根据配置文件状态更新Pollinations相关控件的显示"""
    try:
        # 确保必要的控件存在
        if not hasattr(self, '更换SD模型_label'):
            return

        # 从配置文件读取状态
        pollinations_enabled = A0_config.config.get('使用Pollinations', False)

        if pollinations_enabled:
            # 启用Pollinations：禁用本地模型，显示云端模型
            self.更换SD模型_label.setText('本地模型(已禁用)')
            if hasattr(self, '更换SD模型'):
                self.更换SD模型.setEnabled(False)

            # 显示云端模型控件
            if hasattr(self, 'Pollinations模型_label'):
                self.Pollinations模型_label.show()
            if hasattr(self, 'Pollinations模型'):
                self.Pollinations模型.show()
            if hasattr(self, 'Pollinations管理'):
                self.Pollinations管理.show()

            # 显示API密钥控件
            if hasattr(self, 'Pollinations密钥_label'):
                self.Pollinations密钥_label.show()
            if hasattr(self, 'Pollinations密钥'):
                self.Pollinations密钥.show()
            if hasattr(self, 'Pollinations密钥显示'):
                self.Pollinations密钥显示.show()

            # 显示Pollinations风格控件
            if hasattr(self, 'Pollinations风格_label'):
                self.Pollinations风格_label.show()
            if hasattr(self, 'Pollinations风格'):
                self.Pollinations风格.show()

            # 🎯 显示Pollinations风格设置控件，并自动启用风格增强
            if hasattr(self, '正面风格标题'):
                self.正面风格标题.show()
            if hasattr(self, '正面风格提示词'):
                self.正面风格提示词.show()
            if hasattr(self, '反面风格标题'):
                self.反面风格标题.show()
            if hasattr(self, '反面风格提示词'):
                self.反面风格提示词.show()
            if hasattr(self, '保存风格设置'):
                self.保存风格设置.show()
            if hasattr(self, '重置风格设置'):
                self.重置风格设置.show()

            # 🎯 自动启用风格增强，不需要用户再次勾选
            if hasattr(self, '使用Pollinations风格'):
                self.使用Pollinations风格.setChecked(True)
                A0_config.config['使用Pollinations风格增强'] = True

                # 更新风格UI状态
                if hasattr(self, 'update_pollinations_style_ui_state'):
                    self.update_pollinations_style_ui_state()

        else:
            # 禁用Pollinations：启用本地模型，隐藏云端模型
            self.更换SD模型_label.setText('绘图模型')
            if hasattr(self, '更换SD模型'):
                self.更换SD模型.setEnabled(True)

            # 隐藏云端模型控件
            if hasattr(self, 'Pollinations模型_label'):
                self.Pollinations模型_label.hide()
            if hasattr(self, 'Pollinations模型'):
                self.Pollinations模型.hide()
            if hasattr(self, 'Pollinations管理'):
                self.Pollinations管理.hide()

            # 隐藏API密钥控件
            if hasattr(self, 'Pollinations密钥_label'):
                self.Pollinations密钥_label.hide()
            if hasattr(self, 'Pollinations密钥'):
                self.Pollinations密钥.hide()
            if hasattr(self, 'Pollinations密钥显示'):
                self.Pollinations密钥显示.hide()

            # 隐藏Pollinations风格控件
            if hasattr(self, 'Pollinations风格_label'):
                self.Pollinations风格_label.hide()
            if hasattr(self, 'Pollinations风格'):
                self.Pollinations风格.hide()

            # 🎯 隐藏Pollinations风格设置控件，并禁用风格增强
            if hasattr(self, '正面风格标题'):
                self.正面风格标题.hide()
            if hasattr(self, '正面风格提示词'):
                self.正面风格提示词.hide()
            if hasattr(self, '反面风格标题'):
                self.反面风格标题.hide()
            if hasattr(self, '反面风格提示词'):
                self.反面风格提示词.hide()
            if hasattr(self, '保存风格设置'):
                self.保存风格设置.hide()
            if hasattr(self, '重置风格设置'):
                self.重置风格设置.hide()

            # 🎯 自动禁用风格增强
            if hasattr(self, '使用Pollinations风格'):
                self.使用Pollinations风格.setChecked(False)
                A0_config.config['使用Pollinations风格增强'] = False

                # 更新风格UI状态
                if hasattr(self, 'update_pollinations_style_ui_state'):
                    self.update_pollinations_style_ui_state()

    except Exception as e:
        print(f"[错误] 更新Pollinations显示状态失败: {e}")
        import traceback
        traceback.print_exc()
