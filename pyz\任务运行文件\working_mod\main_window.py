import base64
import gc
import io
import json
import os as jduvudu312us_usjlq
import random
import re
import shutil
import sys
import tempfile
import threading
import time
import uuid
from io import BytesIO
from tkinter import messagebox
import soundfile as sf
import chardet
import cv2
import requests
from urllib.parse import urlparse
from PIL import Image
from PyQt5 import QtCore
from PyQt5.QtCore import *
from PyQt5.QtCore import Qt, QUrl
from PyQt5.QtGui import QPixmap, QIcon, QDesktopServices, QKeySequence, QCursor, QImage, QStandardItemModel, QStandardItem, QContextMenuEvent, QTextCursor, QFont, QColor
from PyQt5.QtWidgets import QApplication, QWidget, QPushButton, QSpacerItem, QProgressBar, QAction, QMenu, QFrame, QStackedWidget, QListView, QRadioButton, QLayout, QSplitter
from PyQt5.QtWidgets import QHeaderView, QVBoxLayout, QSizePolicy, QDialog, QLineEdit, QLabel, QGridLayout, QGroupBox, QFileDialog, QAbstractItemView, QTextEdit, QDesktopWidget, QMessageBox
from PyQt5.QtWidgets import QMainWindow, QTableWidget, QTableWidgetItem, QHBoxLayout, QComboBox, QScrollArea
from moviepy.video.io.VideoFileClip import VideoFileClip
from pyz.UI文件.多行输入框组件 import Custom_TextEdit
from pyz.UI文件.广告位 import AdWidget
from pyz.UI文件.日志窗口 import LogWindow
from pyz.UI文件.菜单栏 import 菜单栏
from pyz.UI文件.视频转绘页 import 视频转绘组件
from pyz.UI文件.软件设置UI import ruanjianshezhi
from pyz.UI文件.镜头设置UI import jingtoushezhi
from pyz.UI文件.预览图片窗口 import PreviewImageDialog
from pyz.任务运行文件.A0_通用函数 import 文件名最大数
from pyz.任务运行文件.A24_会员充值 import ExampleDialog
from pyz.任务运行文件.A29_自用工作流 import Comfyui_t2i
from pyz.任务运行文件.A31_数字人 import 口型对齐
from pyz.任务运行文件.云端SD_MJ import SDAPI
from pyz.任务运行文件.文视GPT推理 import 端口, Runthread_GPT
from pyz.任务运行文件 import A4_人物设定, A2_登录注册充值, A0_config, A16_style
from pyz.UI文件.标题栏 import TitleBar
from pyz.任务运行文件.tags import tags
from pyz.任务运行文件.新建项目 import xinjianxiangmu, up_zjsy
from pyz.任务运行文件.单个任务 import Thread_huitu
from pyz.任务运行文件.批量任务 import Runthread
from pyz.UI文件.拆分项目 import chaifenxiangmu
from pyz.任务运行文件.A1_Split_Task import 生成项目文档, 生成字幕文件
from pyz.任务运行文件.A3_jiamijiemi import 加密数据, 解密数据
from pyz.任务运行文件.阿里云翻译 import Sample
from pyz.任务运行文件.working_mod.custom_scroll_area import CustomScrollArea
from pyz.任务运行文件.working_mod.custom_text_edit import CustomTextEdit
from pyz.任务运行文件.working_mod.custom_text_edit_shipin import CustomTextEdit_shipin
from pyz.任务运行文件.working_mod.custom_text_edit_content import CustomTextEdit_content
class MainWindow(QMainWindow):
    def __init__(self, 端口, width, height, 文字大小):
        super().__init__()
        self.A0_config = A0_config

        # 首先设置尺寸参数，确保缩放方法可用
        self.width_s, self.height_s, self.文字大小 = (width, height, 文字大小)
        self.比例 = self.height_s / 1080  # 提前计算比例

        from PyQt5.QtGui import QFont
        from PyQt5.QtWidgets import QApplication
        font = QFont()
        font.setPointSize(20)  # 你想要的字号
        font.setFamily('Microsoft YaHei')
        if QApplication.instance() is not None:
         QApplication.instance().setFont(font)
        self.lora = {}
        self.文字样式 = f'color: {A0_config.文字色}; font-size: {文字大小}px;'
        self.kuandu = self.缩放(250)
        self.端口 = 端口
        self.云端部署链接 = 'https://www.xiangongyun.com/image/detail/29f1cf38-8d7c-4bcc-b3c6-848a36f41274?r=AS51AR'
        if self.端口 == 'yun':
            self.云端部署链接 = 'https://xiangongyun.com/image/detail/2b554e69-c647-4684-b16b-4d46e4af9765?r=AS51AR'
            self.kuandu = self.缩放(300)
            A0_config.config['推理模型'] = '克隆模式'
            A0_config.config['语音平台'] = '微软语音'
        else:  # inserted
            if self.端口 == 'zong':
                self.云端部署链接 = 'https://xiangongyun.com/image/detail/64cf7dc6-4fbe-4871-a745-f28b8901e6ee?r=AS51AR'
            else:  # inserted
                if self.端口 == 'zhang':
                    self.云端部署链接 = 'https://www.xiangongyun.com/console/instance?r=AS51AR'
        if not A0_config.NOVIP and 端口 == '' and (A0_config.name_gpt in A0_config.config['ChatGPT端口']):
            if A0_config.API == 'http://wsai.b50.cc':
                A0_config.config['推理增强'] = True
        if A0_config.API!= 'http://wsai.b50.cc':
            A0_config.config['推理增强'] = False
            self.推理增强模式 = False
        self.textbox_to_row = {}
        self.中文推理 = True
        self.推理增强模式 = A0_config.config.get('推理增强')
        self.column = None
        self.row = None
        self.editing_enabled = False
        self.转绘调试 = False
        self.zhuxian = False
        self.running = False
        self.功能 = ''
        self.huitu = False
        self.停止 = False
        self.tanchuang = False
        self.stop_run = True
        self.增加 = False
        self.yu = False
        self.人物设定 = False
        self.日志窗口 = False
        self.设置窗口 = False
        self.语音字数 = 0
        self.单境字数 = 15
        self.当前任务 = ''
        self.segments = []
        self.tts_token = ''
        self.subscription_key = ''
        self.subscription_token = ''
        self.上一个模型 = ''
        self.region = ''
        self.当前进度 = 0
        self.任务总数 = 1
        self.导入剪映 = False
        self.openpos = ''
        self.canny = ''
        self.mlsd = ''
        self.lineart = ''
        self.ip_adapter = ''
        self.当前任务 = ''
        self.图片编号行 = 0
        self.内容行 = 1
        self.修改文案行 = 2
        self.原图片行 = 3
        self.正面词行 = 4
        self.图片行 = 5
        self.操作行 = 6
        self.备选图片行 = 7
        self.分镜行 = 8
        self.语音行 = 9
        self.动图提示词行 = 10
        self.单次任务 = ''
        self.运行行数 = 0
        self.worker = Runthread(main_win=self)
        self.worker_solo = Thread_huitu(main_win=self, mood=self)
        self.worker_solo._signal.connect(self.huitu_backlog)
        self.worker_solo.start()
        self.worker_gpt = Runthread_GPT(main_win=self)
        self.项目文件夹 = jduvudu312us_usjlq.path.join(jduvudu312us_usjlq.getcwd(), '项目文件')
        self.网络视频文件夹 = jduvudu312us_usjlq.path.join(self.项目文件夹, '网络视频')
        self.转场文件夹 = jduvudu312us_usjlq.path.join(jduvudu312us_usjlq.getcwd(), 'effect', 'transition/')
        self.特效文件夹 = jduvudu312us_usjlq.path.join(jduvudu312us_usjlq.getcwd(), 'effect', 'video_effect/')
        self.当前项目 = ''
        self.项目配置 = {}
        self.设备号 = uuid.UUID(int=uuid.getnode()).hex[(-12):]
        self.项目id = 28
        self.要求长度 = 20
        self.行高 = self.缩放(30)
        self.任务模式 = A0_config.config['推理模型']
        self.setSizePolicy(QSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding))
        self.setStyleSheet(self.设置布局())
        self.title_bar = TitleBar(self)
        self.setMenuWidget(self.title_bar)
        self.centralwidget = QWidget(self)
        screen = QDesktopWidget().screenGeometry()
        self.max_width = screen.width()
        self.max_height = screen.height()
        self.centralwidget.setMaximumSize(self.max_width, self.max_height - self.缩放(80))
        self.全局主窗口 = QFrame(self.centralwidget)
        self.全局主窗口.setFrameShape(QFrame.StyledPanel)
        self.全局主窗口.setFrameShadow(QFrame.Raised)
        self.右伸缩条 = QFrame(self.全局主窗口)
        self.右伸缩条.setMinimumSize(QtCore.QSize(5, 0))
        self.右伸缩条.setMaximumSize(QtCore.QSize(5, 16777215))
        self.右伸缩条.setCursor(QCursor(Qt.SizeHorCursor))
        self.右伸缩条.setFrameShape(QFrame.StyledPanel)
        self.右伸缩条.setFrameShadow(QFrame.Raised)
        self.底部伸缩条 = QFrame(self.全局主窗口)
        self.底部伸缩条.setMinimumSize(QtCore.QSize(0, 5))
        self.底部伸缩条.setMaximumSize(QtCore.QSize(16777215, 5))
        self.底部伸缩条.setCursor(QCursor(Qt.SizeVerCursor))
        self.底部伸缩条.setFrameShape(QFrame.StyledPanel)
        self.底部伸缩条.setFrameShadow(QFrame.Raised)
        self.角伸缩条 = QFrame(self.全局主窗口)
        self.角伸缩条.setMinimumSize(QtCore.QSize(5, 5))
        self.角伸缩条.setMaximumSize(QtCore.QSize(5, 5))
        self.角伸缩条.setCursor(QCursor(Qt.SizeFDiagCursor))
        self.角伸缩条.setFrameShape(QFrame.StyledPanel)
        self.角伸缩条.setFrameShadow(QFrame.Raised)
        self.创建ui()
        self.创建菜单栏()
        self.创建辅助工具页面()
        self.批量挂机页面()
        self.创建视频转绘页面()
        self.工作组件 = QStackedWidget(self)
        self.工作组件.addWidget(self.文转视频工作窗口)
        self.工作组件.addWidget(self.辅助工具窗口)
        self.工作组件.addWidget(self.视频转绘窗口)
        self.工作组件.addWidget(self.批量挂机窗口)
        self.run_code()
        if self.任务模式 == '原创模式':
            self.工作组件.setCurrentIndex(0)
            self.菜单栏组件.菜单栏.setCurrentRow(0)
        else:  # inserted
            if self.任务模式 == '克隆模式':
                self.工作组件.setCurrentIndex(0)
                self.菜单栏组件.菜单栏.setCurrentRow(1)
            else:  # inserted
                if self.任务模式 == '视频转绘':
                    self.工作组件.setCurrentIndex(0)
                    self.菜单栏组件.菜单栏.setCurrentRow(2)
        self.操作组件 = QFrame()
        self.中间垂直布局 = QVBoxLayout(self.操作组件)
        self.中间垂直布局.setContentsMargins(0, 0, 0, 0)
        self.中间垂直布局.setSpacing(0)
        # 广告组件 = AdWidget(self)
        self.中间垂直布局.addLayout(self.第一行)
        self.中间垂直布局.addLayout(self.第二行)
        # self.中间垂直布局.addWidget(广告组件)
        # 注释掉广告组件的隐藏操作，因为广告组件已被禁用
        # if not A0_config.NOVIP or self.端口 == 'yun' or self.端口 == 'zong':
        #     广告组件.hide()
        self.中间垂直布局.addWidget(self.工作组件)
        self.中间垂直布局.addWidget(self.进度条)
        self.中间垂直布局.addWidget(self.底部伸缩条)
        self.右侧伸缩条布局 = QVBoxLayout()
        self.右侧伸缩条布局.setContentsMargins(0, 0, 0, 0)
        self.右侧伸缩条布局.addWidget(self.右伸缩条)
        self.右侧伸缩条布局.addWidget(self.角伸缩条)
        splitter = QSplitter(Qt.Orientation.Horizontal)
        splitter.addWidget(self.菜单栏组件)
        splitter.addWidget(self.操作组件)
        splitter.setStretchFactor(0, 1)
        splitter.setStretchFactor(1, 4)
        self.全局水平布局 = QHBoxLayout(self.全局主窗口)
        self.全局水平布局.setContentsMargins(0, 0, 0, 0)
        self.全局水平布局.addWidget(splitter)
        self.全局水平布局.addLayout(self.右侧伸缩条布局)
        self.centralwidget.setLayout(self.全局水平布局)
        self.setCentralWidget(self.centralwidget)
        self.start_x = None
        self.start_y = None
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setWindowFlags(Qt.FramelessWindowHint)
        self.evn = 0
        print('界面加载完成')
        self.用户名 = A0_config.config['激活码']
        self.通用正面词 = A0_config.prompt
        self.通用反面词 = A0_config.negative_prompt
        self.进度条.hide()
        self.当前项目 = self.重绘列表.currentText()
        if self.当前项目!= '':
            if self.任务模式 == '原创模式':
                self.当前项目文件夹 = jduvudu312us_usjlq.path.join(self.项目文件夹, '原创项目', self.当前项目)
            else:  # inserted
                if self.任务模式 == '克隆模式':
                    self.当前项目文件夹 = jduvudu312us_usjlq.path.join(self.项目文件夹, '克隆项目', self.当前项目)
                else:  # inserted
                    self.当前项目文件夹 = jduvudu312us_usjlq.path.join(self.项目文件夹, '转绘项目', self.当前项目)
            self.图片文件夹 = jduvudu312us_usjlq.path.join(self.当前项目文件夹, '图片文件')
            self.视频文件夹 = jduvudu312us_usjlq.path.join(self.当前项目文件夹, '视频文件')
            self.原始图片文件夹 = jduvudu312us_usjlq.path.join(self.当前项目文件夹, '原始图片')
            self.音频文件夹 = jduvudu312us_usjlq.path.join(self.当前项目文件夹, '音频文件')
        self.任务模式切换事件()
        self.隐藏组件()
        self.运行检测()

        # 设置全局字体大小
        from PyQt5.QtWidgets import QApplication
        app = QApplication.instance()
        if app is not None:
            app.setStyleSheet("QPushButton, QComboBox, QCheckBox, QLabel { font-size: 24px !important; }")

        # 单独设置顶部按钮字体
        self.应用顶部按钮字体()

        # 调用调试方法
        self.调试字体信息()

        # 在所有UI创建完成后初始化背景图片
        from PyQt5.QtCore import QTimer
        QTimer.singleShot(100, lambda: self.初始化背景图片())

        # 添加定期检查背景图片的定时器，确保背景图片不会消失
        self.background_check_timer = QTimer()
        self.background_check_timer.timeout.connect(self.check_and_restore_background)
        self.background_check_timer.start(5000)  # 每5秒检查一次

        # 新增：默认seed及分镜
        self.default_seed = None
        self.default_seed_fenjing = None

        # 从项目配置中加载保存的seed值
        self.加载项目seed配置()

        # 检查并应用浏览器绘图方案拦截器
        self.初始化浏览器绘图拦截器()

    def 加载项目seed配置(self):
        """从项目JSON文件中加载保存的seed配置"""
        try:
            # 加载角色专用种子配置
            if hasattr(self, '项目配置') and self.项目配置 and '角色专用种子配置' in self.项目配置:
                角色种子配置 = self.项目配置['角色专用种子配置']
                print(f"已加载角色专用种子配置，包含 {len(角色种子配置)} 个角色:")
                for 角色名, 配置 in 角色种子配置.items():
                    种子值 = 配置.get('seed', '未设置')
                    启用状态 = '启用' if 配置.get('enabled', False) else '禁用'
                    更新时间 = 配置.get('last_updated', '未知')
                    print(f"  - {角色名}: seed={种子值} ({启用状态}, 更新时间: {更新时间})")


        except Exception as e:
            print(f"加载项目seed配置时出错: {e}")

    def 获取角色专用种子(self, 角色名):
        """获取指定角色的专用种子"""
        try:
            # 从角色专用种子配置中获取种子值
            if hasattr(self, '项目配置') and self.项目配置 and '角色专用种子配置' in self.项目配置:
                角色配置 = self.项目配置['角色专用种子配置'].get(角色名, {})
                if 角色配置.get('enabled', False):
                    种子值 = 角色配置.get('seed')
                    if 种子值 is not None:
                        print(f"   ✅ 使用角色[{角色名}]的专用种子: {种子值}")
                        return 种子值

            print(f"   ❌ 角色[{角色名}]没有启用的专用种子")
            return None

        except Exception as e:
            print(f"获取角色专用种子时出错: {e}")
            return None

    def 初始化浏览器绘图拦截器(self):
        """初始化浏览器绘图方案拦截器"""
        try:
            # 检查是否启用了浏览器绘图方案A
            if A0_config.config.get('浏览器绘图方案', False):
                print("🎨 检测到浏览器绘图方案A已启用，正在应用拦截器...")

                from pyz.任务运行文件.浏览器绘图方案.interceptor import 应用拦截器到主窗口
                if 应用拦截器到主窗口(self):
                    print("✅ 浏览器绘图方案A拦截器已在主窗口初始化时应用")
                else:
                    print("❌ 浏览器绘图方案A拦截器应用失败")

            # 检查是否启用了浏览器绘图方案B
            elif A0_config.config.get('浏览器绘图方案B', False):
                print("🎨 检测到浏览器绘图方案B已启用，正在应用拦截器...")

                from pyz.任务运行文件.浏览器绘图方案B.interceptor import 应用拦截器到主窗口
                if 应用拦截器到主窗口(self):
                    print("✅ 浏览器绘图方案B拦截器已在主窗口初始化时应用")
                else:
                    print("❌ 浏览器绘图方案B拦截器应用失败")

            # 检查是否启用了浏览器绘图方案C
            elif A0_config.config.get('浏览器绘图方案C', False):
                print("🎨 检测到浏览器绘图方案C已启用，正在应用拦截器...")

                from pyz.任务运行文件.浏览器绘图方案C.interceptor import 应用拦截器到主窗口C
                if 应用拦截器到主窗口C(self):
                    print("✅ 浏览器绘图方案C拦截器已在主窗口初始化时应用")
                else:
                    print("❌ 浏览器绘图方案C拦截器应用失败")

            else:
                print("🔄 浏览器绘图方案未启用，使用默认绘图流程")

        except Exception as e:
            print(f"❌ 初始化浏览器绘图拦截器时出错: {e}")

    def 调试字体信息(self):
        """打印主界面各部分控件的字体信息到控制台"""
        from PyQt5.QtWidgets import QPushButton, QComboBox, QCheckBox, QLabel, QApplication

        def print_widget_info(widget, name="控件"):
            font = widget.font()
            style = widget.styleSheet()
            text = getattr(widget, 'text', lambda: '')() if hasattr(widget, 'text') else ''
            print(f"[{name}] {widget.__class__.__name__} text='{text}' font-size={font.pointSize()} family={font.family()} style={style[:100]}...")

        # print("\n=== 主界面控件字体调试信息 ===")

        # # 1. 检查全局字体设置
        # app = QApplication.instance()
        # app_font = app.font() if app else None
        # if app_font:
        #     print(f"[全局应用字体] size={app_font.pointSize()} family={app_font.family()}")

        # # 2. 检查主窗口字体
        # main_font = self.font()
        # print(f"[主窗口字体] size={main_font.pointSize()} family={main_font.family()}")

        # # 3. 检查菜单栏组件
        # if hasattr(self, '菜单栏组件'):
        #     print("\n--- 菜单栏区域 ---")
        #     print_widget_info(self.菜单栏组件, "菜单栏组件")
        #     for child in self.菜单栏组件.findChildren((QPushButton, QComboBox, QCheckBox, QLabel)):
        #         print_widget_info(child, f"菜单栏子控件")

        # # 4. 检查顶部按钮区
        # print("\n--- 顶部按钮区 ---")
        # 顶部按钮列表 = ['SD版本', '新建任务', '重绘列表', '加载任务', '删除任务', '复制任务', '一键成片',
        #             '销毁镜像', '云端部署', '增加分镜', '设置分镜', '批量推理', '批量改写',
        #             '批量语音', '批量绘图', '批量放大', '视频推理', '批量视频', '合成视频',
        #             '打开文件', '停止运行']

        # for btn_name in 顶部按钮列表:
        #     if hasattr(self, btn_name):
        #         print_widget_info(getattr(self, btn_name), btn_name)

        # # 5. 检查表格区域
        # print("\n--- 表格区域 ---")
        # if hasattr(self, 'table_widget'):
        #     print_widget_info(self.table_widget, "主表格")
        # if hasattr(self, '计划列表'):
        #     print_widget_info(self.计划列表, "计划列表表格")

        # # 6. 检查工作组件区域
        # print("\n--- 工作组件区域 ---")
        # if hasattr(self, '工作组件'):
        #     print_widget_info(self.工作组件, "工作组件")
        #     for child in self.工作组件.findChildren((QPushButton, QComboBox, QCheckBox, QLabel)):
        #         text = getattr(child, 'text', lambda: '')() if hasattr(child, 'text') else ''
        #         if text:  # 只打印有文字的控件
        #             print_widget_info(child, f"工作区控件")

        # # 7. 检查所有控件的实际渲染字体
        # print("\n--- 所有控件统计 ---")
        # all_buttons = self.findChildren(QPushButton)
        # all_combos = self.findChildren(QComboBox)
        # all_labels = self.findChildren(QLabel)
        # print(f"找到按钮数量: {len(all_buttons)}")
        # print(f"找到下拉框数量: {len(all_combos)}")
        # print(f"找到标签数量: {len(all_labels)}")

        # print("=== 调试信息结束 ===\n")

    def 应用顶部按钮字体(self):
        """强制设置顶部按钮的字体大小"""
        顶部按钮列表 = ['SD版本', '新建任务', '重绘列表', '加载任务', '删除任务', '复制任务', '一键成片',
                    '销毁镜像', '云端部署', '增加分镜', '设置分镜', '批量推理', '批量改写',
                    '批量语音', '批量绘图', '批量放大', '视频推理', '批量视频', '合成视频',
                    '打开文件', '停止运行']

        for btn_name in 顶部按钮列表:
            if hasattr(self, btn_name):
                btn = getattr(self, btn_name)
                current_style = btn.styleSheet()
                if 'font-size' in current_style:
                    # 移除现有的font-size设置
                    import re
                    current_style = re.sub(r'font-size:\s*\d+px;?', '', current_style)
                # 添加新的字体大小
                new_style = current_style + '; font-size: 24px !important;'
                btn.setStyleSheet(new_style)
                # print(f"已设置 {btn_name} 字体为24px")

    def 运行检测(self):
        thread = threading.Thread(target=A0_config.获取账号信息, args=(self,))
        thread.start()

    def 隐藏组件(self):
        if self.端口 == 'yun':
            self.工作组件.setCurrentIndex(0)
            self.菜单栏组件.菜单栏.setCurrentRow(1)
            self.菜单栏组件.视频原创.setHidden(True)
            self.菜单栏组件.视频转绘.setHidden(True)
            self.菜单栏组件.使用教程.setHidden(True)
            self.菜单栏组件.联系我们.setHidden(True)
            self.菜单栏组件.推文模型.setHidden(True)
            self.菜单栏组件.会员群.setHidden(True)
            self.菜单栏组件.文视官网.setHidden(True)
            self.菜单栏组件.菜单栏.setFixedHeight(self.缩放(160))
            self.设置分镜.setHidden(True)
            self.批量推理.setHidden(True)
            self.批量绘图.setHidden(True)
            self.批量放大.setHidden(True)
            self.小说改文组件.setHidden(True)
        else:  # inserted
            if self.端口 == 'zong':
                self.菜单栏组件.使用教程.setHidden(True)
                self.菜单栏组件.文视官网.setHidden(True)
                self.菜单栏组件.会员群.setHidden(True)
            else:  # inserted
                if self.端口 == 'zhang':
                    self.菜单栏组件.使用教程.setHidden(True)
                    self.菜单栏组件.文视官网.setHidden(True)
                    self.菜单栏组件.会员群.setHidden(True)
                    self.菜单栏组件.升级会员.setHidden(True)
                    self.菜单栏组件.联系我们.setHidden(True)
                    self.菜单栏组件.推文模型.setHidden(True)

    def 创建ui(self):
        self.顶部按钮()
        self.创建顶部布局()
        self.进度条 = QProgressBar(self)
        self.进度条.setFixedHeight(self.缩放(20))
        self.进度条.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.进度条.setFormat('%p%')  # 使用%p%显示百分比
        self.进度条.setMaximum(100)
        # 设置进度条样式，确保文字左对齐显示
        self.进度条.setStyleSheet(f'''
            QProgressBar {{
                text-align: left;
                padding-left: 5px;
                color: {A0_config.文字色};
                border: 1px solid {A0_config.边框色};
                border-radius: 3px;
                background-color: {A0_config.按钮色};
            }}
            QProgressBar::chunk {{
                background-color: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4CAF50, stop:1 #45a049);
                border-radius: 2px;
            }}
        ''')
        self.table_widget = QTableWidget(self)
        self.table_widget.setVerticalScrollMode(QAbstractItemView.ScrollPerPixel)
        vertical_scroll_bar = self.table_widget.verticalScrollBar()
        vertical_scroll_bar.setSingleStep(self.缩放(15))
        horizontal_scroll_bar = self.table_widget.horizontalScrollBar()
        horizontal_scroll_bar.setSingleStep(self.缩放(15))
        # 禁用水平滚动条，强制表格内容适应可用宽度
        self.table_widget.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.table_widget.setContextMenuPolicy(Qt.CustomContextMenu)
        self.table_widget.customContextMenuRequested.connect(self.showContextMenu)
        self.table_widget.cellDoubleClicked.connect(self.Double_click)
        self.文转视频工作窗口 = QFrame(self)
        垂直布局 = QVBoxLayout(self.文转视频工作窗口)
        垂直布局.setContentsMargins(self.缩放(10), 0, self.缩放(0), 0)
        垂直布局.addWidget(self.table_widget)

    def 批量挂机页面(self):
        self.创建计划任务布局()
        self.批量挂机窗口 = QFrame(self)
        批量挂机窗口布局 = QVBoxLayout(self.批量挂机窗口)
        批量挂机窗口布局.addWidget(self.批量计划任务)

    def 创建辅助工具页面(self):
        下载视频组 = QGroupBox('下载网络视频', self)
        下载视频布局 = QHBoxLayout(下载视频组)
        下载视频布局.setContentsMargins(0, 0, 0, 0)
        下载视频布局.setSpacing(self.缩放(30))
        self.视频链接 = QLineEdit()
        self.视频链接.setPlaceholderText('输入网络视频地址,支持国内外各大主流平台')
        self.视频链接.setFixedHeight(int(self.行高 * 2))
        下载视频 = QPushButton('下载网络视频')
        下载视频.setFixedHeight(int(self.行高 * 2))
        下载视频.clicked.connect(lambda: self.执行任务('下载网络视频'))
        打开文件夹 = QPushButton('打开视频文件夹')
        打开文件夹.setFixedHeight(int(self.行高 * 2))
        打开文件夹.clicked.connect(lambda: self.open_folder(self.网络视频文件夹))
        下载视频布局.addWidget(self.视频链接)
        下载视频布局.addWidget(下载视频)
        下载视频布局.addWidget(打开文件夹)
        self.小说改文页面()
        辅助工具窗口布局 = QVBoxLayout()
        下载视频布局.setContentsMargins(self.缩放(20), self.缩放(20), self.缩放(20), self.缩放(20))
        下载视频布局.setSpacing(self.缩放(30))
        辅助工具窗口布局.addWidget(下载视频组)
        辅助工具窗口布局.addWidget(self.小说改文组件)
        self.辅助工具窗口 = QFrame(self)
        self.辅助工具窗口.setLayout(辅助工具窗口布局)

    def 小说改文页面(self):
        self.小说改文组件 = QGroupBox(self)
        水平布局 = QHBoxLayout(self.小说改文组件)
        垂直布局1 = QVBoxLayout()
        self.小说原文 = Custom_TextEdit(self)
        self.小说原文.setPlaceholderText(f'请输入小说原文, {A0_config.name_gpt}GPT修改小说文案')
        垂直布局1.addWidget(self.小说原文)
        self.修改指令 = Custom_TextEdit(self)
        self.修改指令.setPlaceholderText('输入你的问题或要求, 留空会用默认指令')
        self.修改指令.setFixedHeight(5 * self.修改指令.fontMetrics().lineSpacing())
        垂直布局1.addWidget(self.修改指令)
        垂直布局 = QVBoxLayout()
        垂直布局.setContentsMargins(self.缩放(10), 0, self.缩放(10), 0)
        self.开始改文 = QPushButton('①改写文案')
        self.开始改文.clicked.connect(lambda: self.改写文案())
        self.开始改文.setFixedHeight(int(self.行高 * 1.5))
        self.开始改文.setFixedWidth(self.缩放(140))
        self.开始改文.setStyleSheet("font-size: 20px;")
        垂直布局.addWidget(self.开始改文)
        self.转到原创 = QPushButton('②新建任务')
        self.转到原创.clicked.connect(lambda: self.转原创任务())
        self.转到原创.setFixedHeight(int(self.行高 * 1.5))
        self.转到原创.setFixedWidth(self.缩放(140))
        self.转到原创.setStyleSheet("font-size: 20px;")
        垂直布局.addWidget(self.转到原创)
        self.提取角色 = QPushButton('③提取角色')
        self.提取角色.clicked.connect(lambda: self.提取小说角色())
        self.提取角色.setFixedHeight(int(self.行高 * 1.5))
        self.提取角色.setFixedWidth(self.缩放(140))
        self.提取角色.setStyleSheet("font-size: 20px;")
        垂直布局.addWidget(self.提取角色)
        self.一键增加分镜 = QPushButton('④增加分镜')
        self.一键增加分镜.clicked.connect(lambda: self.一键增加角色分镜())
        self.一键增加分镜.setFixedHeight(int(self.行高 * 1.5))
        self.一键增加分镜.setFixedWidth(self.缩放(140))
        self.一键增加分镜.setStyleSheet("font-size: 20px;")
        垂直布局.addWidget(self.一键增加分镜)
        垂直布局2 = QVBoxLayout()
        垂直布局2.addItem(QSpacerItem(0, 5, QSizePolicy.Minimum, QSizePolicy.Expanding))
        垂直布局2.addLayout(垂直布局)
        垂直布局2.addItem(QSpacerItem(0, 5, QSizePolicy.Minimum, QSizePolicy.Expanding))
        self.修改后内容 = Custom_TextEdit(self)
        self.修改后内容.setPlaceholderText('修改后的内容, 改文后需要用到请先新建任务, 然后再提取角色, 历史记录请在日志文件中')
        水平布局.addLayout(垂直布局1)
        水平布局.addLayout(垂直布局2)
        水平布局.addWidget(self.修改后内容)

    def 一键增加角色分镜(self):
        try:
            # 检查是否为离线模式，离线模式跳过VIP检查
            离线模式 = (A0_config.config.get('激活码', '').strip() == '' or
                      A0_config.API == 'http://wsai.b50.cc' or
                      '离线' in A0_config.config.get('激活码', ''))

            if A0_config.NOVIP and not 离线模式:
                self.提示('VIP专属功能, 请先升级为VIP')
                return
            if len(self.修改后内容.toPlainText()) < 10 or '角色名' not in self.修改后内容.toPlainText():
                self.提示('请先点击提取角色形象或形象格式有误, 请手动调整或重新提取,\n格式如下:\n[角色名]张三\n[别名]张三, 他, 张麻子\n[角色形象]一个满脸麻子的48岁中年男人')
                return
            if self.table_widget.rowCount() < 3:
                self.提示('请先新建或加载任务后继续')
                return
            reply = QMessageBox.question(self, '确认', f'一键智能分镜会清除当前所有已经添加的分镜, 并重新增加!!!\n智能角色提取对别名和角色识别并不完全准确, 如有遗落请先手动修改别名或添加, \n\n手动添加格式如下(记得换行):\n[角色名]张三\n[别名]张三, 他, 张麻子\n[角色形象]一个满脸麻子的48岁中年男人\n\n确认为  {self.当前项目}  增加分镜', QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
            if reply == QMessageBox.Yes:
                分镜信息 = self.修改后内容.toPlainText()
                角色名 = []
                别名 = []
                角色形象 = []
                for b in ['[', ']', ']\n']:
                    分镜信息 = 分镜信息.replace(b, '')
                for a in 分镜信息.split('\n'):
                    if '角色名' in a:
                        角色名.append(a.replace('角色名', '').replace(' ', '').replace('/', '').replace('*', '').replace('#', ''))
                    else:  # inserted
                        if '别名' in a:
                            别名.append(a.replace('别名', '').replace('，', '\n').replace('。', '\n').replace(',', '\n').replace(' ', ''))
                        else:  # inserted
                            if '角色形象' in a:
                                角色形象.append(f'{a}, '.replace('角色形象', '').replace('。', ', ').replace('，', ', '))
                self.项目配置['fenjing'] = {'全局': {'性别': '', '称呼': '', 'guanjianci': '', 'guanjianci_zh': '', 'zhongzi': '', 'img': '', 'url': ''}}
                for inx, data in enumerate(self.项目配置['data']):
                    data['fenjing'] = ''
                # 🛡️ 修复：安全保存角色信息，区分中英文内容
                import re
                for 标签, 识别词, 形象 in zip(角色名, 别名, 角色形象):
                    if 标签!= '' and 识别词!= '':
                        # 检测角色形象是否包含中文
                        包含中文 = bool(re.search(r'[\u4e00-\u9fff]', 形象))

                        if 包含中文:
                            # 中文形象只保存到中文字段，英文字段留空等待翻译
                            self.项目配置['fenjing'][标签] = {
                                '性别': '',
                                '称呼': 识别词,
                                'guanjianci': '',  # 英文字段留空
                                'guanjianci_zh': 形象,  # 中文保存到中文字段
                                'zhongzi': '',
                                'img': '',
                                'url': ''
                            }
                            print(f"   🔤 角色 {标签} 检测到中文形象，已保存到guanjianci_zh字段")
                        else:
                            # 英文形象可以保存到英文字段
                            self.项目配置['fenjing'][标签] = {
                                '性别': '',
                                '称呼': 识别词,
                                'guanjianci': 形象,
                                'guanjianci_zh': 形象,
                                'zhongzi': '',
                                'img': '',
                                'url': ''
                            }
                            print(f"   ✅ 角色 {标签} 检测到英文形象，已保存到guanjianci字段")

                # 检查是否启用智能场景匹配
                智能匹配启用 = A0_config.config.get('scene_matching_enabled', False)

                if 智能匹配启用:
                    print("[智能场景匹配] 开始智能分镜匹配...")
                    try:
                        from pyz.任务运行文件.A21_智能场景匹配 import 智能匹配分镜

                        # 准备可用角色列表
                        available_characters = 角色名.copy()

                        # 对每个文本行进行智能匹配
                        for index, data in enumerate(self.项目配置['data']):
                            text_content = data['txt']
                            if len(text_content.strip()) < 5:  # 跳过太短的文本
                                continue

                            print(f"[智能场景匹配] 分析第{index+1}行: {text_content[:30]}...")

                            # 调用智能匹配
                            match_result = 智能匹配分镜(text_content, available_characters)

                            已有分镜 = []

                            if match_result.get("success") and match_result.get("data"):
                                # 使用AI分析结果
                                ai_data = match_result["data"]
                                主要角色 = ai_data.get("主要角色", [])
                                推荐分镜 = ai_data.get("推荐分镜", {})
                                角色匹配 = 推荐分镜.get("角色匹配", "")

                                print(f"[智能场景匹配] AI识别角色: {主要角色}")
                                print(f"[智能场景匹配] 推荐角色: {角色匹配}")

                                # 优先使用AI推荐的角色匹配
                                if 角色匹配 and 角色匹配 in available_characters:
                                    已有分镜.append(角色匹配)
                                    print(f"[智能场景匹配] 使用AI推荐角色: {角色匹配}")
                                else:
                                    # 如果AI推荐的角色不在可用列表中，尝试匹配AI识别的角色
                                    for ai_角色 in 主要角色:
                                        if ai_角色 in available_characters and ai_角色 not in 已有分镜:
                                            已有分镜.append(ai_角色)
                                            print(f"[智能场景匹配] 匹配AI识别角色: {ai_角色}")
                                            break

                                # 如果AI没有匹配到合适角色，回退到传统匹配
                                if not 已有分镜:
                                    print("[智能场景匹配] AI未匹配到角色，使用传统匹配")
                                    已有分镜 = self._传统角色匹配(text_content, 角色名, 别名, 标签)
                            else:
                                # AI匹配失败，使用传统匹配
                                print(f"[智能场景匹配] AI匹配失败: {match_result.get('error', '未知错误')}")
                                已有分镜 = self._传统角色匹配(text_content, 角色名, 别名, 标签)

                            # 限制分镜数量
                            已有分镜 = 已有分镜[:4]
                            data['fenjing'] = ','.join(已有分镜)
                            self.fenjing(data['fenjing'], index)

                    except Exception as e:
                        print(f"[智能场景匹配] 智能匹配异常: {e}")
                        print("[智能场景匹配] 回退到传统匹配模式")
                        智能匹配启用 = False

                # 如果未启用智能匹配或智能匹配失败，使用传统匹配
                if not 智能匹配启用:
                    print("[智能场景匹配] 使用传统角色匹配...")
                    for 标签, 识别词, 形象 in zip(角色名, 别名, 角色形象):
                        if 标签!= '' and 识别词!= '':
                            for index, data in enumerate(self.项目配置['data']):
                                识别词 = 识别词 + '\n' + 标签
                                已有分镜 = data['fenjing'].split(',')
                                已有分镜 = [sentence.strip() for sentence in 已有分镜 if sentence.strip()]
                                for 词 in 识别词.split('\n'):
                                    if bool(re.search('[\\u4e00-\\u9fa5]', 词)):
                                        词 = 词.replace(' ', '')
                                    if 词 in data['txt'] and 标签 not in 已有分镜 and (len(已有分镜) < 4):
                                        已有分镜.append(标签)
                                data['fenjing'] = ','.join(已有分镜)
                                self.fenjing(data['fenjing'], index)
                self.保存配置()
                self.提示('增加成功, 可点击\"设置分镜\"查看和修改配置')
                self.一键增加分镜.setText('增加成功')
        except Exception as e:
            self.提示(f'请确认任务是否正常加载{e}')

    def _传统角色匹配(self, text_content, 角色名, 别名, 当前标签):
        """传统的角色匹配方法"""
        已有分镜 = []

        for 标签, 识别词 in zip(角色名, 别名):
            if 标签 != '' and 识别词 != '':
                识别词列表 = (识别词 + '\n' + 标签).split('\n')
                for 词 in 识别词列表:
                    if bool(re.search('[\\u4e00-\\u9fa5]', 词)):
                        词 = 词.replace(' ', '')
                    if 词 in text_content and 标签 not in 已有分镜 and len(已有分镜) < 4:
                        已有分镜.append(标签)
                        break

        return 已有分镜

    def 提取小说角色(self):
        # 检查是否为支持的平台：文视GPT、本地模型、SiliconFlow
        支持的平台 = [A0_config.name_gpt, '本地', 'SiliconFlow']
        当前平台 = A0_config.config['ChatGPT端口']

        if not any(平台 in 当前平台 for 平台 in 支持的平台):
            self.提示(f'创作工具功能支持{A0_config.name_gpt}GPT、本地模型和SiliconFlow平台')
            return
        try:
            if not self.小说原文.toPlainText() and (not self.修改指令.toPlainText()):
                self.提示('请先输入需要处理的内容或要求')
                return
            self.提取角色.setText('提取中')
            self.开始改文.setEnabled(False)
            self.转到原创.setEnabled(False)
            self.提取角色.setEnabled(False)
            self.修改后内容.setText('')
            self.修改后内容.setReadOnly(True)
            self.功能 = '提取角色'
            if self.running:
                self.提示('有任务正在进行中')
            else:  # inserted
                self.running = True
                self.worker_gpt.start()
        except Exception as e:
            print('提取角色错误', e)

    def call_backlog_gaiwen(self, 返回数据):
        text = 返回数据[0]
        进度 = 返回数据[1]
        if 进度 == '改写完成':
            if '中' in self.开始改文.text():
                self.开始改文.setText('改写完成')
            if '中' in self.提取角色.text():
                self.提取角色.setText('设计完成')
            self.修改后内容.setReadOnly(False)
            self.转到原创.setEnabled(True)
            self.开始改文.setEnabled(True)
            self.提取角色.setEnabled(True)
        else:  # inserted
            if 进度 == '弹窗':
                self.弹窗停止(text)
            else:  # inserted
                if 进度 == '提取成功':
                    self.修改后内容.setText(text)
                    self.修改后内容.setReadOnly(False)
                    self.转到原创.setEnabled(True)
                    self.开始改文.setEnabled(True)
                    self.提取角色.setEnabled(True)
                else:  # inserted
                    self.修改后内容.moveCursor(QTextCursor.End)
                    self.修改后内容.insertPlainText(text)

    def 创建计划任务布局(self):
        self.批量计划任务 = QGroupBox('批量计划任务')
        计划布局 = QVBoxLayout(self.批量计划任务)
        计划执行布局 = QHBoxLayout()
        计划布局.addLayout(计划执行布局)
        self.计划数量 = QLineEdit(self)
        self.计划数量.setPlaceholderText('任务数量')
        self.计划数量.setText(str(1))
        计划执行布局.addWidget(self.计划数量)
        添加计划 = QPushButton('添加计划', self)
        添加计划.clicked.connect(self.addTasks)
        计划执行布局.addWidget(添加计划)
        删除计划 = QPushButton('删除计划', self)
        删除计划.clicked.connect(self.reTasks)
        计划执行布局.addWidget(删除计划)
        计划执行布局.addItem(QSpacerItem(0, 15, QSizePolicy.Expanding, QSizePolicy.Minimum))
        开始任务 = QPushButton('开始任务', self)
        开始任务.clicked.connect(lambda: self.执行任务('批量计划任务'))
        计划执行布局.addWidget(开始任务)
        停止任务 = QPushButton('停止任务', self)
        停止任务.clicked.connect(self.StopTasks)
        计划执行布局.addWidget(停止任务)
        for i in range(计划执行布局.count()):
            widget = 计划执行布局.itemAt(i).widget()
            if widget is not None:
                widget.setFixedHeight(int(self.行高 * 1.5))
                if i == 3:
                    continue
                widget.setFixedWidth(self.缩放(80))
        self.计划列表 = QTableWidget(self)
        计划布局.addWidget(self.计划列表)
        self.计划列表.setColumnCount(10)
        self.计划列表.setHorizontalHeaderLabels(['任务编号', '选择任务', '选择模型', '选择VAE', '采样方法', '调度器', '引导系数', '采样步数', '重绘幅度', '运行状态'])
        header = self.计划列表.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)

    def reTasks(self):
        当前行数 = self.计划列表.rowCount()
        if 当前行数 > int(self.计划数量.text()):
            for i in range(int(self.计划数量.text())):
                self.计划列表.removeRow(self.计划列表.rowCount() - 1)
        else:  # inserted
            if 当前行数 > 0:
                self.计划列表.removeRow(self.计划列表.rowCount() - 1)

    def addTasks(self):
        当前行数 = self.计划列表.rowCount()
        rowCount = int(self.计划数量.text())
        for rows in range(rowCount):
            row = rows + 当前行数
            self.计划列表.insertRow(row)
            编号 = QTableWidgetItem(str(row + 1))
            计划任务 = QComboBox(self)
            for index in range(self.重绘列表.count()):
                计划任务.addItem(self.重绘列表.itemText(index))
            if 当前行数 < 计划任务.count():
                计划任务.setCurrentIndex(当前行数)
            计划模型 = QComboBox(self)
            计划模型.addItems(A0_config.config['更换SD模型'])
            计划模型.setCurrentText(A0_config.config['当前选中模型'])
            计划VAE = QComboBox(self)
            计划VAE.addItems(A0_config.config['更换VAE模型'])
            计划VAE.setCurrentText(A0_config.config['当前选中VAE'])
            采样方法 = QComboBox(self)
            采样方法.addItems(A0_config.config['采样方法'])
            采样方法.setCurrentText(A0_config.config['图片采样方法'])
            调度器 = QComboBox(self)
            调度器.addItems(A0_config.config['所有调度器'])
            调度器.setCurrentText(A0_config.config['调度器'])
            引导系数 = QTableWidgetItem(A0_config.config['提示词相关性'])
            采样步数 = QTableWidgetItem(A0_config.config['图片采样步数'])
            重绘幅度 = QTableWidgetItem(A0_config.config['修复重绘幅度'])
            计划状态 = QTableWidgetItem('未开始')
            self.计划列表.setItem(row, 0, 编号)
            self.计划列表.setCellWidget(row, 1, 计划任务)
            self.计划列表.setCellWidget(row, 2, 计划模型)
            self.计划列表.setCellWidget(row, 3, 计划VAE)
            self.计划列表.setCellWidget(row, 4, 采样方法)
            self.计划列表.setCellWidget(row, 5, 调度器)
            self.计划列表.setItem(row, 6, 引导系数)
            self.计划列表.setItem(row, 7, 采样步数)
            self.计划列表.setItem(row, 8, 重绘幅度)
            self.计划列表.setItem(row, 9, 计划状态)
            计划状态.setTextAlignment(Qt.AlignCenter)
            引导系数.setTextAlignment(Qt.AlignCenter)
            采样步数.setTextAlignment(Qt.AlignCenter)
            重绘幅度.setTextAlignment(Qt.AlignCenter)
            计划状态.setTextAlignment(Qt.AlignCenter)
            编号.setTextAlignment(Qt.AlignCenter)
        self.计划列表.verticalHeader().setDefaultSectionSize(self.缩放(45))
        header = self.计划列表.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        self.计划列表.verticalHeader().setVisible(False)
        self.计划列表.resizeColumnsToContents()
        self.计划列表.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.计划列表.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeToContents)
        self.计划列表.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)
        self.计划列表.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)

    def StopTasks(self):
        if self.任务模式 == '视频转绘':
            api = A0_config.config['云端地址'].replace('，', ',').replace(' ', '').split(',')[0]
            if api.endswith('/'):
                api = api[:(-1)]
            requests.post(f'{api}/interrupt')
        self.running = False
        print('停止执行,请等待本次结束')

    def 创建视频转绘页面(self):
        self.视频转绘窗口 = 视频转绘组件(self)
        self.视频转绘窗口.调试运行.clicked.connect(lambda: self.生成视频转绘任务(True))
        self.视频转绘窗口.开始任务.clicked.connect(lambda: self.生成视频转绘任务(False))

    def 创建菜单栏(self):
        self.菜单栏组件 = 菜单栏(self)
        self.菜单栏组件.菜单栏.itemClicked.connect(self.onItemClicked)
        self.菜单栏组件.菜单栏1.itemClicked.connect(self.onItemClicked)

    def 生成视频转绘任务(self, 调试):
        self.转绘调试 = 调试
        self.执行任务('视频转绘任务')

    def 更换背景(self, 更换):
        pass
        self.加载历史任务()
        if 更换:
            self.提示('更换背景需要背景视频时长比原视频长')
            options = QFileDialog.Options()
            file_dialog = QFileDialog()
            file_dialog.setNameFilter('视频文件 Files (*.mp4 *.jpg *.png *.jpeg)')
            file_dialog.setFileMode(QFileDialog.ExistingFile)
            selected_file, _ = file_dialog.getOpenFileName(self, '选择文件', '', '视频文件 (*.mp4)', options=options)
            if selected_file!= '':
                video_path = jduvudu312us_usjlq.path.join(self.当前项目文件夹, f'更换背景{jduvudu312us_usjlq.path.splitext(selected_file)[1]}')
                shutil.copy(selected_file, video_path)
                self.项目配置['back'] = jduvudu312us_usjlq.path.join(self.当前项目文件夹, '更换背景.png')
                self.get_frame_from_video(video_path, A0_config.改文件名(self.项目配置['back']))
                self.set_image(self.视频转绘窗口.背景视频, A0_config.改文件名(self.项目配置['back']))
                self.保存配置()
        else:  # inserted
            image = A0_config.改文件名(self.项目配置.get('back'))
            if image:
                if jduvudu312us_usjlq.path.exists(image):
                    jduvudu312us_usjlq.remove(image)
                image = image.replace('.mp4', '.png')
                if image:
                    if jduvudu312us_usjlq.path.exists(image):
                        jduvudu312us_usjlq.remove(image)
            self.项目配置['back'] = ''
            self.保存配置()
            self.set_image(self.视频转绘窗口.背景视频, '')

    def onItemClicked(self, item):
        if self.running and item.text() in ['爆款克隆', '视频原创', '视频转绘']:
            self.提示('有任务正在运行中, 请勿随意切换任务模式')
            if self.任务模式 == '原创模式':
                self.工作组件.setCurrentIndex(0)
                self.菜单栏组件.菜单栏.setCurrentRow(0)
                return
            if self.任务模式 == '克隆模式':
                self.工作组件.setCurrentIndex(0)
                self.菜单栏组件.菜单栏.setCurrentRow(1)
                return
            if self.任务模式 == '视频转绘':
                self.工作组件.setCurrentIndex(0)
                self.菜单栏组件.菜单栏.setCurrentRow(2)
            return None
        if item.text() == '爆款克隆':
            self.工作组件.setCurrentIndex(0)
            self.任务模式 = '克隆模式'
            self.任务模式切换事件()
            A0_config.config['推理模型'] = self.任务模式
            A0_config.修改配置()
        else:  # inserted
            if item.text() == '视频原创':
                self.工作组件.setCurrentIndex(0)
                self.任务模式 = '原创模式'
                self.任务模式切换事件()
                A0_config.config['推理模型'] = self.任务模式
                A0_config.修改配置()
            else:  # inserted
                if item.text() == '视频转绘':
                    self.工作组件.setCurrentIndex(0)
                    self.任务模式 = '视频转绘'
                    self.任务模式切换事件()
                    A0_config.config['推理模型'] = self.任务模式
                    A0_config.修改配置()
                else:  # inserted
                    if item.text() == '联系我们':
                        if self.端口 == 'zong':
                            QMessageBox.information(self, '提示', '客服QQ:  251444839\n\n客服微信:  tilike8888\n\n商务合作:  Letian20182020', QMessageBox.Ok)
                        else:  # inserted
                            QMessageBox.information(self, '提示', '技术QQ: 735665755\n\n技术微信: luoguohui99\n\nQQ交流群: 1025271670\n\n添加备注: 文视AI', QMessageBox.Ok)
                    else:  # inserted
                        if item.text() == '使用教程':
                            操作教程链接 = 'https://wenshiai.feishu.cn/wiki/space/7317199589176999937'
                            self.on_third_button_clicked(操作教程链接)
                        else:  # inserted
                            if item.text() == '软件设置':
                                if self.设置窗口:
                                    return
                                self.设置窗口 = True
                                try:
                                    海外模式 = A0_config.config['海外模式']
                                    dialog = ruanjianshezhi(self)
                                    dialog.exec_()
                                    self.保存参数(dialog)
                                    if 海外模式!= A0_config.config['海外模式'] and self.任务模式 == '原创模式' and (self.table_widget.rowCount() > 1):
                                        self.执行任务('海外模式切换')
                                except Exception as e:
                                    print(f'设置错误: {e}')
                                finally:  # inserted
                                    self.设置窗口 = False
                            if item.text() == '创作工具':
                                self.工作组件.setCurrentIndex(1)
                            else:  # inserted
                                if item.text() == '批量挂机':
                                    self.工作组件.setCurrentIndex(3)
                                else:  # inserted
                                    if item.text() == '会员Q群':
                                        if not A0_config.试用:
                                            链接 = 'http://qm.qq.com/cgi-bin/qm/qr?_wv=1027&k=IUp-31oFnGKkJKPpNT-awbdJ-nWxoi_W&authKey=OkfE6YKS1jkcLNkobtRmnNV6EvTTffAyNlumxeX5N8KUxjV561N8R964Tasx6Qc4&noverify=0&group_code=104714849'
                                        else:  # inserted
                                            self.提示(f'{A0_config.name}会员专属服务群, 首页-充值-加入会员后可加入，免费会员将跳转到{A0_config.name}交流群')
                                            链接 = 'http://qm.qq.com/cgi-bin/qm/qr?_wv=1027&k=oNQEt3rSzVX_Y9ShjnbmfHDjl6rxbWG3&authKey=m1ac0U9xVnE1LjvQ33zwpa4Wz3CdelOf%2FM%2F6OuW9xcEy2Gwqi%2FgkbgPbIrY3QWOU&noverify=0&group_code=1025271670'
                                        self.on_third_button_clicked(链接)
                                    else:  # inserted
                                        if item.text() == '会员模型':
                                            if not A0_config.试用:
                                                链接 = 'https://pan.baidu.com/s/1jq2G73G5GpbND-yJbCfDFg?pwd=wens'
                                            else:  # inserted
                                                self.提示('会员专用推文模型, 首页-充值-加入会员后可下载，试用会员将跳转到预览链接')
                                                链接 = 'https://pan.baidu.com/s/14U0pI6nm6-b0uxmROMwI5Q?pwd=8888'
                                            self.on_third_button_clicked(链接)
                                        else:  # inserted
                                            if item.text() == '夜间模式':
                                                icon5 = QIcon()
                                                pixmap5 = QPixmap('img/buttom_white/太阳1_sun-one.svg')
                                                pixmap5 = pixmap5.scaled(self.缩放(20), self.缩放(20), Qt.KeepAspectRatio, Qt.SmoothTransformation)
                                                icon5.addPixmap(pixmap5, QIcon.Normal, QIcon.Off)
                                                self.菜单栏组件.夜间模式.setIcon(icon5)
                                                self.菜单栏组件.夜间模式.setText('高亮模式')
                                                A0_config.夜间模式 = True
                                                A0_config.背景色 = 'rgb(49, 51, 53)'
                                                A0_config.按钮色 = 'rgb(60, 63, 65)'
                                                A0_config.文字色 = 'rgb(220, 220, 220)'
                                                A0_config.边框色 = 'rgb(100, 100, 100)'
                                                A0_config.菜单栏色 = 'rgb(53, 53, 53)'
                                                A0_config.标题色 = 'rgb(43, 43, 43)'
                                                A0_config.子窗口色 = 'rgb(53, 53, 53)'
                                                A0_config.选中色 = 'rgb(75, 110, 175)'
                                                A0_config.警告色 = 'rgb(230, 130, 0)'
                                                self.修改色系()
                                                A0_config.config['夜间模式'] = True
                                                A0_config.修改配置()
                                            else:  # inserted
                                                if item.text() == '高亮模式':
                                                    icon5 = QIcon()
                                                    pixmap5 = QPixmap('img/buttom_white/月亮_moon.svg')
                                                    pixmap5 = pixmap5.scaled(self.缩放(20), self.缩放(20), Qt.KeepAspectRatio, Qt.SmoothTransformation)
                                                    icon5.addPixmap(pixmap5, QIcon.Normal, QIcon.Off)
                                                    self.菜单栏组件.夜间模式.setIcon(icon5)
                                                    self.菜单栏组件.夜间模式.setText('夜间模式')
                                                    A0_config.夜间模式 = False
                                                    A0_config.背景色 = 'rgb(255, 255, 255)'
                                                    A0_config.按钮色 = 'rgb(220, 220, 220)'
                                                    A0_config.文字色 = 'rgb(0, 0, 0)'
                                                    A0_config.边框色 = 'rgb(100, 100, 100)'
                                                    A0_config.菜单栏色 = 'rgb(210, 210, 210)'
                                                    A0_config.标题色 = 'rgb(160, 160, 160)'
                                                    A0_config.子窗口色 = 'rgb(255, 255, 255)'
                                                    A0_config.选中色 = 'rgb(110, 150, 210)'
                                                    A0_config.警告色 = 'rgb(230, 130, 0)'
                                                    self.修改色系()
                                                    A0_config.config['夜间模式'] = False
                                                    A0_config.修改配置()
                                                else:  # inserted
                                                    if item.text() == '会员升级':
                                                        chognzhiui = ExampleDialog(self)
                                                        chognzhiui.exec_()
                                                        if self.任务模式 == '原创模式':
                                                            self.菜单栏组件.菜单栏.setCurrentRow(0)
                                                        else:  # inserted
                                                            if self.任务模式 == '克隆模式':
                                                                self.菜单栏组件.菜单栏.setCurrentRow(1)
                                                            else:  # inserted
                                                                if self.任务模式 == '视频转绘':
                                                                    self.菜单栏组件.菜单栏.setCurrentRow(2)
                                                    else:  # inserted
                                                        if item.text() == '运行日志':
                                                            if self.日志窗口:
                                                                return
                                                            self.日志窗口 = True
                                                            try:
                                                                log_window = LogWindow(self, A0_config.log_file_path)
                                                                log_window.exec_()
                                                            finally:  # inserted
                                                                self.日志窗口 = False
                                                        if item.text() == '文视官网':
                                                            文视官网链接 = f"http://www.b50.cc?username={A0_config.config.get('激活码')}"
                                                            self.on_third_button_clicked(文视官网链接)
        self.菜单栏组件.菜单栏1.setCurrentRow(0)

    def 修改色系(self):
        self.设置表格格式()
        self.setStyleSheet(self.设置布局())
        self.title_bar.setStyleSheet(f"""
    QW {{
        background-color: {A0_config.标题色};
    }}
    #QW {{
        background-color: {A0_config.标题色};
    }}
    #TitleBar {{
        background-color: {A0_config.标题色};
        height: {self.缩放(45)}px;
    }}
    #TitleLabel {{
        background-color: {A0_config.标题色};
        color: {A0_config.文字色};
        font-size: {self.缩放(17)}px;
        padding: 2px 5px;
    }}
    #MinimizeButton, #MaxRestoreButton, #CloseButton {{
        color: {A0_config.文字色};
        background-color: transparent;
        border: none;
        font-size: {self.缩放(17)}px;
        padding: 2px 5px;
    }}
    #MinimizeButton:hover, #MaxRestoreButton:hover {{
        background-color: rgb(78,82,84);
    }}
    #CloseButton:hover {{
        background-color: rgb(232, 17,35);
    }}
""")
        self.centralwidget.setStyleSheet(self.设置布局())
        self.视频转绘窗口.setStyleSheet(self.设置布局())
        self.菜单栏组件.setStyleSheet(f'background-color: {A0_config.菜单栏色};min-width: {self.缩放(140)}px;')
        # self.菜单栏组件.菜单栏.setStyleSheet(f'QListView::item:selected { background-color: {A0_config.选中色}; color: {A0_config.文字色}; border-left: 2px solid {A0_config.文字色}; };')
        # self.菜单栏组件.菜单栏1.setStyleSheet(f'QListView::item:selected {color: {A0_config.文字色};background-color: {A0_config.选中色};}')
        self.菜单栏组件.菜单栏.setStyleSheet(f'QListView::item:selected {{ background-color: {A0_config.选中色}; color: {A0_config.文字色}; border-left: 2px solid {A0_config.文字色}; }};')
        self.菜单栏组件.菜单栏1.setStyleSheet(f'QListView::item:selected {{color: {A0_config.文字色};background-color: {A0_config.选中色};}}')
        header = self.计划列表.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)

    def showContextMenu(self, pos):
        index = self.table_widget.indexAt(pos)
        if index.isValid():
            row = index.row()
            col = index.column()
            menu = QMenu(self)
            if col == self.正面词行:
                copy_action = QAction('复制', self)
                paste_action = QAction('粘贴', self)
                resetting_all_action = QAction('重置所有', self)
                copy_action.triggered.connect(self.copyCell)
                paste_action.triggered.connect(self.pasteCell)
                resetting_all_action.triggered.connect(self.resetting_all)
                menu.addAction(copy_action)
                menu.addSeparator()
                menu.addAction(paste_action)
                menu.addSeparator()
                menu.addSeparator()
                menu.addAction(resetting_all_action)
                menu.exec_(self.table_widget.mapToGlobal(pos))
            else:  # inserted
                if col == self.分镜行:
                    zengjia_action = QAction('增加分镜', self)
                    shezhi_action = QAction('设置分镜', self)
                    remove_action = QAction('删除选中分镜', self)
                    remove_all_action = QAction('删除所有分镜', self)
                    zengjia_action.triggered.connect(self.zengjiafenjing)
                    shezhi_action.triggered.connect(self.shezhifenjing)
                    remove_action.triggered.connect(lambda: self.removefenjing(row))
                    remove_all_action.triggered.connect(self.removefenjing_all)
                    menu.addAction(zengjia_action)
                    menu.addSeparator()
                    menu.addAction(shezhi_action)
                    menu.addSeparator()
                    menu.addAction(remove_action)
                    menu.addSeparator()
                    menu.addAction(remove_all_action)
                    menu.exec_(self.table_widget.mapToGlobal(pos))
                else:  # inserted
                    if col == self.语音行:
                        增加声音 = QAction('增加声音', self)
                        删除选中 = QAction('删除选中', self)
                        全部删除 = QAction('全部删除', self)
                        使用图片分镜 = QAction('使用图片分镜', self)
                        menu.addAction(增加声音)
                        menu.addSeparator()
                        menu.addAction(删除选中)
                        menu.addSeparator()
                        menu.addAction(全部删除)
                        menu.addSeparator()
                        menu.addAction(使用图片分镜)
                        增加声音.triggered.connect(lambda: self.zjsy(row))
                        删除选中.triggered.connect(lambda: self.scsy(row))
                        全部删除.triggered.connect(lambda: self.scall(row))
                        使用图片分镜.triggered.connect(lambda: self.fzsy(row))
                        menu.exec_(self.table_widget.mapToGlobal(pos))
                    else:  # inserted
                        if col == self.内容行:
                            chaifen_action = QAction('拆分镜头', self)
                            chaifen_action.triggered.connect(self.chaifen_yc)
                            menu.addAction(chaifen_action)
                            if self.任务模式 == '原创模式' and A0_config.config['海外模式']:
                                tihuan_action = QAction('显示英文', self)
                                tihuan_action.triggered.connect(self.yijianchongzhi_yc)
                                menu.addAction(tihuan_action)
                                menu.addSeparator()
                                tihuan_action1 = QAction('显示中文', self)
                                tihuan_action1.triggered.connect(self.yijianchongzhi_zh)
                                menu.addSeparator()
                                menu.addAction(tihuan_action1)
                            menu.exec_(self.table_widget.mapToGlobal(pos))
                        else:  # inserted
                            if col == self.图片行:
                                dakai_action = QAction('打开图片', self)
                                tihuan_action = QAction('替换图片', self)
                                copy_action = QAction('复制图片', self)
                                pr_action = QAction('粘贴图片', self)
                                辅助图片 = QAction('设置为辅助图片', self)
                                视频尾帧 = QAction('设置为视频尾帧', self)
                                resetting_action = QAction('删除图片', self)
                                resetting_all_action = QAction('删除所有图片', self)
                                resetting_action_vi_th = QAction('替换视频', self)
                                resetting_action_vi = QAction('删除视频', self)
                                resetting_all_action_vi = QAction('删除所有视频', self)
                                resetting_all_action1 = QAction('删除语音', self)
                                if self.任务模式 == '视频转绘' and row!= 0:
                                    使用前视频尾帧 = QAction('使用前视频尾帧', self)
                                    使用前视频尾帧.triggered.connect(lambda: self.img_weizhen(row, col))
                                menu.addAction(dakai_action)
                                menu.addSeparator()
                                menu.addAction(tihuan_action)
                                menu.addSeparator()
                                menu.addAction(copy_action)
                                menu.addSeparator()
                                menu.addAction(pr_action)
                                menu.addSeparator()
                                menu.addAction(resetting_action_vi_th)
                                menu.addSeparator()
                                menu.addAction(辅助图片)
                                menu.addSeparator()
                                menu.addAction(视频尾帧)
                                menu.addSeparator()
                                menu.addAction(resetting_action)
                                menu.addSeparator()
                                menu.addAction(resetting_all_action)
                                menu.addSeparator()
                                menu.addAction(resetting_action_vi)
                                menu.addSeparator()
                                menu.addAction(resetting_all_action_vi)
                                menu.addSeparator()
                                menu.addAction(resetting_all_action1)
                                if self.任务模式 == '视频转绘' and row!= 0:
                                    menu.addSeparator()
                                    menu.addAction(使用前视频尾帧)
                                dakai_action.triggered.connect(lambda: self.Double_click(row, col, True))
                                tihuan_action.triggered.connect(lambda: self.tihuanxintu(row, col))
                                resetting_action.triggered.connect(lambda: self.resetting_tu(row, col))
                                copy_action.triggered.connect(lambda: self.fuzhixintu(row, col))
                                pr_action.triggered.connect(lambda: self.zhantiexintu(row, col))
                                resetting_all_action.triggered.connect(self.resetting_all_tu)
                                resetting_action_vi.triggered.connect(lambda: self.resetting_tu_vi(row, col))
                                resetting_action_vi_th.triggered.connect(lambda: self.resetting_tu_vi_th(row, col))
                                resetting_all_action_vi.triggered.connect(self.resetting_all_tu_vi)
                                resetting_all_action1.triggered.connect(self.resetting_all_yuyin)
                                辅助图片.triggered.connect(lambda: self.img_fuzhu(row, col, '辅助图片'))
                                视频尾帧.triggered.connect(lambda: self.img_fuzhu(row, col, '视频尾帧'))
                                menu.exec_(self.table_widget.mapToGlobal(pos))
                            else:  # inserted
                                if col == self.原图片行:
                                    tihuan_action = QAction('替换图片', self)
                                    menu.addAction(tihuan_action)
                                    tihuan_action.triggered.connect(lambda: self.tihuanxintu(row, col))
                                    menu.exec_(self.table_widget.mapToGlobal(pos))
                                else:  # inserted
                                    if col == self.修改文案行:
                                        tihuan_action = QAction('一键重置', self)
                                        menu.addAction(tihuan_action)
                                        tihuan_action.triggered.connect(self.yijianchongzhi)
                                        menu.exec_(self.table_widget.mapToGlobal(pos))

    def img_fuzhu(self, row, col, 任务):
        reply = QMessageBox.question(self, '确认', f'是否添加当前图片为 {任务}\n添加后可在右侧按钮 \"分镜设置\" 中修改或删除', QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        if reply == QMessageBox.Yes:
            try:
                img = self.项目配置['data'][row]['img'].get('path')
                视频尾帧 = self.项目配置['data'][row]['img_end']
                辅助图片 = self.项目配置['data'][row]['img_cn']
                if img and jduvudu312us_usjlq.path.exists(img):
                    if 任务 == '视频尾帧':
                        shutil.copy(img, 视频尾帧)
                    else:  # inserted
                        shutil.copy(img, 辅助图片)
                else:  # inserted
                    print('图片不存在')
            except Exception as e:
                print('复制错误')
                self.提示(f'复制错误: {e}')

    def zjsy(self, row):
        if A0_config.NOVIP:
            A0_config.升级提示('多角色视频')
            return
        try:
            dialog = up_zjsy(self)
            result = dialog.exec_()
            if result == QDialog.Accepted:
                role = dialog.text_edit.text()
                print(role)
                if role not in self.项目配置.get('fj_yuyin', {}).keys() and role!= '':
                    if A0_config.name_gpt in A0_config.config['语音平台']:
                        if 'TTS' in A0_config.config['语音平台']:
                            声音 = A0_config.config.get('选中底模')
                            模型 = A0_config.config.get('选中底模')
                        else:  # inserted
                            if 'CosyVoice' in A0_config.config['语音平台']:
                                声音 = A0_config.config.get('选中CV参考音')
                                模型 = A0_config.config.get('选中CV参考音')
                            else:  # inserted
                                声音 = A0_config.config.get('选中声音')
                                模型 = A0_config.config.get('选中底模')
                        self.项目配置['fj_yuyin'][role] = {'声音': 声音, '模型': 模型}
                    else:  # inserted
                        self.项目配置['fj_yuyin'][role] = {'声音': A0_config.config['微软声音'], '模型': A0_config.config['微软角色']}
                    for row in range(len(self.项目配置.get('data'))):
                        if self.任务模式 == '原创模式':
                            self.fenjing_yy(row)
                            self.保存配置()
                else:  # inserted
                    self.提示('声音分镜已存在')
        except Exception as e:
            print('增加声音分镜错误: ', e)

    def scsy(self, row):
        try:
            名称 = self.项目配置.get('data')[row]['fj_yuyin']
            print(名称)
            if 名称 == '旁白':
                self.提示('默认分镜无法删除')
                return
            reply = QMessageBox.question(self, '确认', f'是否删除语音分镜 \"{名称}\" , 删除后不可恢复', QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
            if reply == QMessageBox.Yes:
                del self.项目配置['fj_yuyin'][名称]
                for row in range(len(self.项目配置.get('data'))):
                    if self.任务模式 == '原创模式':
                        if self.项目配置['data'][row].get('fj_yuyin') == 名称 or not self.项目配置['data'][row].get('fj_yuyin'):
                            self.项目配置['data'][row]['fj_yuyin'] = '旁白'
                        self.fenjing_yy(row)
                self.保存配置()
                return
        except Exception as e:
            print('删除声音分镜错误: ', e)

    def scall(self, row):
        try:
            reply = QMessageBox.question(self, '确认', '是否删除当前所有语音分镜, 删除后不可恢复', QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
            if reply == QMessageBox.Yes:
                self.项目配置['fj_yuyin'] = {'旁白': {'模型': '', '声音': ''}}
                for row in range(len(self.项目配置.get('data'))):
                    if self.任务模式 == '原创模式':
                        self.项目配置['data'][row]['fj_yuyin'] = '旁白'
                        self.fenjing_yy(row)
                self.保存配置()
        except Exception as e:
            print('删除所有声音分镜错误: ', e)

    def fzsy(self, row):
        for role in self.项目配置['fenjing'].keys():
            print(role)
            if role not in self.项目配置.get('fj_yuyin', {}).keys() and role!= '' and (role!= '全局'):
                if A0_config.name_gpt in A0_config.config['语音平台']:
                    if 'TTS' in A0_config.config['语音平台']:
                        声音 = A0_config.config.get('选中底模')
                        模型 = A0_config.config.get('选中底模')
                    else:  # inserted
                        if 'CosyVoice' in A0_config.config['语音平台']:
                            声音 = A0_config.config.get('选中CV参考音')
                            模型 = A0_config.config.get('选中CV参考音')
                        else:  # inserted
                            声音 = A0_config.config.get('选中声音')
                            模型 = A0_config.config.get('选中底模')
                    self.项目配置['fj_yuyin'][role] = {'声音': 声音, '模型': 模型}
                else:  # inserted
                    self.项目配置['fj_yuyin'][role] = {'声音': A0_config.config['微软声音'], '模型': A0_config.config['微软角色']}
            else:  # inserted
                print('声音分镜已存在')
        for row in range(len(self.项目配置.get('data'))):
            if self.任务模式 == '原创模式':
                self.fenjing_yy(row)
                self.保存配置()

    def yijianchongzhi(self):
        try:
            reply = QMessageBox.question(self, '确认', '确认重置所有修改后文案, 重置后不可恢复', QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
            if reply == QMessageBox.Yes:
                for index, data in enumerate(self.项目配置['data']):
                    self.项目配置['data'][index]['txtxiugai'] = self.项目配置['data'][index]['txt']
                    if self.任务模式 == '克隆模式':
                        修改文案行 = QTableWidgetItem(self.项目配置['data'][index]['txtxiugai'])
                        修改文案行.setTextAlignment(Qt.AlignCenter)
                        self.table_widget.setItem(index, self.修改文案行, 修改文案行)
                self.保存配置()
        except Exception as e:
            self.提示(f'执行出错: {e}')

    def yijianchongzhi_yc(self):
        try:
            for index, data in enumerate(self.项目配置['data']):
                if self.任务模式 == '原创模式':
                    # 更新内容行的自定义文本组件
                    内容行_widget = self.table_widget.cellWidget(index, self.内容行)
                    if 内容行_widget:
                        内容文本框 = 内容行_widget.layout().itemAt(0).widget()
                        if isinstance(内容文本框, CustomTextEdit_content):
                            内容文本框.setText(self.项目配置['data'][index]['txt_en'])
        except Exception as e:
            self.提示(f'执行出错: {e}')

    def yijianchongzhi_zh(self):
        try:
            for index, data in enumerate(self.项目配置['data']):
                if self.任务模式 == '原创模式':
                    # 更新内容行的自定义文本组件
                    内容行_widget = self.table_widget.cellWidget(index, self.内容行)
                    if 内容行_widget:
                        内容文本框 = 内容行_widget.layout().itemAt(0).widget()
                        if isinstance(内容文本框, CustomTextEdit_content):
                            内容文本框.setText(self.项目配置['data'][index]['txt'])
        except Exception as e:
            self.提示(f'执行出错: {e}')

    def shezhifenjing(self):
        if self.table_widget.rowCount() < 3:
            self.提示('请先新建或加载任务后继续')
            return
        if self.人物设定:
            return
        self.人物设定 = True
        try:
            self.sub_ui = A4_人物设定.SubUI(self)
            self.sub_ui.exec_()
        finally:  # inserted
            self.人物设定 = False

    def zengjiafenjing(self):
        try:
            if self.table_widget.rowCount() < 3:
                self.提示('请先新建或加载任务后继续')
                return
            # 使用简化的对话框避免编码问题
            dialog = self.create_simple_zengjia_dialog()
            result = dialog.exec_()
            if result == QDialog.Accepted:
                self.handle_zengjia_result(dialog)
        except UnicodeEncodeError as e:
            print(f"编码错误: {e}")
            self.提示('编码错误，请检查系统编码设置')
            return
        except Exception as e:
            print(f"增加分镜出错: {e}")
            self.提示(f'增加分镜出错: {e}')
            return

    def create_simple_zengjia_dialog(self):
        """创建简化的增加分镜对话框，避免编码问题"""
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QTextEdit, QLabel, QComboBox
        from PyQt5.QtCore import Qt

        dialog = QDialog(self)
        dialog.setWindowTitle('添加角色')
        dialog.setModal(True)
        dialog.resize(600, 400)

        layout = QVBoxLayout()

        # 角色名称输入
        name_label = QLabel('角色名称：')
        layout.addWidget(name_label)
        dialog.name_edit = QTextEdit()
        dialog.name_edit.setFixedHeight(30)
        dialog.name_edit.setPlaceholderText('请输入角色名称或场景')
        layout.addWidget(dialog.name_edit)

        # 角色别名输入
        alias_label = QLabel('角色别名：')
        layout.addWidget(alias_label)
        dialog.alias_edit = QTextEdit()
        dialog.alias_edit.setFixedHeight(80)
        dialog.alias_edit.setPlaceholderText('请输入角色别名，每行一个')
        layout.addWidget(dialog.alias_edit)

        # 角色形象输入
        appearance_label = QLabel('角色外貌：')
        layout.addWidget(appearance_label)
        dialog.appearance_edit = QTextEdit()
        dialog.appearance_edit.setFixedHeight(80)
        dialog.appearance_edit.setPlaceholderText('请输入角色外貌描述')
        layout.addWidget(dialog.appearance_edit)

        # 按钮布局
        button_layout = QHBoxLayout()

        # 确定按钮
        ok_button = QPushButton('确定')
        ok_button.setFixedSize(150, 30)
        ok_button.clicked.connect(dialog.accept)
        button_layout.addWidget(ok_button)

        # 智能提取按钮
        smart_button = QPushButton('智能提取')
        smart_button.setFixedSize(150, 30)
        smart_button.setStyleSheet('QPushButton {background-color: #4CAF50;}')
        smart_button.clicked.connect(lambda: self.handle_smart_extract(dialog))
        button_layout.addWidget(smart_button)

        # 一键提取提示词按钮
        extract_button = QPushButton('一键提取提示词')
        extract_button.setFixedSize(150, 30)
        extract_button.setStyleSheet('QPushButton {background-color: #f44336;}')
        extract_button.clicked.connect(lambda: self.handle_extract_prompts(dialog))
        button_layout.addWidget(extract_button)

        layout.addLayout(button_layout)
        dialog.setLayout(layout)

        return dialog

    def handle_zengjia_result(self, dialog):
        """处理增加分镜对话框的结果"""
        try:
            name = dialog.name_edit.toPlainText().strip()
            alias = dialog.alias_edit.toPlainText().strip()
            appearance = dialog.appearance_edit.toPlainText().strip()

            if name:
                print(f"添加角色: {name}")
                if alias:
                    print(f"别名: {alias}")
                if appearance:
                    print(f"形象: {appearance}")
                self.提示(f'成功添加角色: {name}')
            else:
                self.提示('请输入角色名称')
        except Exception as e:
            print(f"处理结果出错: {e}")
            self.提示('处理结果时出错')

    def handle_smart_extract(self, dialog):
        """处理智能提取功能"""
        try:
            self.提示('智能提取功能暂未实现')
        except Exception as e:
            print(f"智能提取出错: {e}")

    def handle_extract_prompts(self, dialog):
        """处理一键提取提示词功能"""
        try:
            # 🛡️ 修复：找到一键提取提示词按钮
            extract_button = None
            for child in dialog.findChildren(QPushButton):
                button_text = child.text()
                if button_text == '一键提取提示词':
                    extract_button = child
                    print(f"🔍 找到提取按钮，文本：{button_text}")
                    break

            # 🛡️ 修复：检查是否找到了按钮
            if extract_button is None:
                print("❌ 未找到一键提取提示词按钮")
                self.提示('未找到一键提取提示词按钮，请重试')
                return

            # 创建一个适配器对象，模拟原来的zengjia对象接口
            class DialogAdapter(QDialog):
                def __init__(self, main_window, simple_dialog, button):
                    super().__init__(main_window)  # 继承QDialog，确保是QWidget
                    self.mood = main_window
                    self.dialog = simple_dialog
                    # 🛡️ 修复：安全设置按钮引用
                    self.一键提取提示词 = button  # 这个属性被一键提取提示词处理器使用
                    print(f"✅ DialogAdapter初始化完成，按钮引用：{button is not None}")

                def accept(self):
                    """模拟对话框的accept方法"""
                    print("🔍 DialogAdapter.accept() 被调用")
                    self.dialog.accept()
                    print("🔍 对话框已关闭")

                def _显示置顶错误提示(self, message):
                    """显示置顶的错误提示窗口"""
                    self.mood.提示(message)

            # 调用一键提取提示词功能
            from pyz.任务运行文件.一键提取提示词 import 一键提取提示词处理器

            adapter = DialogAdapter(self, dialog, extract_button)
            processor = 一键提取提示词处理器(self, adapter)
            processor.执行提取()

            # 一键提取提示词执行完毕，直接返回，不执行后续的普通添加角色逻辑
            return

        except ImportError as e:
            print(f"导入一键提取提示词模块失败: {e}")
            # 使用简单的消息框，避免触发复杂的错误处理逻辑
            from tkinter import messagebox
            messagebox.showerror('错误', '功能模块加载失败，请检查安装')
            return
        except Exception as e:
            print(f"启动一键提取提示词失败: {e}")
            # 使用简单的消息框，避免触发复杂的错误处理逻辑
            from tkinter import messagebox
            messagebox.showerror('错误', f'启动失败：{str(e)}')
            return

        # 以下是普通添加角色的逻辑，只有在非一键提取提示词时才执行
        if result == QDialog.Accepted:
            标签 = dialog.text_edit.toPlainText()
            识别词 = dialog.text_edit1.toPlainText().replace(',', '\n').replace('，', '\n') + '\n' + 标签
            性别 = dialog.人物性别.currentText()
            角色形象 = dialog.角色形象.toPlainText()
            参考图路径 = dialog.参考图路径
            image_path = ''

            # 检查是否使用了系统保留字段作为角色名称
            if 标签 == '全局':
                messagebox.showinfo('提示', '不能使用"全局"作为角色名称，这是系统保留字段')
                return

            if 标签!= '' and 识别词!= '' and (标签!= '智能提取'):
                if 参考图路径 and jduvudu312us_usjlq.path.exists(参考图路径):
                    image_path = jduvudu312us_usjlq.path.join(self.当前项目文件夹, f'{标签}.png')
                    shutil.copy(参考图路径, image_path)

                # 🛡️ 修复：安全保存手动添加的角色，区分中英文内容
                包含中文 = bool(re.search(r'[\u4e00-\u9fff]', 角色形象))

                if 包含中文:
                    # 中文形象只保存到中文字段，英文字段留空等待翻译
                    self.项目配置['fenjing'][标签] = {
                        '性别': 性别,
                        '称呼': 识别词,
                        'guanjianci': '',  # 英文字段留空
                        'guanjianci_zh': 角色形象,  # 中文保存到中文字段
                        'zhongzi': '',
                        'img': image_path,
                        'url': ''
                    }
                    print(f"   🔤 手动添加角色 {标签} 检测到中文形象，已保存到guanjianci_zh字段")
                else:
                    # 英文形象可以保存到英文字段
                    self.项目配置['fenjing'][标签] = {
                        '性别': 性别,
                        '称呼': 识别词,
                        'guanjianci': 角色形象,
                        'guanjianci_zh': 角色形象,
                        'zhongzi': '',
                        'img': image_path,
                        'url': ''
                    }
                    print(f"   ✅ 手动添加角色 {标签} 检测到英文形象，已保存到guanjianci字段")
                for index, data in enumerate(self.项目配置['data']):
                    已有分镜 = data['fenjing'].split(',')
                    已有分镜 = [sentence.strip() for sentence in 已有分镜 if sentence.strip()]
                    for 词 in 识别词.split('\n'):
                        if bool(re.search('[\\u4e00-\\u9fa5]', 词)):
                            词 = 词.replace(' ', '')
                        if 词 in data['txt'] and 标签 not in data['fenjing'] and (标签!= '') and (len(data['fenjing'].split(',')) < 4):
                            已有分镜.append(标签)
                            break
                    data['fenjing'] = ','.join(已有分镜)
                    self.fenjing(data['fenjing'], index)
                self.保存配置()
            else:  # 处理其他情况
                if 标签 == '智能提取':
                    self.小说原文.setText(识别词.replace('智能提取', ''))
                    self.工作组件.setCurrentIndex(1)
                    self.菜单栏组件.菜单栏.setCurrentRow(5)
                    self.提取小说角色()
                elif 标签 == '':
                    # 如果标签为空，提示用户输入角色名称
                    self.提示('请输入角色名称')
                else:
                    # 其他情况，可能是识别词为空
                    self.提示('请完善角色信息')

    def removefenjing(self, row):
        if self.项目配置['data'][row]['fenjing'] == '':
            self.提示('当前没有选中分镜')
            return
        reply = QMessageBox.question(self, '确认', f"是否在删除分镜: {self.项目配置['data'][row]['fenjing']}, 删除后不可恢复", QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        if reply == QMessageBox.Yes:
            for 标签 in self.项目配置['data'][row]['fenjing'].split(','):
                if 标签 in self.项目配置['fenjing'].keys():
                    del self.项目配置['fenjing'][标签]
                    for index, data in enumerate(self.项目配置['data']):
                        已有分镜 = []
                        for f in data['fenjing'].split(','):
                            if 标签!= f:
                                if f in self.项目配置['fenjing'].keys():
                                    if f not in 已有分镜:
                                        已有分镜.append(f)
                        data['fenjing'] = ','.join(已有分镜)
                        self.fenjing(data['fenjing'], index)
            self.保存配置()

    def removefenjing_all(self):
        reply = QMessageBox.question(self, '确认', '是否删除当前所有分镜, 删除后不可恢复', QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        if reply == QMessageBox.Yes:
            self.项目配置['fenjing'] = {'全局': {'guanjianci': '', 'zhongzi': '', 'img': ''}}
            for inx, data in enumerate(self.项目配置['data']):
                data['fenjing'] = ''
                self.fenjing(data['fenjing'], inx)
            self.保存配置()

    def fuzhixintu(self, current_row, current_col):
        原始图片 = A0_config.改文件名(self.项目配置['data'][current_row]['img']['path'])
        if 原始图片 and jduvudu312us_usjlq.path.exists(原始图片):
            clipboard = QApplication.clipboard()
            clipboard.setText(原始图片)
        print(原始图片)

    def img_weizhen(self, row, col):
        try:
            上一个视频 = self.项目配置['data'][row - 1]['video']
            备份图片组 = self.项目配置['data'][row]['img_beixuan']
            max_number = 文件名最大数(备份图片组)
            图片路径 = jduvudu312us_usjlq.path.join(self.图片文件夹, f"{self.项目配置['data'][row]['index']}({max_number + 1}).png")
            if 上一个视频 and jduvudu312us_usjlq.path.exists(上一个视频):
                cap = cv2.VideoCapture(上一个视频)
                total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                cap.set(cv2.CAP_PROP_POS_FRAMES, total_frames - 1)
                ret, frame = cap.read()
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                img = Image.fromarray(frame_rgb)
                img.save(图片路径)
                self.项目配置['data'][row]['img']['path'] = 图片路径
                self.项目配置['data'][row]['img_beixuan'].append({'path': 图片路径, 'seep': 0})
                self.保存配置()
                图片行 = QTableWidgetItem()
                pixmap = QPixmap(图片路径)
                # 增大图片显示尺寸，从160改为240
                width = int(pixmap.width() * self.缩放(240) / pixmap.height())
                scaled_pixmap = pixmap.scaledToWidth(width)
                图片行.setIcon(QIcon(scaled_pixmap))
                图片行.setTextAlignment(Qt.AlignCenter)
                图片行.setData(Qt.UserRole, 图片路径)
                if col == self.图片行:
                    self.table_widget.setItem(row, self.图片行, 图片行)
                else:  # inserted
                    self.table_widget.setItem(row, self.原图片行, 图片行)
                self.table_widget.setIconSize(pixmap.size())
                图片行.setTextAlignment(Qt.AlignCenter)
            if col == self.图片行:
                self.更新表格备选图片(row)
        except Exception as e:
            提示 = f'抽取上一个视频尾帧出错: {str(e)}'
            print(提示)
            self.提示(提示)

    def zhantiexintu(self, current_row, current_col):
        clipboard = QApplication.clipboard()
        text = clipboard.text()
        try:
            if '.png' not in text:
                return
            if not jduvudu312us_usjlq.path.exists(text):
                return
            原始图片 = A0_config.改文件名(self.项目配置['data'][current_row]['img']['path'])
            if not 原始图片 or not jduvudu312us_usjlq.path.exists(原始图片):
                原始图片 = jduvudu312us_usjlq.path.join(self.图片文件夹, f"{self.项目配置['data'][current_row]['index']}(0).png")
                self.项目配置['data'][current_row]['img']['path'] = 原始图片
                self.项目配置['data'][current_row]['img_beixuan'].append({'path': 原始图片, 'seep': 0})
                self.保存配置()
            print(text, '粘贴到', 原始图片)
            shutil.copy(text, 原始图片)
            if jduvudu312us_usjlq.path.exists(原始图片):
                图片行 = QTableWidgetItem()
                pixmap = QPixmap(原始图片)
                # 增大图片显示尺寸，从160改为240
                width = int(pixmap.width() * self.缩放(240) / pixmap.height())
                scaled_pixmap = pixmap.scaledToWidth(width)
                图片行.setIcon(QIcon(scaled_pixmap))
                图片行.setTextAlignment(Qt.AlignCenter)
                图片行.setData(Qt.UserRole, 原始图片)
                if current_col == self.图片行:
                    self.table_widget.setItem(current_row, self.图片行, 图片行)
                else:  # inserted
                    self.table_widget.setItem(current_row, self.原图片行, 图片行)
                self.table_widget.setIconSize(pixmap.size())
                图片行.setTextAlignment(Qt.AlignCenter)
            if current_col == self.图片行:
                self.更新表格备选图片(current_row)
        except Exception as e:
            print(f'复制并替换新图时出错: {str(e)}')
            self.提示(f'替换图片时出错: {str(e)}')

    def tihuanxintu(self, current_row, current_col):
        try:
            options = QFileDialog.Options()
            file_dialog = QFileDialog()
            file_dialog.setNameFilter('PNG Files (*.png)')
            file_dialog.setFileMode(QFileDialog.ExistingFile)
            selected_file, _ = file_dialog.getOpenFileName(self, '选择PNG图片', '', 'PNG Files (*.png)', options=options)
            if selected_file!= '':
                if current_col == self.图片行:
                    原始图片 = A0_config.改文件名(self.项目配置['data'][current_row]['img']['path'])
                else:  # inserted
                    原始图片 = A0_config.改文件名(self.项目配置['data'][current_row]['img_yuantu'])
                try:
                    if not jduvudu312us_usjlq.path.exists(原始图片):
                        原始图片 = jduvudu312us_usjlq.path.join(self.图片文件夹, f"{self.项目配置['data'][current_row]['index']}(0).png")
                        self.项目配置['data'][current_row]['img']['path'] = 原始图片
                        self.项目配置['data'][current_row]['img_beixuan'].append({'path': 原始图片, 'seep': 0})
                        self.保存配置()
                    print(current_row, current_col, selected_file, 原始图片)
                    shutil.copy(selected_file, 原始图片)
                    if self.任务模式 == '视频转绘':
                        参考图片 = self.项目配置['data'][current_row]['img_cn']
                        shutil.copy(selected_file, 参考图片)
                    if jduvudu312us_usjlq.path.exists(原始图片):
                        图片行 = QTableWidgetItem()
                        pixmap = QPixmap(原始图片)
                        # 增大图片显示尺寸，从160改为240
                        width = int(pixmap.width() * self.缩放(240) / pixmap.height())
                        scaled_pixmap = pixmap.scaledToWidth(width)
                        图片行.setIcon(QIcon(scaled_pixmap))
                        图片行.setTextAlignment(Qt.AlignCenter)
                        图片行.setData(Qt.UserRole, 原始图片)
                        if current_col == self.图片行:
                            self.table_widget.setItem(current_row, self.图片行, 图片行)
                        else:  # inserted
                            self.table_widget.setItem(current_row, self.原图片行, 图片行)
                        self.table_widget.setIconSize(pixmap.size())
                        图片行.setTextAlignment(Qt.AlignCenter)
                    if current_col == self.图片行:
                        self.更新表格备选图片(current_row)
                except Exception as e:
                    print(f'复制并替换新图时出错: {str(e)}')
                    self.提示(f'替换图片时出错: {str(e)}')
        except Exception as e:
            print(f'替换图片错误{e}')
            self.提示(f'替换图片错误{e}')

    def resetting_tu(self, current_row, current_col):
        try:
            reply = QMessageBox.question(self, '确认', '确认当前分镜所有图片, 删除后不可恢复', QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
            if reply == QMessageBox.Yes:
                self.项目配置['data'][current_row]['img'] = {'path': '', 'seep': ''}
                for 图片 in self.项目配置['data'][current_row]['img_beixuan']:
                    # 处理不同的数据格式
                    if isinstance(图片, dict):
                        图片路径 = 图片.get('path', '')
                    elif isinstance(图片, str):
                        图片路径 = 图片
                    else:
                        continue

                    if 图片路径 and jduvudu312us_usjlq.path.exists(A0_config.改文件名(图片路径)):
                        jduvudu312us_usjlq.remove(A0_config.改文件名(图片路径))
                self.项目配置['data'][current_row]['img_beixuan'] = []
                self.保存配置()
                图片行 = QTableWidgetItem('')
                self.table_widget.setItem(current_row, self.图片行, 图片行)
                cell_widget = QWidget(self)
                item_image = QLabel()
                item_image.setPixmap(QPixmap())
                self.table_widget.setCellWidget(current_row, self.备选图片行, cell_widget)
        except Exception as e:
            self.提示(f'执行出错: {e}')

    def resetting_tu_vi_th(self, current_row, current_col):
        options = QFileDialog.Options()
        file_dialog = QFileDialog()
        file_dialog.setNameFilter('MP4 Files (*.mp4)')
        file_dialog.setFileMode(QFileDialog.ExistingFile)
        selected_file, _ = file_dialog.getOpenFileName(self, '选择MP4图片', '', 'MP4 Files (*.mp4)', options=options)
        try:
            if selected_file!= '':
                原始图片 = A0_config.改文件名(self.项目配置['data'][current_row]['img']['path'])
                if not 原始图片 or not jduvudu312us_usjlq.path.exists(原始图片):
                    原始图片 = jduvudu312us_usjlq.path.join(self.图片文件夹, f"{self.项目配置['data'][current_row]['index']}(0).png")
                    self.项目配置['data'][current_row]['img']['path'] = 原始图片
                    self.项目配置['data'][current_row]['img_beixuan'].append({'path': 原始图片, 'seep': 0})
                else:  # inserted
                    备份图片组 = self.项目配置['data'][current_row]['img_beixuan']
                    max_number = 文件名最大数(备份图片组)
                    原始图片 = jduvudu312us_usjlq.path.join(self.图片文件夹, f"{self.项目配置['data'][current_row]['index']}({max_number + 1}).png")
                    self.项目配置['data'][current_row]['img']['path'] = 原始图片
                    self.项目配置['data'][current_row]['img_beixuan'].append({'path': 原始图片, 'seep': 0})
                备选视频组 = self.项目配置['data'][current_row]['video_beixuan']
                最大值 = 文件名最大数(备选视频组)
                历史文件名 = f"{self.项目配置['data'][current_row]['index']}({最大值 + 1}).mp4"
                历史文件名 = jduvudu312us_usjlq.path.join(self.视频文件夹, 历史文件名)
                self.项目配置['data'][current_row]['video_beixuan'].append({'path': 历史文件名, 'img': 原始图片})
                self.项目配置['data'][current_row]['video'] = 历史文件名
                shutil.copy(selected_file, 历史文件名)
                cap = cv2.VideoCapture(历史文件名)
                if not cap.isOpened():
                    print('无法打开视频文件')
                else:  # inserted
                    ret, frame = cap.read()
                    if ret:
                        image = Image.fromarray(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
                        image.save(原始图片)
                cap.release()
                if jduvudu312us_usjlq.path.exists(原始图片):
                    图片行 = QTableWidgetItem()
                    pixmap = QPixmap(原始图片)
                    # 增大图片显示尺寸，从160改为240
                    width = int(pixmap.width() * self.缩放(240) / pixmap.height())
                    scaled_pixmap = pixmap.scaledToWidth(width)
                    图片行.setIcon(QIcon(scaled_pixmap))
                    图片行.setTextAlignment(Qt.AlignCenter)
                    图片行.setData(Qt.UserRole, 原始图片)
                    if current_col == self.图片行:
                        self.table_widget.setItem(current_row, self.图片行, 图片行)
                    else:  # inserted
                        self.table_widget.setItem(current_row, self.原图片行, 图片行)
                    self.table_widget.setIconSize(pixmap.size())
                    图片行.setTextAlignment(Qt.AlignCenter)
                if current_col == self.图片行:
                    self.更新表格备选图片(current_row)
                self.保存配置()
        except Exception as e:
            print(f'替换视频时出错: {str(e)}')
            self.提示(f'替换视频时出错: {str(e)}')

    def resetting_tu_vi(self, current_row, current_col):
        try:
            reply = QMessageBox.question(self, '确认', '将删除当前分镜视频及分镜备份视频, 删除后不可恢复', QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
            if reply == QMessageBox.Yes:
                视频 = self.项目配置['data'][current_row]['video']
                if jduvudu312us_usjlq.path.exists(A0_config.改文件名(视频)):
                    jduvudu312us_usjlq.remove(A0_config.改文件名(视频))
                备份视频 = self.项目配置['data'][current_row]['video_beixuan']
                for d in 备份视频:
                    if d.get('path') and jduvudu312us_usjlq.path.exists(A0_config.改文件名(d.get('path'))):
                        jduvudu312us_usjlq.remove(A0_config.改文件名(d.get('path')))
                self.项目配置['data'][current_row]['video_beixuan'] = []
                self.保存配置()
        except Exception as e:
            self.提示(f'执行出错: {e}')

    def resetting_all_tu(self):
        try:
            selected_items = self.table_widget.selectedItems()
            if len(selected_items) == 1:
                reply = QMessageBox.question(self, '确认', '确认删除所有分镜图片, 删除后不可恢复', QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
                if reply == QMessageBox.Yes:
                    reply = QMessageBox.question(self, '确认', '即将删除所有分镜图片, 是否删除', QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
                    if reply == QMessageBox.Yes:
                        for index, data in enumerate(self.项目配置['data']):
                            self.项目配置['data'][index]['img'] = {'path': '', 'seep': ''}
                            for 图片 in self.项目配置['data'][index]['img_beixuan']:
                                if jduvudu312us_usjlq.path.exists(A0_config.改文件名(图片['path'])):
                                    jduvudu312us_usjlq.remove(A0_config.改文件名(图片['path']))
                            self.项目配置['data'][index]['img_beixuan'] = []
                            图片行 = QTableWidgetItem('')
                            self.table_widget.setItem(index, self.图片行, 图片行)
                            cell_widget = QWidget(self)
                            item_image = QLabel()
                            item_image.setPixmap(QPixmap())
                            self.table_widget.setCellWidget(index, self.备选图片行, cell_widget)
                        self.保存配置()
        except Exception as e:
            self.提示(f'执行出错: {e}')

    def resetting_all_tu_vi(self):
        try:
            selected_items = self.table_widget.selectedItems()
            if len(selected_items) == 1:
                reply = QMessageBox.question(self, '确认', '确认删除所有视频, 删除后不可恢复', QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
                if reply == QMessageBox.Yes:
                    reply = QMessageBox.question(self, '确认', '即将删除所有视频, 是否删除', QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
                    if reply == QMessageBox.Yes:
                        for index, data in enumerate(self.项目配置['data']):
                            视频 = data['video']
                            if jduvudu312us_usjlq.path.exists(A0_config.改文件名(视频)):
                                jduvudu312us_usjlq.remove(A0_config.改文件名(视频))
                            备份视频 = data['video_beixuan']
                            for d in 备份视频:
                                if d.get('path') and jduvudu312us_usjlq.path.exists(A0_config.改文件名(d.get('path'))):
                                    jduvudu312us_usjlq.remove(A0_config.改文件名(d.get('path')))
                            data['video_beixuan'] = []
                        self.保存配置()
        except Exception as e:
            self.提示(f'执行出错: {e}')

    def resetting_all_yuyin(self):
        try:
            selected_items = self.table_widget.selectedItems()
            if len(selected_items) == 1:
                reply = QMessageBox.question(self, '确认', '确认删除所有语音, 删除后不可恢复', QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
                if reply == QMessageBox.Yes:
                    for index, data in enumerate(self.项目配置['data']):
                        path = A0_config.改文件名(self.项目配置['data'][index]['yuyin'])
                        if jduvudu312us_usjlq.path.exists(path):
                            jduvudu312us_usjlq.remove(path)
        except Exception as e:
            self.提示(f'执行出错: {e}')

    def 清理重复备选图片(self, row_index):
        """清理指定行的重复备选图片和不存在的图片"""
        try:
            if 'img_beixuan' not in self.项目配置['data'][row_index]:
                return

            备选图片列表 = self.项目配置['data'][row_index]['img_beixuan']
            seen_paths = set()
            清理后列表 = []

            for item in 备选图片列表:
                # 处理不同的数据格式
                if isinstance(item, dict):
                    # 新格式：{'path': '路径', 'seep': 种子}
                    path = item.get('path', '')
                elif isinstance(item, str):
                    # 旧格式：直接是路径字符串
                    path = item
                else:
                    print(f"⚠️ 清理时发现未知的图片数据格式: {type(item)} - {item}")
                    continue

                if path and path not in seen_paths:
                    # 检查文件是否存在
                    actual_path = A0_config.改文件名(path)
                    if jduvudu312us_usjlq.path.exists(actual_path):
                        seen_paths.add(path)
                        清理后列表.append(item)

            # 如果发现重复或不存在的文件，更新配置
            if len(清理后列表) != len(备选图片列表):
                removed_count = len(备选图片列表) - len(清理后列表)
                print(f"[调试] 第{row_index+1}行清理了{removed_count}个重复或不存在的图片")
                self.项目配置['data'][row_index]['img_beixuan'] = 清理后列表
                self.保存配置()

        except Exception as e:
            print(f"清理重复备选图片时出错: {e}")

    def 清理所有重复备选图片(self):
        """清理所有行的重复备选图片"""
        try:
            for i in range(len(self.项目配置.get('data', []))):
                self.清理重复备选图片(i)
        except Exception as e:
            print(f"清理所有重复备选图片时出错: {e}")

    def 修复备选图片数据(self, row):
        """修复指定行的备选图片数据，从图片文件夹中扫描并补充缺失的图片"""
        try:
            if row >= len(self.项目配置.get('data', [])):
                return

            row_data = self.项目配置['data'][row]
            row_index = row_data.get('index', row + 1)

            # 确保img_beixuan字段存在
            if 'img_beixuan' not in row_data:
                row_data['img_beixuan'] = []

            # 获取当前已有的备选图片路径
            existing_paths = set()
            for item in row_data['img_beixuan']:
                if 'path' in item:
                    existing_paths.add(item['path'])

            # 扫描图片文件夹，查找该行对应的所有图片
            import glob
            import random

            # 构建搜索模式：行号(数字).png
            search_pattern = jduvudu312us_usjlq.path.join(self.图片文件夹, f"{row_index}(*.png")
            found_images = glob.glob(search_pattern)

            # 如果没有找到图片，尝试其他可能的文件扩展名
            if not found_images:
                for ext in ['*.jpg', '*.jpeg', '*.bmp', '*.gif']:
                    alt_pattern = jduvudu312us_usjlq.path.join(self.图片文件夹, f"{row_index}({ext}")
                    found_images.extend(glob.glob(alt_pattern))

            # 添加缺失的图片到备选列表
            added_count = 0
            for img_path in found_images:
                if img_path not in existing_paths and jduvudu312us_usjlq.path.exists(img_path):
                    # 生成随机种子
                    random_seed = random.randint(1000000000, 9999999999)
                    row_data['img_beixuan'].append({
                        'path': img_path,
                        'seep': random_seed
                    })
                    added_count += 1

            # 如果添加了新图片，保存配置并输出调试信息
            if added_count > 0:
                print(f"[调试] 第{row+1}行修复了{added_count}张备选图片，总数：{len(row_data['img_beixuan'])}")
                self.保存配置()

        except Exception as e:
            print(f"修复第{row+1}行备选图片数据时出错: {e}")

    def 修复所有备选图片数据(self):
        """修复所有行的备选图片数据"""
        try:
            print("[调试] 开始修复所有备选图片数据...")
            total_added = 0
            for i in range(len(self.项目配置.get('data', []))):
                before_count = len(self.项目配置['data'][i].get('img_beixuan', []))
                self.修复备选图片数据(i)
                after_count = len(self.项目配置['data'][i].get('img_beixuan', []))
                added = after_count - before_count
                total_added += added
                if added > 0:
                    print(f"[调试] 第{i+1}行添加了{added}张备选图片")

            if total_added > 0:
                print(f"[调试] 总共修复了{total_added}张备选图片")
            else:
                print("[调试] 所有备选图片数据完整，无需修复")

        except Exception as e:
            print(f"修复所有备选图片数据时出错: {e}")

    def chaifen_yc(self):
        try:
            # 原有拆分功能代码已移除，保留空函数避免调用错误
            pass
        except Exception as e:
            self.提示(f'拆分功能错误: {e}')

    def _original_chaifen_logic(self):
        """保留原有的拆分逻辑代码结构"""
        try:
            # 以下是原有的拆分逻辑，暂时保留但不使用
            # self.项目配置['data'][row]['fenjing'] = ''
                        for k, v in self.项目配置['fenjing'].items():
                            if k in data['txt'] and k not in data['fenjing']:
                                data['fenjing'] = f"{k},{data['fenjing']}".rstrip(',')
                        self.项目配置['data'].insert(row + 1, data)
                        self.保存配置()
                        print(A0_config.改文件名(self.项目配置['data'][row]['img_yuantu']), jduvudu312us_usjlq.path.join(self.原始图片文件夹, f'{图片编号}.png'))
                        if jduvudu312us_usjlq.path.exists(A0_config.改文件名(self.项目配置['data'][row]['img_yuantu'])):
                            shutil.copy(A0_config.改文件名(self.项目配置['data'][row]['img_yuantu']), data['img_yuantu'])
                        if self.任务模式 == '原创模式':
                            self.fenjing(self.项目配置['data'][row]['fenjing'], row)
                        # 更新内容行的自定义文本组件
                        内容行_widget = self.table_widget.cellWidget(row, self.内容行)
                        if 内容行_widget:
                            内容文本框 = 内容行_widget.layout().itemAt(0).widget()
                            if isinstance(内容文本框, CustomTextEdit_content):
                                内容文本框.setText(self.项目配置['data'][row]['txt'])
                        self.table_widget.insertRow(row + 1)
                        # 使用自定义文本组件显示拆分内容，支持自动换行
                        拆分内容行_widget = QWidget(self)
                        拆分内容行_layout = QVBoxLayout(拆分内容行_widget)
                        拆分内容行_layout.setContentsMargins(0, 0, 0, 0)
                        拆分内容文本框 = CustomTextEdit_content(self, row + 1, self)
                        拆分内容文本框.setText(data['txt'])
                        拆分内容行_layout.addWidget(拆分内容文本框)
                        self.table_widget.setCellWidget(row + 1, self.内容行, 拆分内容行_widget)
                        if self.任务模式 == '原创模式':
                            self.fenjing_yy(row + 1)
                            视频提示词_w = QWidget(self)
                            垂直布局_v = QVBoxLayout(视频提示词_w)
                            垂直布局_v.setContentsMargins(0, 0, 0, 0)
                            视频文本框 = CustomTextEdit_shipin(self, row + 1, self)
                            视频文本框.focusOutEvent = lambda event: self.视频文本框失焦(event, 视频文本框, row + 1)
                            垂直布局_v.addWidget(视频文本框)
                            self.table_widget.setCellWidget(row + 1, self.动图提示词行, 视频提示词_w)
                            正面词行_widget = QWidget(self)
                            垂直布局 = QVBoxLayout(正面词行_widget)
                            垂直布局.setContentsMargins(0, 0, 0, 0)
                            search_model = QStandardItemModel()
                            文本框 = CustomTextEdit(self, self)
                            # 优先显示中文prompt，如果没有则显示英文prompt
                            显示内容 = data.get('prompt_cn', data.get('prompt', ''))
                            文本框.setText(显示内容)
                            下拉框 = QListView(self)
                            下拉框.setViewMode(QListView.ListMode)
                            下拉框.setMovement(QListView.Static)
                            下拉框.setVerticalScrollMode(QListView.ScrollPerPixel)
                            下拉框.setModel(search_model)
                            水平布局 = QHBoxLayout()
                            水平布局.addWidget(文本框)
                            水平布局.addWidget(下拉框)
                            下拉框.hide()
                            水平布局 = QHBoxLayout()
                            水平布局.addWidget(文本框)
                            水平布局.addWidget(下拉框)
                            下拉框.hide()
                            近景 = QRadioButton('近景')
                            中景 = QRadioButton('中景')
                            远景 = QRadioButton('远景')
                            景深布局 = QHBoxLayout()
                            景深布局.addWidget(近景)
                            景深布局.addWidget(中景)
                            景深布局.addWidget(远景)
                            垂直布局.addLayout(水平布局)
                            垂直布局.addLayout(景深布局)
                            中景.setChecked(True)
                            self.table_widget.setCellWidget(row + 1, self.正面词行, 正面词行_widget)
                            下拉框.clicked.connect(lambda event: self.点击下拉框(event, 文本框, search_model, row + 1, 下拉框))
                            文本框.textChanged.connect(lambda: self.文字输入监控(文本框, 下拉框, search_model))
                            文本框.focusOutEvent = lambda event: self.文本框失焦(event, 文本框, row + 1, 下拉框, search_model)
                            近景.clicked.connect(lambda event: self.点击单选框(近景, row + 1))
                            中景.clicked.connect(lambda event: self.点击单选框(中景, row + 1))
                            远景.clicked.connect(lambda event: self.点击单选框(远景, row + 1))
                        else:  # inserted
                            # 优先显示中文prompt，如果没有则显示英文prompt
                            显示内容 = data.get('prompt_cn', data.get('prompt', ''))
                            正面词行 = QTableWidgetItem(显示内容)
                            正面词行.setTextAlignment(Qt.AlignCenter)
                            self.table_widget.setItem(row + 1, self.正面词行, 正面词行)
                        图片编号行 = QTableWidgetItem(str(row + 2))
                        图片编号行.setTextAlignment(Qt.AlignCenter)
                        self.table_widget.setItem(row + 1, self.图片编号行, 图片编号行)
                        self.fenjing(data['fenjing'], row + 1)
                        if self.任务模式 == '克隆模式':
                            self.table_widget.item(row, self.修改文案行).setText(self.项目配置['data'][row]['txtxiugai'])
                            修改文案行 = QTableWidgetItem(data['txt'])
                            修改文案行.setTextAlignment(Qt.AlignCenter)
                            self.table_widget.setItem(row + 1, self.修改文案行, 修改文案行)
                            原图片行 = QTableWidgetItem()
                            原始图片 = A0_config.改文件名(data['img_yuantu'])
                            if jduvudu312us_usjlq.path.exists(原始图片):
                                pixmap = QPixmap(原始图片)
                                # 增大图片显示尺寸，从160改为240
                                width = int(pixmap.width() * self.缩放(240) / pixmap.height())
                                scaled_pixmap = pixmap.scaledToWidth(width)
                                原图片行.setIcon(QIcon(scaled_pixmap))
                                原图片行.setTextAlignment(Qt.AlignCenter)
                                原图片行.setData(Qt.UserRole, 原始图片)
                                原图尺寸 = pixmap.size()
                                self.table_widget.setItem(row + 1, self.原图片行, 原图片行)
                                if 原图尺寸:
                                    self.table_widget.setIconSize(原图尺寸)
                        cell_widget = QWidget(self)
                        操作组件 = QGridLayout(cell_widget)
                        if self.任务模式 == '原创模式':
                            推理改写 = QPushButton('推关键词')
                            推理改写.clicked.connect(lambda: self.reasoning(row + 1))
                        else:  # inserted
                            推理改写 = QPushButton('改写内容')
                            推理改写.clicked.connect(lambda: self.gaixie(row + 1))
                        重新绘图 = QPushButton('重新绘图')
                        重新绘图.clicked.connect(lambda: self.Redraw(row + 1))
                        制作视频 = QPushButton('制作动画')
                        制作视频.clicked.connect(lambda: self.make_video(row + 1))
                        语音合成 = QPushButton('语音试听')
                        语音合成.clicked.connect(lambda: self.yuyin(row + 1))
                        向上合并 = QPushButton('向上合并')
                        向上合并.clicked.connect(lambda: self.hebing('向上', row + 1))
                        向下合并 = QPushButton('分镜设置')
                        向下合并.setStyleSheet(f'background-color: {A0_config.警告色}')
                        向下合并.clicked.connect(lambda: self.镜头设置(row))
                        按钮 = [推理改写, 重新绘图, 制作视频, 语音合成, 向上合并, 向下合并]
                        for a in 按钮:
                            a.setFixedSize(self.缩放(100), self.缩放(25))
                        操作组件.addWidget(向上合并, 0, 0)
                        操作组件.addWidget(推理改写, 1, 0)
                        操作组件.addWidget(重新绘图, 2, 0)
                        操作组件.addWidget(语音合成, 3, 0)
                        操作组件.addWidget(制作视频, 4, 0)
                        操作组件.addWidget(向下合并, 5, 0)
                        self.table_widget.setCellWidget(row + 1, self.操作行, cell_widget)
                        for i in range(row + 1, len(self.项目配置['data'])):
                            item = self.table_widget.item(i, self.图片编号行)
                            if item is not None:
                                item.setText(str(i + 1))
                        self.updateButtonData()
        except Exception as e:
            print(f'拆分任务错误{e}')
            self.提示(f'拆分任务错误: {e}')

    def 镜头设置(self, row):
        try:
            if A0_config.NOVIP:
                self.提示('VIP专属功能, 请先升级为VIP')
                return
            dialog = jingtoushezhi(row=row, parent=self)
            dialog.exec_()
            运镜路径 = dialog.控制组件.路径数组
            self.项目配置['data'][row]['lujing'] = 运镜路径[:9]
            self.保存配置()
        except Exception as e:
            提示 = f'设置分镜错误, {e}'
            print(提示)
            self.提示(提示)

    def copyCell(self):
        selected_items = self.table_widget.selectedItems()
        if len(selected_items) == 1:
            clipboard = QApplication.clipboard()
            clipboard.setText(selected_items[0].text())

    def pasteCell(self):
        clipboard = QApplication.clipboard()
        text = clipboard.text()
        selected_items = self.table_widget.selectedItems()
        if len(selected_items) == 1:
            selected_item = selected_items[0]
            current_row = selected_item.row()
            current_col = selected_item.column()
            self.项目配置['data'][current_row]['prompt'] = text
            self.保存配置()
            selected_item.setText(text)

    def re_all(self):
        reply = QMessageBox.question(self, '确认', '确认重置所有关键词, 重置后不可恢复', QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        if reply == QMessageBox.Yes:
            for index, data in enumerate(self.项目配置['data']):
                data['prompt_video'] = ''
                视频提示词_w = QWidget(self)
                垂直布局_v = QVBoxLayout(视频提示词_w)
                垂直布局_v.setContentsMargins(0, 0, 0, 0)
                视频文本框 = CustomTextEdit_shipin(self, index, self)
                视频文本框.focusOutEvent = lambda event: self.视频文本框失焦(event, 视频文本框, index)
                视频文本框.setText(data['prompt_video'])
                垂直布局_v.addWidget(视频文本框)
                self.table_widget.setCellWidget(index, self.动图提示词行, 视频提示词_w)
            self.保存配置()

    def tuili_one(self, row):
        self.当前任务 = '单个视频推理'
        if not self.running:
            self.zhuxian = False
        if self.zhuxian:
            print('有任务运行中')
            self.提示(f'{A0_config.name}, 有任务运行中')
            return
        self.修改第二个按钮文字(row, '正在推理', 1)
        self.单次任务 = '视频推理'
        self.运行行数 = row
        self.worker_solo.add_task(self.单次任务, self.运行行数)

    def re_one(self):
        selected_items = self.table_widget.selectedItems()
        selected_item = selected_items[0]
        current_row = selected_item.row()
        self.项目配置['data'][current_row]['prompt_video'] = ''
        item = QTableWidgetItem('')
        self.table_widget.setItem(current_row, self.动图提示词行, item)
        self.保存配置()

    def resetting_all(self):
        try:
            reply = QMessageBox.question(self, '确认', '确认重置所有关键词, 重置后不可恢复', QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
            if reply == QMessageBox.Yes:
                for index, data in enumerate(self.项目配置['data']):
                    self.项目配置['data'][index]['prompt'] = ''
                    self.项目配置['data'][index]['prompt_cn'] = ''  # 同时清空中文提示词
                    self.项目配置['data'][index]['miaoshu'] = ''
                    if self.任务模式 == '原创模式':
                        cell_widget = self.table_widget.cellWidget(index, self.正面词行)
                        layout = cell_widget.layout()
                        for j in range(layout.count()):
                            item = layout.itemAt(j)
                            if isinstance(item, QLayout):
                                layout_item = item.layout()
                                所有组件 = []
                                for i in range(layout_item.count()):
                                    if layout_item.itemAt(i).widget():
                                        所有组件.append(layout_item.itemAt(i).widget())
                                for 组件 in 所有组件:
                                    if isinstance(组件, CustomTextEdit):
                                        if layout_item.indexOf(组件) == 0:
                                            组件.setPlainText('')
                    else:  # inserted
                        正面词行 = QTableWidgetItem('')
                        正面词行.setTextAlignment(Qt.AlignCenter)
                        self.table_widget.setItem(index, self.正面词行, 正面词行)
                self.保存配置()
        except Exception as e:
            self.提示(f'执行出错: {e}')

    def 缩放(self, 像素):
        self.比例 = self.height_s / 1080
        return int(像素 * self.比例)

    def _webui_post_重试(self, url, data, headers, max_retries=6):
        """
        WebUI POST请求重试机制，支持502、503等错误的自动重试

        Args:
            url: 请求URL
            data: 请求数据
            headers: 请求头
            max_retries: 最大重试次数，默认6次
        """
        import time
        import json

        for attempt in range(max_retries):
            try:
                # 根据重试次数调整延迟
                if attempt > 0:
                    delay = min(9 + (attempt * 3), 30)  # 9s, 12s, 15s, 18s, 21s, 24s (最大30s)
                    print(f"[重试] WebUI POST请求第{attempt+1}次尝试，等待{delay}秒...")
                    time.sleep(delay)

                response = requests.post(url, data=json.dumps(data), headers=headers, timeout=300)

                if response.status_code == 200:
                    return response
                elif response.status_code == 502:
                    print(f"[重试] WebUI 502错误 (尝试 {attempt+1}/{max_retries})")
                    if attempt < max_retries - 1:
                        continue
                    else:
                        print(f"[错误] WebUI 502错误重试失败")
                        return None
                elif response.status_code == 503:
                    print(f"[重试] WebUI 503错误 (尝试 {attempt+1}/{max_retries})")
                    if attempt < max_retries - 1:
                        continue
                    else:
                        print(f"[错误] WebUI 503错误重试失败")
                        return None
                else:
                    print(f"[错误] WebUI HTTP错误 {response.status_code}")
                    return None

            except requests.exceptions.Timeout as e:
                print(f"[重试] WebUI请求超时 (尝试 {attempt+1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    continue
                else:
                    print(f"[错误] WebUI请求超时重试失败")
                    return None

            except requests.exceptions.ConnectionError as e:
                print(f"[重试] WebUI连接错误 (尝试 {attempt+1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    continue
                else:
                    print(f"[错误] WebUI连接错误重试失败")
                    return None

            except Exception as e:
                error_str = str(e).lower()
                print(f"[错误] WebUI请求异常 (尝试 {attempt+1}/{max_retries}): {e}")

                # 检查是否是可重试的异常
                retry_errors = ['502', '503', 'bad gateway', 'service unavailable', 'timeout']
                should_retry = any(error in error_str for error in retry_errors)

                if should_retry and attempt < max_retries - 1:
                    print(f"[重试] 检测到可重试异常，将重试")
                    continue
                else:
                    print(f"[错误] {'不可重试的异常' if not should_retry else '异常重试失败'}")
                    return None

        return None

    def 弹窗停止(self, 提示内容=None, **kwargs):
        text = ''
        if A0_config.config['SDurl数量'] > 1 and (self.当前任务 in ['批量绘图任务', '批量视频任务', '批量放大任务'] or self.单次任务 in ['图片', '视频']) and (not A0_config.NOVIP):
            text = '其他线程运行中, 等待完成即可, 如有问题查看日志截图\n'
        else:  # inserted
            self.running = False
            self.当前进度 = self.任务总数
        try:
            if self.tanchuang:
                self.tanchuang = False
                if 'VIP' in 提示内容 or '成功' in 提示内容:
                    QTimer.singleShot(0, lambda: self.提示(f'{提示内容}'))
                else:  # inserted
                    QTimer.singleShot(0, lambda: self.提示(f'{text}错误信息: \n{提示内容[:500]}\n画图问题请查看SD日志窗口\n其他问题查看软件日志\n如需帮助请点击左下角打开日志文件并发送截图'))
                self.停止 = True
        except Exception as e:
            print(e)

    def 提示(self, 提示内容=None, **kwargs):
        self.running = False
        try:
            if self.tanchuang:
                self.tanchuang = False
                print(提示内容)
                if 'VIP' in 提示内容:
                    messagebox.showinfo('提示', f'{提示内容}')
                else:  # inserted
                    messagebox.showinfo('警告', f'错误信息: \n{提示内容[:3000]}\n画图问题请查看SD日志窗口\n其他问题查看软件日志\n如需帮助请点击左下角打开日志文件并发送截图')
                self.停止 = True
                self.当前进度 = self.任务总数
        except Exception as e:
            print(e)

    def 顶部按钮(self):
        self.当前项目文件夹 = jduvudu312us_usjlq.path.join(self.项目文件夹)
        if self.任务模式 == '原创模式':
            self.当前项目文件夹 = jduvudu312us_usjlq.path.join(self.项目文件夹, '原创项目')
        else:  # inserted
            if self.任务模式 == '克隆项目':
                self.当前项目文件夹 = jduvudu312us_usjlq.path.join(self.项目文件夹, '克隆项目')
            else:  # inserted
                if self.任务模式 == '视频转绘':
                    self.当前项目文件夹 = jduvudu312us_usjlq.path.join(self.项目文件夹, '转绘项目')
                else:  # inserted
                    if self.任务模式 == 'AI虚拟人':
                        self.当前项目文件夹 = jduvudu312us_usjlq.path.join(self.项目文件夹, '虚拟人项目')
        self.SD版本 = QComboBox()
        self.SD版本.setToolTip('推荐ComfyUI, SD两个不同的UI, ComfyUI效果更好\nWebUI对新手友好,自由度低 \nComfyUI节点式工作流, 要求配置低, 出图快')
        SD版本_options = ['ComfyUI', f'{A0_config.name}会员专享(免费)', 'ComfyUI-自配工作流', f'Flux-4步({round(A0_config.flux_4 * A0_config.VIP倍率 * A0_config.flux倍率, 3)}元/张)', f'Flux-20步({round(A0_config.flux_20 * A0_config.VIP倍率 * A0_config.flux倍率, 3)}元/张)', f'GPT-4o({round(A0_config.gpt4o * A0_config.VIP倍率 * A0_config.MJ倍率, 3)}元/次)', f'MJ快速({round(A0_config.mj_k * A0_config.VIP倍率 * A0_config.MJ倍率, 3)}元/次)', 'MJ自备(需外网)', 'WebUI']
        for option in SD版本_options:
            self.SD版本.addItem(option)
            if option == A0_config.config['SD版本']:
                self.SD版本.setCurrentText(option)
        self.SD版本.activated.connect(lambda: self.SD版本点击事件())
        self.新建任务 = QPushButton('新建任务', self)
        self.新建任务.clicked.connect(lambda: self.新建项目())
        self.重绘列表 = QComboBox(self)
        self.重绘列表.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.加载任务 = QPushButton('加载任务', self)
        self.加载任务.clicked.connect(lambda: self.加载历史任务())
        self.删除任务 = QPushButton('删除任务', self)
        self.删除任务.clicked.connect(lambda: self.删除历史任务())
        self.复制任务 = QPushButton('复制任务', self)
        self.复制任务.clicked.connect(lambda: self.复制当前任务())
        self.一键成片 = QPushButton('一键成片', self)
        self.一键成片.setToolTip('集合后面所有功能,\n原创模式: 先设置分镜后启动, 推理-画图(放大)-语音-视频\n克隆模式: 推理-画图(放大)-视频')
        self.一键成片.clicked.connect(lambda: self.执行任务('一键成片任务'))
        self.销毁镜像 = QPushButton('销毁镜像', self)
        self.销毁镜像.setToolTip('销毁云端镜像, 如果配置了仙宫云KEY, 会销毁全部配套镜像')
        self.云端部署 = QPushButton('云端部署', self)
        self.云端部署.setToolTip('部署云端镜像, 如果配置了仙宫云KEY, 会自动部署配套镜像')
        self.销毁镜像.setStyleSheet(f'background-color: {A0_config.失败色};')
        self.云端部署.setStyleSheet(f'background-color: {A0_config.警告色};')
        self.销毁镜像.clicked.connect(lambda: self.执行销毁镜像函数())
        self.云端部署.clicked.connect(lambda: self.执行云端部署函数())
        self.增加分镜 = QPushButton('增加分镜', self)
        self.增加分镜.setToolTip('增加任务的分镜, 以便推理时能更好的识别文案中人物的性别和特征\n可以是人物、场景等')
        self.增加分镜.clicked.connect(lambda: (print("🔍 增加分镜按钮被点击"), self.zengjiafenjing()))
        self.设置分镜 = QPushButton('设置分镜', self)
        self.设置分镜.setToolTip('主要用于固定场景风格、统一人物形象\n设置每个分镜的元素, 能在画图时让SD更准确的理解画面内容')
        self.设置分镜.clicked.connect(lambda: self.shezhifenjing())
        self.批量推理 = QPushButton('批量推理', self)
        self.批量推理.setToolTip('原创模式: ,需要配置好GPT后才可用, 通过GPT推理画面关键词\n部分镜头不准确可点击下面列表单独推理\n克隆模式: SD反推图片关键词, 需要启动SD后才可用')
        self.批量推理.clicked.connect(lambda: self.执行任务('批量推理任务'))
        self.批量改写 = QPushButton('批量改写', self)
        self.批量改写.setToolTip('批量修改克隆文案, 默认全部改, \n单条修改请点击列表中修改文案')
        self.批量改写.clicked.connect(lambda: self.执行任务('批量改写任务'))
        self.批量语音 = QPushButton('批量语音', self)
        self.批量语音.setToolTip(f'合成语音文件, 支持微软语音和{A0_config.name}语音克隆, \n微软语音: 报错检查网络, 或者文案内容是否为空\n{A0_config.name}语音: 需要下载安装包到本地启动服务即可用')
        self.批量语音.clicked.connect(lambda: self.执行任务('批量语音任务'))
        self.批量绘图 = QPushButton('批量绘图', self)
        self.批量绘图.setToolTip('启动SD后再画图, 如有报错看日志框, 根据提示操作')
        self.批量绘图.clicked.connect(lambda: self.执行任务('批量绘图任务'))
        self.批量放大 = QPushButton('批量放大', self)
        self.批量放大.setToolTip('放大原图, 与高清修复功能一样, \n已经放大过的图片不会处理, \n如需再次放大可调高放大倍数')
        self.批量放大.clicked.connect(lambda: self.执行任务('批量放大任务'))
        self.视频推理 = QPushButton('视频推理', self)
        self.视频推理.setToolTip('根据图片推理视频关键词, \n已经推理过的图片不会处理')
        self.视频推理.clicked.connect(lambda: self.执行任务('视频推理任务'))
        self.批量视频 = QPushButton('批量动画', self)
        self.批量视频.setToolTip('启动SD后再制作, 如有报错看日志框, 根据提示操作')
        self.批量视频.clicked.connect(lambda: self.执行任务('批量视频任务'))
        self.合成视频 = QPushButton('合成视频', self)
        self.合成视频.setToolTip('生成剪映工程文件, 提示成功后在剪映首页能看到, \n没有就重启剪映, 还没有就看日志框有没有错误, \n一般是图片不全, 再次点击批量绘图后再放大即可')
        self.合成视频.clicked.connect(lambda: self.执行任务('合成视频任务'))
        self.打开文件 = QPushButton('打开文件', self)
        self.打开文件.setStyleSheet(f'background-color: {A0_config.警告色}')
        self.打开文件.clicked.connect(lambda: self.open_folder(self.当前项目文件夹))
        self.停止运行 = QPushButton('停止运行', self)
        self.停止运行.setStyleSheet(f'background-color: {A0_config.失败色}')
        self.停止运行.clicked.connect(lambda: self.stop())

    def 执行销毁镜像函数(self):
        if A0_config.config.get('key_xgy'):
            if A0_config.config['强制销毁']:
                提示 = '将强制销毁所有云端镜像, 是否继续'
            else:  # inserted
                提示 = '将检测并等待云端comfyui完成后再销毁, 如果需要强制销毁可在 \"软件设置\" 中勾选 \"强制销毁\"'
            reply = QMessageBox.question(self, '确认', 提示, QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
            if reply == QMessageBox.Yes:
                self.销毁镜像.setText('正在销毁')
                self.worker_solo.add_task('云端销毁', 0)
        else:  # inserted
            self.提示('没有配置仙宫云访问令牌, 请先配置后操作')

    def 执行云端部署函数(self):
        if A0_config.config.get('key_xgy'):
            reply = QMessageBox.question(self, '确认', f"将自动部署 {A0_config.config['镜像数量']} 个云端镜像, 是否部署", QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
            if reply == QMessageBox.Yes:
                self.云端部署.setText('正在部署')
                self.云端部署.setStyleSheet(f'background-color: {A0_config.选中色}')
                self.worker_solo.add_task('云端部署', 0)
        else:  # inserted
            self.on_third_button_clicked(self.云端部署链接)

    def SD版本点击事件(self):
        A0_config.config['SD版本'] = self.SD版本.currentText()
        if self.SD版本.currentText() == 'WebUI':
            A0_config.config['云端地址'] = A0_config.config['云端地址'].replace('8188', '7860')
        else:  # inserted
            if self.SD版本.currentText() == 'ComfyUI':
                A0_config.config['云端地址'] = A0_config.config['云端地址'].replace('7860', '8188')
        A0_config.修改配置()

    def 反推关键词(self, img_file):
        api = A0_config.config['云端地址'].replace('，', ',').replace(' ', '').split(',')[0]
        if api.endswith('/'):
            api = api[:(-1)]
        image = Image.open(img_file)
        output = io.BytesIO()
        image.save(output, format='PNG')
        image_bytes = output.getvalue()
        encoded_image = base64.b64encode(image_bytes).decode('utf-8')
        data = {'image': encoded_image, 'model': 'deepdanbooru'}
        headers = {'accept': 'application/json', 'Content-Type': 'application/json'}
        url = f'{api}/sdapi/v1/interrogate'
        response = requests.post(url, headers=headers, json=data)
        print(response.text)
        data = response.json()['caption']
        print('原始关键词:', data)
        result = data.split(',')
        关键词 = []
        for 词 in result:
            词.replace('subtitled', '')
            if 'boy' in 词 or 'girl' in 词:
                关键词.append(f'({词}:1.5)')
            else:  # inserted
                关键词.append(词)
        result = ','.join(关键词)
        return result

    def 获取插件模型(self):
        self.openpos = ''
        self.canny = ''
        self.mlsd = ''
        self.lineart = ''
        self.ip_adapter = ''
        op模型 = A0_config.config['ControlNet模型']
        if not op模型:
            print('提示: ', '当前SD缺少必要模型, 爆款克隆效果将大幅下降和分镜参考图不可用')
            return
        for 模型 in op模型:
            if 'xl' in A0_config.config['当前选中模型']:
                if 'xl' in 模型.lower():
                    if 'openpose' in 模型:
                        self.openpos = 模型
                    if 'canny' in 模型:
                        self.canny = 模型
                    if 'mlsd' in 模型:
                        self.mlsd = 模型
                    if 'lineart' in 模型:
                        self.lineart = 模型
                    if 'adapter' in 模型:
                        self.ip_adapter = 模型
            else:  # inserted
                if 'xl' not in 模型.lower():
                    if 'openpose' in 模型:
                        self.openpos = 模型
                    if 'canny' in 模型:
                        self.canny = 模型
                    if 'mlsd' in 模型:
                        self.mlsd = 模型
                    if 'lineart' in 模型:
                        self.lineart = 模型
                    if 'adapter' in 模型:
                        self.ip_adapter = 模型
        return True

    def 执行任务(self, 任务):
        self.当前任务 = 任务
        if A0_config.异地登录:
            提示 = '账号在其他设备登录, 请重新登录后继续, 如果没有在其他设备登录检查网络是否异常, 开了代理或梯子请关闭后重新登录'
            self.提示(提示)
        else:  # inserted
            if '127.0' in A0_config.config['云端地址'] and self.当前任务 == '批量视频任务' and ('工作流' not in A0_config.config['SD版本']) and (not A0_config.config['口型对齐']) and (A0_config.name not in A0_config.config['当前选中动画模型']):
                提示 = '动态视频支持云端部署, 或自配工作流, 本地部署包尚未上线, 敬请期待'
                self.提示(提示)
            else:  # inserted
                执行任务 = ['合成视频任务', '批量推理任务', '批量绘图任务', '批量视频任务', '批量放大任务', '批量语音任务', '批量改写任务', '一键成片任务', '海外模式切换', '视频推理任务']
                if self.table_widget.rowCount() < 1 and self.当前任务 in 执行任务:
                    self.提示('请先新建或加载任务后继续')
                else:  # inserted
                    if self.任务模式 == '原创模式':
                        if 'UI' in A0_config.config['SD版本'] and A0_config.config['图片采样方法'] == '启动SD后刷新SD配置' and (self.当前任务 == '批量绘图任务' or self.当前任务 == '批量放大任务'):
                            print('请先点击\"设置\"-左上角\"刷新配置\"获取SD配置信息')
                            self.提示('请先点击\"设置\"-左上角\"刷新配置\"获取SD配置信息')
                            return
                        if self.当前任务 == '批量推理任务':
                            # 检查是否配置了推理平台
                            推理平台 = A0_config.config.get('ChatGPT端口', '')

                            # 如果是本地推理，不需要API密钥
                            if 推理平台 == '本地推理':
                                pass  # 本地推理不需要验证
                            else:
                                # 检查统一管理平台配置
                                api_key = A0_config.platform_manager.get_platform_api_key(推理平台)
                                if not api_key or api_key.strip() == '':
                                    print(f'平台 {推理平台} 未配置API密钥')
                                    self.提示(f'请在软件设置中配置 {推理平台} 平台的API密钥')
                                    return
                    else:  # inserted
                        if A0_config.config['图片采样方法'] == '启动SD后刷新SD配置':
                            if self.当前任务 == '批量推理任务' or self.当前任务 == '批量绘图任务' or self.当前任务 == '批量放大任务':
                                self.提示('请先点击\"设置\"-左上角\"刷新配置\"获取SD配置信息')
                                return
                    self.获取插件模型()
                    self.start_task()

    def 创建顶部布局(self):
        self.第一行 = QHBoxLayout()
        self.第一行.setContentsMargins(self.缩放(10), self.缩放(10), self.缩放(10), self.缩放(0))
        self.第一行.setSpacing(self.缩放(15))
        self.第一行.addWidget(self.SD版本)
        self.第一行.addWidget(self.新建任务)
        self.第一行.addWidget(self.重绘列表)
        self.第一行.addWidget(self.加载任务)
        self.第一行.addWidget(self.删除任务)
        self.第一行.addWidget(self.复制任务)
        self.第一行.addWidget(self.一键成片)
        self.第一行.addItem(QSpacerItem(0, 15, QSizePolicy.Expanding, QSizePolicy.Minimum))
        self.第一行.addWidget(self.云端部署)
        self.第一行.addWidget(self.销毁镜像)
        self.第一行.addWidget(self.打开文件)
        self.第一行.addWidget(self.停止运行)
        self.第二行 = QHBoxLayout()
        self.第二行.setContentsMargins(self.缩放(10), self.缩放(10), self.缩放(10), self.缩放(10))
        self.第二行.setSpacing(self.缩放(15))
        self.第二行.addWidget(self.批量改写)
        self.第二行.addWidget(self.增加分镜)
        self.第二行.addWidget(self.设置分镜)
        self.第二行.addWidget(self.批量推理)
        self.第二行.addWidget(self.批量语音)
        self.第二行.addWidget(self.批量绘图)
        self.第二行.addWidget(self.批量放大)
        self.第二行.addWidget(self.视频推理)
        self.第二行.addWidget(self.批量视频)
        self.第二行.addWidget(self.合成视频)

        # 定义一个通用的、完整的样式
        base_style = f"font-size: {self.文字大小}px; color: {A0_config.文字色}; background-color: {A0_config.按钮色}; border: 1px solid {A0_config.边框色};"

        for i in range(self.第一行.count()):
            widget = self.第一行.itemAt(i).widget()
            if widget is not None:
                widget.setFixedHeight(int(self.行高 * 1.3))
                if isinstance(widget, (QPushButton, QComboBox)):
                    widget.setStyleSheet(base_style)

        for i in range(self.第二行.count()):
            widget = self.第二行.itemAt(i).widget()
            if widget is not None:
                widget.setFixedHeight(int(self.行高 * 1.3))
                if isinstance(widget, (QPushButton, QComboBox)):
                    widget.setStyleSheet(base_style)

        # 为特殊按钮重新应用样式，覆盖背景色
        self.销毁镜像.setStyleSheet(base_style + f"background-color: {A0_config.失败色};")
        self.云端部署.setStyleSheet(base_style + f"background-color: {A0_config.警告色};")
        self.打开文件.setStyleSheet(base_style + f"background-color: {A0_config.警告色};")
        self.停止运行.setStyleSheet(base_style + f"background-color: {A0_config.失败色};")

    def 海外模式点击事件(self):
        if self.任务模式 == '原创模式':
            self.执行任务('海外模式切换')
        return None

    def 任务模式切换事件(self):
        if self.任务模式 == '原创模式':
            self.重置表格()
            self.table_widget.setRowCount(0)
            self.重绘列表.clear()
            显示组 = [self.加载任务, self.复制任务, self.一键成片, self.批量推理, self.增加分镜, self.设置分镜, self.批量绘图, self.批量放大, self.视频推理, self.批量语音, self.合成视频, self.视频推理, self.批量视频]
            隐藏组 = [self.批量改写]
            if A0_config.name_gpt in A0_config.config['ChatGPT端口']:
                if not A0_config.NOVIP and 端口 == '' and (A0_config.name_gpt in A0_config.config['ChatGPT端口']) and (A0_config.API == 'http://wsai.b50.cc'):
                    A0_config.config['推理增强'] = True
                    self.推理增强模式 = A0_config.config['推理增强']
                if A0_config.API!= 'http://wsai.b50.cc':
                    # A0_config.config['推理增强'] = False  # 允许所有接口使用推理增强
                    # self.推理增强模式 = A0_config.config['推理增强']
                    pass
            else:  # inserted
                # self.推理增强模式 = False  # 允许所有端口使用推理增强
                # A0_config.config['推理增强'] = False
                pass
        else:  # inserted
            if self.任务模式 == '克隆模式':
                self.重置表格()
                self.table_widget.setRowCount(0)
                self.重绘列表.clear()
                显示组 = [self.加载任务, self.复制任务, self.一键成片, self.批量改写, self.批量推理, self.设置分镜, self.批量绘图, self.批量放大, self.批量语音, self.合成视频]
                隐藏组 = [self.增加分镜, self.批量改写, self.视频推理, self.批量视频]
            else:  # inserted
                隐藏组 = [self.复制任务, self.批量改写, self.增加分镜, self.批量语音, self.视频推理, self.批量放大]
                显示组 = [self.设置分镜, self.批量推理, self.一键成片, self.加载任务, self.批量绘图, self.批量视频, self.合成视频]
                A0_config.config['图片高清修复'] = False
                A0_config.config['面部修复'] = False
                A0_config.config['图片采样方法'] = 'euler_ancestral'
                A0_config.config['调度器'] = 'simple'
                A0_config.config['图片采样步数'] = '20'
                A0_config.config['提示词相关性'] = '4'
        for data in 隐藏组:
            data.hide()
        for data in 显示组:
            data.show()
        self.run_code()
        self.隐藏组件()

    def set_image(self, 组件, image_path):
        if image_path:
            print(image_path)
            image = QImage(image_path)
            scaled_image = image.scaled(self.缩放(500), self.缩放(500), Qt.KeepAspectRatio)
            组件.setPixmap(QPixmap.fromImage(scaled_image))
            组件.setAlignment(Qt.AlignCenter)
            组件.mouseDoubleClickEvent = lambda event, extra_arg=image_path: self.on_image_label_double_clicked(event, extra_arg)
        else:  # inserted
            组件.setPixmap(QPixmap(''))

    def chongzhi_gpt(self):
        chognzhiui = ExampleDialog(self)
        chognzhiui.exec_()

    def 下载网络视频1(self, mood):
        try:
            download_url = 加密数据(self.视频链接.text())
            if download_url == '':
                messagebox.showinfo('提示', '请输入网络视频地址')
                return
            当前时间 = 加密数据(str(A0_config.获取时间戳()))
            data = {'download_url': download_url, 'token': 当前时间}
            response = A2_登录注册充值.登录请求(self).下载视频(data)
            if response:
                response_data = json.loads(解密数据(response['body']))
                print(response_data)
                if response_data['code'] == 200:
                    url = response_data['data']['video']
                    视频名称 = response_data['data']['text'][:10]
                    替换词 = ['?', '|', '\\', '<', '>', ':', '*', ' ', '？', '“', '”', '：', '/', '\n', '《', '》', '#', '，', '.', '~']
                    for 词 in 替换词:
                        视频名称 = 视频名称.replace(词, '')
                    host = urlparse(url).netloc
                    headers = {'Host': host, 'User-Agent': 'Mozilla/5.0 (Linux; Android 10; OXF-AN10) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.117 Mobile Safari/537.36', 'Accept-Encoding': 'gzip, deflate', 'Accept': '*/*', 'Connection': 'keep-alive'}
                    filename = jduvudu312us_usjlq.path.join(self.网络视频文件夹, f'{视频名称}_{int(time.time())}.mp4')
                    print(f'开始下载:{filename},\n视频链接: {url}')
                    response = requests.get(url, headers=headers, stream=True)
                    response.raise_for_status()
                    with open(filename, 'wb') as file:
                        for chunk in response.iter_content(chunk_size=8192):
                            file.write(chunk)
                    if jduvudu312us_usjlq.path.exists(filename):
                        print(f'{filename} 视频已下载完成。')
                        if A0_config.NOVIP:
                            A0_config.GPT统计(15000)
                        提示 = f'{filename} 视频VIP已下载完成。'
                        print(提示)
                        mood._signal.emit((提示, '弹窗', '弹窗'))
                        return True
                    print(f'{filename} 视频已下载失败, 可在日志文件找到视频链接, 复制到浏览器手动下载。')
                    提示 = f'{filename} 视频已下载失败, 可在日志文件找到视频链接, 复制到浏览器手动下载。'
                    mood._signal.emit((提示, '弹窗', '弹窗'))
                else:  # inserted
                    提示 = f'视频提取失败: {response_data} \n如果下载失败可在日志窗口点击连接手动下载'
                    print(提示)
                    mood._signal.emit((提示, '弹窗', '弹窗'))
            else:  # inserted
                print('网络链接失败: ', response.text)
                提示 = f'网络链接失败: {response.text}'
                mood._signal.emit((提示, '弹窗', '弹窗'))
        except Exception as e:
            print('下载视频错误: ', e)
            提示 = f'下载视频错误: {e}'
            mood._signal.emit((提示, '弹窗', '弹窗'))

    def 删除字符(self, a):
        替换词 = [' in ', 'the ', ' and ', ' this ', ' with ', ' an ', ' into ', ' of ', ' may ', ' at ', ' would ', ' for ', ' is ', ' it ', ' on ', ' are ', ' as ', ' such ', ' be ', ' can ', ' who ', ' character ', ' world ']
        for ci in 替换词:
            a = a.lower().replace(ci, '')
        return a

    def print_values(self, par):
        selected_character = par.微软声音.currentText()
        selected_character_voice = ''
        for character in par.人物:
            if selected_character in character.keys():
                selected_character_voice = character[selected_character]['声音']
                break
        selected_role = par.微软角色.currentText()
        selected_role_key = ''
        for key, value in par.角色.items():
            if key == selected_role:
                selected_role_key = value
                break
        selected_style = par.声音风格.currentText()
        selected_style_key = ''
        for key, value in par.风格.items():
            if key == selected_style:
                selected_style_key = value
                break
        A0_config.config['微软声音'] = selected_character_voice
        A0_config.config['微软角色'] = selected_role_key
        A0_config.config['角色风格'] = selected_style_key

    def on_cell_double_clicked(self, item):
        row = item.row()
        column = item.column()
        if column == self.内容行 or column == self.分镜行 or column == self.正面词行 or (column == self.修改文案行) or (column == self.动图提示词行):
            if not self.editing_enabled:
                self.editing_enabled = True
                if self.row is not None:
                    self.table_widget.removeCellWidget(self.row, self.column)
                # 获取当前数据，处理自定义组件的情况
                if column == self.内容行:
                    # 内容行使用自定义文本组件
                    内容行_widget = self.table_widget.cellWidget(row, self.内容行)
                    if 内容行_widget:
                        内容文本框 = 内容行_widget.layout().itemAt(0).widget()
                        if isinstance(内容文本框, CustomTextEdit_content):
                            current_data = 内容文本框.toPlainText()
                        else:
                            current_data = ""
                    else:
                        current_data = ""
                else:
                    # 其他列使用普通的QTableWidgetItem
                    current_data = item.text() if item else ""
                self.editor = QTextEdit(current_data)
                self.context_menu = QMenu(self)
                copy_action = QAction('复制', self)
                paste_action = QAction('粘贴', self)
                copy_action.setShortcut(QKeySequence.Copy)
                paste_action.setShortcut(QKeySequence.Paste)
                copy_action.triggered.connect(self.copyText1)
                paste_action.triggered.connect(self.pasteText1)
                self.context_menu.addAction(copy_action)
                self.context_menu.addAction(paste_action)
                self.editor.setContextMenuPolicy(Qt.CustomContextMenu)
                self.editor.customContextMenuRequested.connect(self.showContextMenu1)
                self.table_widget.setCellWidget(row, column, self.editor)
                self.editor.textChanged.connect(lambda: self.on_cell_edit_finished(row, column))
                self.editor.focusOutEvent = lambda event: self.on_editor_focus_out(event, self.editor, row, column)

    def showContextMenu1(self, pos):
        self.context_menu.exec_(self.editor.mapToGlobal(pos))

    def copyText1(self):
        selected_text = self.editor.textCursor().selectedText()
        if selected_text:
            clipboard = QApplication.clipboard()
            clipboard.setText(selected_text)

    def pasteText1(self):
        clipboard = QApplication.clipboard()
        text = clipboard.text()
        cursor = self.editor.textCursor()
        cursor.insertText(text)

    def on_cell_edit_finished(self, row, column):
        editor = self.table_widget.cellWidget(row, column)
        new_data = self.editor.toPlainText()
        self.table_widget.setItem(row, column, QTableWidgetItem(new_data))
        self.row = row
        self.column = column
        self.editing_enabled = False

    def on_editor_focus_out(self, event, editor, row, column):
        QTextEdit.focusOutEvent(editor, event)
        new_data = self.editor.toPlainText()
        self.table_widget.removeCellWidget(row, column)
        if column == self.内容行:
            # 如果是内容行，使用自定义文本组件
            修改内容行_widget = QWidget(self)
            修改内容行_layout = QVBoxLayout(修改内容行_widget)
            修改内容行_layout.setContentsMargins(0, 0, 0, 0)
            修改内容文本框 = CustomTextEdit_content(self, row, self)
            修改内容文本框.setText(new_data)
            修改内容行_layout.addWidget(修改内容文本框)
            self.table_widget.setCellWidget(row, column, 修改内容行_widget)
        else:
            # 其他列使用普通的QTableWidgetItem
            修改内容行 = QTableWidgetItem(new_data)
            修改内容行.setTextAlignment(Qt.AlignCenter)
            self.table_widget.setItem(row, column, 修改内容行)
        self.execute_function(row, column)
        self.row = row
        self.column = column
        self.editing_enabled = False
        first_cell_item = self.table_widget.item(row, column)

    def execute_function(self, row, column):
        item = self.table_widget.item(row, column)
        print(item.text())
        if column == self.内容行:
            if self.项目配置['data'][row]['txt']!= item.text():
                self.项目配置['data'][row]['txt'] = item.text()
                self.项目配置['data'][row]['txt_en'] = item.text()
        if column == self.正面词行:
            self.项目配置['data'][row]['prompt'] = item.text()
        if column == self.分镜行:
            self.项目配置['data'][row]['fenjing'] = item.text()
        if column == self.修改文案行:
            self.项目配置['data'][row]['txtxiugai'] = item.text()
        if column == self.动图提示词行:
            self.项目配置['data'][row]['prompt_video'] = item.text()
        self.保存配置()

    def on_third_button_clicked(self, 链接):
        url = QUrl(链接)
        a = {'api2d': 'https://openai.api2d.net', 'close': 'https://api.closeai-proxy.xyz', 'moonshot': 'https://api.moonshot.cn', 'deep': 'https://mt.deepwl.shop', 'openai': 'https://api.openai.com', 'deepseek': 'https://api.deepseek.com'}
        for k, v in a.items():
            if k in 链接:
                A0_config.config['other接口地址'] = v
                break
        thread = threading.Thread(target=QDesktopServices.openUrl, args=(url,))
        thread.start()

    def run_code(self):
        self.重绘列表.clear()
        if self.任务模式 == '原创模式':
            项目文件夹 = jduvudu312us_usjlq.path.join(self.项目文件夹, '原创项目')
        else:  # inserted
            if self.任务模式 == '克隆模式':
                项目文件夹 = jduvudu312us_usjlq.path.join(self.项目文件夹, '克隆项目')
            else:  # inserted
                if self.任务模式 == 'AI虚拟人':
                    项目文件夹 = jduvudu312us_usjlq.path.join(self.项目文件夹, '虚拟人项目')
                else:  # inserted
                    项目文件夹 = jduvudu312us_usjlq.path.join(self.项目文件夹, '转绘项目')
        try:
            # 只扫描直接子目录，不递归扫描
            if jduvudu312us_usjlq.path.exists(项目文件夹):
                for item in jduvudu312us_usjlq.listdir(项目文件夹):
                    item_path = jduvudu312us_usjlq.path.join(项目文件夹, item)

                    # 只处理目录
                    if jduvudu312us_usjlq.path.isdir(item_path):
                        # 查找与目录名相同的 .json 配置文件
                        config_file = jduvudu312us_usjlq.path.join(item_path, f"{item}.json")

                        if jduvudu312us_usjlq.path.exists(config_file):
                            # 跳过特殊目录和文件
                            if (not item.startswith('tmp') and
                                not item.startswith('.') and
                                item != 'upload_records' and
                                item != 'image_bed_config'):
                                self.重绘列表.addItem(item)

            self.重绘列表.setCurrentText(self.当前项目)
        except Exception as e:
            print(f'内容拆分: {e}')
            self.提示(f'加载任务失败{e}')
            self.running = False
    def 新建项目(self, file=None):
        if self.running:
            print('有任务运行中')
            self.提示('有任务运行中')
            return
        print('增加新任务')
        self.当前项目 = ''
        self.项目内容 = ''
        try:
            dialog = xinjianxiangmu(self, file=file)
            result = dialog.exec_()
            if result == QDialog.Accepted:
                self.当前项目 = dialog.text_edit.text()
                是否复制分镜 = False
                复制分镜名称 = ''
                if self.任务模式 == '原创模式':
                    self.当前项目文件夹 = jduvudu312us_usjlq.path.join(self.项目文件夹, '原创项目', self.当前项目)
                    是否复制分镜 = dialog.复制分镜.isChecked()
                    复制分镜名称 = dialog.任务列表.currentText()
                else:  # inserted
                    if self.任务模式 == '克隆模式':
                        self.当前项目文件夹 = jduvudu312us_usjlq.path.join(self.项目文件夹, '克隆项目', self.当前项目)
                    else:  # inserted
                        if self.任务模式 == '视频转绘':
                            self.当前项目文件夹 = jduvudu312us_usjlq.path.join(self.项目文件夹, '转绘项目', self.当前项目)
                self.项目内容 = dialog.text_edit1.text()
                self.项目音频 = dialog.text_edit2.text()
                self.项目视频 = dialog.text_edit3.text()
                self.剪映名称 = dialog.line_edit.currentText()
                self.导入剪映 = dialog.剪映工程导入.isChecked()
                self.单境字数 = int(dialog.最小字数.text())
                if self.单境字数 > 200:
                    self.单境字数 = 200
                self.图片文件夹 = jduvudu312us_usjlq.path.join(self.当前项目文件夹, '图片文件')
                self.视频文件夹 = jduvudu312us_usjlq.path.join(self.当前项目文件夹, '视频文件')
                self.原始图片文件夹 = jduvudu312us_usjlq.path.join(self.当前项目文件夹, '原始图片')
                self.音频文件夹 = jduvudu312us_usjlq.path.join(self.当前项目文件夹, '音频文件')
                self.项目配置文件 = jduvudu312us_usjlq.path.join(self.当前项目文件夹, f'{self.当前项目}.json')
                jduvudu312us_usjlq.makedirs(self.当前项目文件夹, exist_ok=True)
                jduvudu312us_usjlq.makedirs(self.图片文件夹, exist_ok=True)
                jduvudu312us_usjlq.makedirs(self.视频文件夹, exist_ok=True)
                jduvudu312us_usjlq.makedirs(self.原始图片文件夹, exist_ok=True)
                jduvudu312us_usjlq.makedirs(self.音频文件夹, exist_ok=True)
                self.视频路径 = jduvudu312us_usjlq.path.join(self.当前项目文件夹, f'{self.当前项目}.mp4')
                self.音频路径 = jduvudu312us_usjlq.path.join(self.当前项目文件夹, f'{self.当前项目}.mp3')
                self.字幕路径 = jduvudu312us_usjlq.path.join(self.当前项目文件夹, f'{self.当前项目}.srt')
                if self.导入剪映 and self.任务模式!= '视频转绘':
                    self.当前项目剪映工程文件 = jduvudu312us_usjlq.path.join(A0_config.config['剪映草稿文件夹'], self.剪映名称, 'draft_content.json')
                    self.当前项目剪映配置文件 = jduvudu312us_usjlq.path.join(A0_config.config['剪映草稿文件夹'], self.剪映名称, 'draft_meta_info.json')
                    if not jduvudu312us_usjlq.path.exists(self.视频路径):
                        with open(self.当前项目剪映配置文件, 'r', encoding='utf-8') as file:
                            data = json.load(file)
                        配置信息 = data['draft_materials']
                        工程视频 = ''
                        工程音频 = ''
                        for 配置 in 配置信息:
                            for value in 配置['value']:
                                if 'mp4' in value['file_Path'].lower():
                                    工程视频 = value['file_Path']
                                if 'mp3' in value['file_Path'].lower():
                                    工程音频 = value['file_Path']
                        if 工程视频!= '':
                            if jduvudu312us_usjlq.path.exists(工程视频):
                                shutil.copy(工程视频, self.视频路径)
                            else:  # inserted
                                self.提示('当前剪映工程视频不可用')
                                self.running = False
                                shutil.rmtree(self.当前项目文件夹)
                                return
                        else:  # inserted
                            self.提示('当前剪映工程没有视频')
                            self.running = False
                            shutil.rmtree(self.当前项目文件夹)
                            return
                        if 工程音频!= '':
                            if jduvudu312us_usjlq.path.exists(工程音频):
                                shutil.copy(工程音频, self.音频路径)
                    self.文案路径 = jduvudu312us_usjlq.path.join(self.当前项目文件夹, f'{self.当前项目}.srt')
                    字幕生成结果 = 生成字幕文件(self)
                    self.项目内容 = self.文案路径
                else:  # inserted
                    if self.任务模式!= '视频转绘':
                        字幕生成结果 = True
                        self.项目内容 = self.项目内容.split('file:///')[len(self.项目内容.split('file:///')) - 1]
                        self.项目音频 = self.项目音频.split('file:///')[len(self.项目音频.split('file:///')) - 1]
                        self.项目视频 = self.项目视频.split('file:///')[len(self.项目视频.split('file:///')) - 1]
                        print(self.当前项目, self.当前项目文件夹, self.项目内容, self.项目音频, self.项目视频)
                        if 'txt' in self.项目内容.lower():
                            self.文案路径 = jduvudu312us_usjlq.path.join(self.当前项目文件夹, f'{self.当前项目}.txt')
                        else:  # inserted
                            if 'srt' in self.项目内容.lower():
                                self.文案路径 = jduvudu312us_usjlq.path.join(self.当前项目文件夹, f'{self.当前项目}.srt')
                        if self.项目内容!= '':
                            shutil.copy(self.项目内容, self.文案路径)
                        if 'mp3' in self.项目音频.lower():
                            shutil.copy(self.项目音频, self.音频路径)
                        if 'mp4' in self.项目视频.lower():
                            shutil.copy(self.项目视频, self.视频路径)
                if self.任务模式 == '视频转绘':
                    self.segments = []
                    if self.导入剪映 and (not self.项目视频):
                        self.当前项目剪映工程文件 = jduvudu312us_usjlq.path.join(A0_config.config['剪映草稿文件夹'], self.剪映名称, 'draft_content.json')
                        self.当前项目剪映配置文件 = jduvudu312us_usjlq.path.join(A0_config.config['剪映草稿文件夹'], self.剪映名称, 'draft_meta_info.json')
                        with open(self.当前项目剪映配置文件, 'r', encoding='utf-8') as file:
                            data = json.load(file)
                        配置信息 = data['draft_materials']
                        工程视频 = ''
                        for 配置 in 配置信息:
                            for value in 配置['value']:
                                if 'mp4' in value['file_Path'].lower():
                                    工程视频 = value['file_Path']
                                    break
                        if 工程视频!= '':
                            if jduvudu312us_usjlq.path.exists(工程视频):
                                shutil.copy(工程视频, self.视频路径)
                                with open(self.当前项目剪映工程文件, 'r', encoding='utf-8') as file:
                                    data = json.load(file)
                                镜头信息 = data['tracks']
                                视频分镜信息 = []
                                字幕分镜信息 = []
                                for 镜头分类 in 镜头信息:
                                    if 镜头分类['type'] == 'video' and (not 字幕分镜信息):
                                        print('提取视频分镜')
                                        视频分镜信息 = 镜头分类['segments']
                                if len(视频分镜信息) < 2 and len(字幕分镜信息) < 2:
                                    print('没有镜头分割又没有字幕')
                                if len(视频分镜信息) < 2:
                                    print('没有镜头分割')
                                for i, 视频镜头分镜 in enumerate(视频分镜信息):
                                    镜头开始时间 = int(视频镜头分镜['source_timerange']['start']) / 1000000
                                    镜头持续时间 = int(视频镜头分镜['source_timerange']['duration']) / 1000000
                                    self.segments.append([镜头开始时间, 镜头持续时间])
                            else:  # inserted
                                self.提示('当前剪映工程视频不可用')
                                self.running = False
                                shutil.rmtree(self.当前项目文件夹)
                                return
                        else:  # inserted
                            print('工程视频不存在')
                            self.提示('当前剪映工程视频不可用')
                            self.running = False
                            shutil.rmtree(self.当前项目文件夹)
                            return
                    else:  # inserted
                        shutil.copy(self.项目视频, self.视频路径)
                    # 初始化基础项目配置
                    self.项目配置 = {
                        '视频路径': self.视频路径,
                        '音频路径': self.项目音频,
                        '文案路径': self.字幕路径,
                        '字幕路径': self.字幕路径,
                        'fenjing': {
                            '全局': {
                                '性别': '',
                                '称呼': '',
                                'guanjianci': '',
                                'guanjianci_zh': '',
                                'zhongzi': '',
                                'img': '',
                                'url': ''
                            }
                        },
                        'fj_yuyin': {
                            '旁白': {
                                '模型': '',
                                '声音': ''
                            }
                        },
                        '角色专用种子配置': {},
                        'data': []
                    }

                    print(f"[新建项目] 已初始化项目配置，包含角色专用种子配置")
                    self.执行任务('镜头分割任务')
                else:  # inserted
                    if 字幕生成结果:
                        # 初始化基础项目配置
                        import random
                        import datetime

                        默认种子 = random.randint(1000000000, 9999999999)
                        当前时间 = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                        self.项目配置 = {
                            '视频路径': self.视频路径,
                            '音频路径': self.音频路径,
                            '文案路径': self.文案路径,
                            '字幕路径': self.字幕路径,
                            'fenjing': {
                                '全局': {
                                    '性别': '',
                                    '称呼': '',
                                    'guanjianci': '',
                                    'guanjianci_zh': '',
                                    'zhongzi': '',
                                    'img': '',
                                    'url': ''
                                }
                            },
                            'fj_yuyin': {
                                '旁白': {
                                    '模型': '',
                                    '声音': ''
                                }
                            },
                            '角色专用种子配置': {},
                            'data': []
                        }
                        print(f"[新建项目] 已初始化项目配置，包含角色专用种子配置和默认种子配置 {默认种子}")
                        self.保存配置()
                        生成项目文档(self)
                        print('项目文件生成成功')
                        if self.任务模式 == '克隆模式':
                            self.执行任务('视频分镜')
                        else:  # inserted
                            if 是否复制分镜:
                                分镜项目配置文件 = A0_config.改文件名(jduvudu312us_usjlq.path.join(self.项目文件夹, '原创项目', 复制分镜名称, f'{复制分镜名称}.json'))
                                with open(分镜项目配置文件, 'r', encoding='utf-8') as file:
                                    分镜项目配置 = json.load(file)
                                if 分镜项目配置.get('fj_yuyin'):
                                    self.项目配置['fj_yuyin'] = 分镜项目配置['fj_yuyin']
                                分镜项目配置['fenjing'] = A0_config.分镜数组改字典(分镜项目配置['fenjing'])
                                for k, v in 分镜项目配置['fenjing'].items():
                                    self.项目配置['fenjing'][k] = v
                                    识别词 = v.get('称呼')
                                    原图片 = A0_config.改文件名(v.get('img')) if v.get('img') else ''
                                    print(原图片)
                                    if 原图片 and jduvudu312us_usjlq.path.exists(原图片):
                                        目标图片 = jduvudu312us_usjlq.path.join(self.项目文件夹, '原创项目', self.当前项目, jduvudu312us_usjlq.path.basename(原图片))
                                        try:
                                            shutil.copy(原图片, 目标图片)
                                        except Exception as e:
                                            print(f'复制参考图出错: {e}\n{原图片}\n{目标图片}')
                                        self.项目配置['fenjing'][k]['img'] = 目标图片
                                    if k!= '全局' and 识别词!= '':
                                        for index, data in enumerate(self.项目配置['data']):
                                            for 词 in 识别词.split('\n'):
                                                if 词 in data['txt'] and k not in data['fenjing'] and (k!= ''):
                                                    data['fenjing'] = f"{k},{data['fenjing']}".rstrip(',')
                                                    break
                                self.保存配置()
                            self.加载选中列表()
                            self.run_code()
                    else:  # inserted
                        shutil.rmtree(self.当前项目文件夹)
        except Exception as e:
            print(f'新建任务异常:{e}')
            if self.任务模式 == '原创模式':
                self.提示(f'新建任务异常,{e}')
            else:  # inserted
                self.提示(f'新建任务异常,请检查是否手动删减了视频, 版本是否为剪映5.5.0, 重新创建草稿:{e}')
            try:
                shutil.rmtree(self.当前项目文件夹)
            except:
                return

    def get_frame_from_video(self, video_path, output_path):
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            print('无法打开视频文件')
            return
        cap.set(cv2.CAP_PROP_POS_FRAMES, 48)
        ret, frame = cap.read()
        img = Image.fromarray(frame[..., ::(-1)])
        img.save(output_path)
        cap.release()

    def 保存配置(self):
        try:
            # 使用路径管理器保存配置，自动转换为相对路径
            from pyz.任务运行文件.path_manager import get_path_manager
            path_manager = get_path_manager()
            path_manager.save_config_with_relative_paths(self.项目配置, self.项目配置文件)
        except Exception as e:
            print(f'保存配置Error: {e}')
            # 如果路径管理器失败，回退到原始方法
            try:
                with tempfile.NamedTemporaryFile('w', dir=jduvudu312us_usjlq.path.dirname(self.项目配置文件), delete=False, encoding='utf-8') as tmpfile:
                    json.dump(self.项目配置, tmpfile, ensure_ascii=False, indent=4)
                jduvudu312us_usjlq.replace(tmpfile.name, self.项目配置文件)
            except Exception as e2:
                print(f'备用保存方法也失败: {e2}')

    def 处理绘图完成(self, 当前行, 图片路径, 已更新主图=False):
        """
        处理绘图完成后的界面更新
        统一处理所有绘图方式（包括Pollinations）的界面更新

        参数:
            当前行: 当前行号
            图片路径: 图片路径
            已更新主图: 是否已经更新过主图片配置（避免重复更新）
        """
        # print(f"[调试] ========== 处理绘图完成 ==========")
        # print(f"[调试] 当前行: {当前行+1}, 图片路径: {图片路径}")

        try:
            if 图片路径:
                # print(f"[调试] ========== 开始更新第{当前行+1}行界面 ==========")
                # print(f"[调试] 图片路径: {图片路径}")

                # 记录更新前的状态
                try:
                    更新前数量 = len(self.项目配置['data'][当前行]['img_beixuan'])
                    # print(f"[调试] 更新前备选图片数量: {更新前数量}")
                except:
                    # print(f"[调试] 无法获取更新前数量")
                    pass

                # 🔥 关键修复：确保新生成的图片设置为主图片（仅在未更新时）
                if not 已更新主图:
                    # print(f"[调试] 1. 更新项目配置中的主图片路径...")
                    if hasattr(self, '项目配置') and self.项目配置 and 'data' in self.项目配置:
                        if 当前行 < len(self.项目配置['data']):
                            # 更新主图片路径
                            self.项目配置['data'][当前行]['img']['path'] = 图片路径
                            # print(f"[调试] ✅ 主图片路径已更新: {图片路径}")

                            # 更新seed值（从备选图片中查找对应的seed）
                            for data in self.项目配置['data'][当前行]['img_beixuan']:
                                if data['path'] == 图片路径:
                                    self.项目配置['data'][当前行]['img']['seep'] = data['seep']
                                    # print(f"[调试] ✅ 主图片seed已更新: {data['seep']}")
                                    break

                            # 保存配置
                            self.保存配置()
                            # print(f"[调试] ✅ 项目配置已保存")
                else:
                    # print(f"[调试] 主图片配置已更新，跳过重复更新")
                    pass

                # 更新主图和备选图片界面显示
                # print(f"[调试] 2. 更新主图界面显示...")
                self.更新表格图片(当前行, 图片路径)

                # print(f"[调试] 3. 更新备选图片界面显示...")
                self.更新表格备选图片(当前行)

                # 记录更新后的状态
                try:
                    更新后数量 = len(self.项目配置['data'][当前行]['img_beixuan'])
                    # print(f"[调试] 更新后备选图片数量: {更新后数量}")

                    if 更新后数量 >= 更新前数量:
                        # print(f"[调试] ✅ 备选图片数量: {更新前数量} -> {更新后数量}")

                        # 显示最新的几张图片
                        最新图片 = self.项目配置['data'][当前行]['img_beixuan'][-3:]
                        # print(f"[调试] 最新3张备选图片:")
                        # for i, img in enumerate(最新图片):
                        #     filename = img['path'].split('/')[-1].split('\\')[-1]
                        #     print(f"[调试]   {i+1}: {filename}")
                        pass
                    else:
                        # print(f"[调试] ⚠️  备选图片数量没有变化")
                        pass
                except Exception as e:
                    # print(f"[调试] 无法获取更新后状态: {e}")
                    pass

                # 终极界面刷新方案
                # print(f"[调试] 3. 开始终极界面刷新...")
                try:
                    from PyQt5.QtWidgets import QApplication
                    from PyQt5.QtCore import QTimer

                    # 检查当前单元格状态
                    当前单元格 = self.table_widget.cellWidget(当前行, self.备选图片行)
                    # print(f"[调试] 当前备选图片单元格状态: {type(当前单元格).__name__ if 当前单元格 else 'None'}")

                    # 第1步：立即刷新
                    # print(f"[调试] 3.1 立即刷新表格...")
                    self.table_widget.update()
                    self.table_widget.repaint()
                    QApplication.processEvents()

                    # 第2步：强制重新加载备选图片（最关键！）
                    def 强制重新加载备选图片():
                        try:
                            # print(f"[调试] 3.2 强制重新加载第{当前行+1}行备选图片开始")

                            # 清除当前单元格
                            # print(f"[调试] 3.2.1 清除当前单元格...")
                            self.table_widget.setCellWidget(当前行, self.备选图片行, None)
                            QApplication.processEvents()

                            # 重新创建备选图片
                            # print(f"[调试] 3.2.2 重新创建备选图片组件...")
                            self.更新表格备选图片(当前行)
                            QApplication.processEvents()

                            # 检查重新创建后的状态
                            新单元格 = self.table_widget.cellWidget(当前行, self.备选图片行)
                            # print(f"[调试] 3.2.3 重新创建后单元格状态: {type(新单元格).__name__ if 新单元格 else 'None'}")

                            # 最终刷新
                            # print(f"[调试] 3.2.4 最终刷新...")
                            self.table_widget.viewport().update()
                            self.table_widget.viewport().repaint()
                            QApplication.processEvents()

                            # print(f"[调试] ✅ 第{当前行+1}行强制重新加载完成")
                        except Exception as e:
                            # print(f"[调试] ❌ 强制重新加载失败: {e}")
                            # import traceback
                            # print(f"[调试] 错误详情: {traceback.format_exc()}")
                            pass

                    # 延迟执行强制重新加载
                    # print(f"[调试] 3.3 设置200ms延迟重新加载...")
                    QTimer.singleShot(200, 强制重新加载备选图片)

                    # print(f"[调试] ✅ 第{当前行+1}行终极界面刷新启动成功")
                except Exception as e:
                    # print(f"[调试] ❌ 终极界面刷新失败: {e}")
                    # import traceback
                    # print(f"[调试] 错误详情: {traceback.format_exc()}")
                    pass

                # print(f"[调试] ========== 第{当前行+1}行界面更新完成 ==========")
            else:
                # print(f"[调试] 图片路径为空，跳过界面更新")
                pass

        except Exception as e:
            # print(f"[调试] 处理绘图完成失败: {e}")
            # import traceback
            # print(f"[调试] 错误详情: {traceback.format_exc()}")
            pass

        # print(f"[调试] ========== 处理绘图完成结束 ==========")

    def stop(self):
        try:
            if self.running:
                a = messagebox.askokcancel(title=A0_config.name, message='确认停止任务')
                if a:
                    if self.任务模式 == '视频转绘' or self.任务模式 == 'AI虚拟人':
                        api = A0_config.config['云端地址'].replace('，', ',').replace(' ', '').split(',')[0]
                        if api.endswith('/'):
                            api = api[:(-1)]
                        requests.post(f'{api}/interrupt')
                    self.running = False
                    print('停止执行,请等待本次结束')
            else:  # inserted
                print('当前没有运行任务')
                if self.任务模式 == '视频转绘' or self.任务模式 == 'AI虚拟人':
                    api = A0_config.config['云端地址'].replace('，', ',').replace(' ', '').split(',')[0]
                    if api.endswith('/'):
                        api = api[:(-1)]
                    requests.post(f'{api}/interrupt')
                self.running = False
                print('停止执行,请等待本次结束')
                self.提示('当前没有运行任务')
        except:
            return None

    def open_folder(self, folder_path):
        try:
            if jduvudu312us_usjlq.path.exists(folder_path):
                thread = threading.Thread(target=jduvudu312us_usjlq.startfile(folder_path))
                thread.start()
        except:
            return None

    def 保存参数(self, par):
        try:
            self.print_values(par)
            A0_config.config['当前选中模型'] = par.更换SD模型.currentText()
            A0_config.config['当前选中VAE'] = par.更换VAE模型.currentText()
            # 保留用户的工作流选择，不强制覆盖
            if '工作流' in A0_config.config['SD版本']:
                A0_config.config['选中工作流'] = par.更换VAE模型.currentText()
            # 注释掉强制设置默认工作流的逻辑，保持用户选择
            # else:  # inserted
            #     A0_config.config['选中工作流'] = '默认工作流'
            A0_config.config['图片长度'] = str(int(par.图片长度.text()) or 960)
            A0_config.config['图片宽度'] = str(int(par.图片宽度.text()) or 540)
            A0_config.config['正提示词'] = par.正提示词.text()
            A0_config.config['反提示词'] = par.反提示词.text()
            A0_config.config['转场样式'] = par.转场样式.currentText()
            A0_config.config['特效样式'] = par.特效样式.currentText()
            A0_config.config['SD版本'] = par.SD版本.currentText()
            A0_config.config['图片风格'] = par.图片风格.currentText()
            A0_config.config['当前选中动画模型'] = par.动画模式.currentText()
            A0_config.config['关键帧样式'] = par.关键帧样式.currentText()
            A0_config.config['辅助图片'] = par.辅助图片.isChecked()
            A0_config.config['口型对齐'] = par.口型对齐.isChecked()
            if par.推理增强.isChecked():
                self.推理增强模式 = True
            else:  # inserted
                self.推理增强模式 = False
            A0_config.config['感情'] = par.感情
            A0_config.config['音量'] = str(par.音量.value())
            A0_config.config['音调'] = str(par.音调.value())
            A0_config.config['语速'] = str(par.语速.value())
            A0_config.config['感情强弱'] = str(par.感情强弱.value() / 100)
            A0_config.config['Lora强度'] = str(par.Lora强度.text() or 0.6)
            A0_config.config['风格强度'] = str(par.风格强度.text() or 0.6)
            if float(A0_config.config['Lora强度']) > 2 or float(A0_config.config['Lora强度']) < 0:
                A0_config.config['Lora强度'] = 0.6
            if float(A0_config.config['风格强度']) > 2 or float(A0_config.config['风格强度']) < 0:
                A0_config.config['风格强度'] = 0.6
            A0_config.config['每张图片数量'] = str(par.每张图片数量.value())
            A0_config.config['图片采样方法'] = par.图片采样方法.currentText()
            A0_config.config['调度器'] = par.调度器.currentText()
            A0_config.config['图片采样步数'] = str(par.图片采样步数.value())
            A0_config.config['提示词相关性'] = str(par.提示词相关性.value() / 10)
            A0_config.config['选中底模'] = par.选择底模.currentText()
            A0_config.config['TTS底模'] = par.选择底模.currentText()
            A0_config.config['口语化'] = par.口语化.value()
            A0_config.config['选中声音'] = par.选择声音.currentText()
            A0_config.config['分句模式'] = par.分句模式.currentText()
            剪映草稿箱 = par.剪映草稿文件夹.text()
            if 剪映草稿箱.endswith('/'):
                剪映草稿箱 = 剪映草稿箱[:(-1)]
            A0_config.config['剪映草稿文件夹'] = 剪映草稿箱
            A0_config.config['ChatGPT端口'] = par.ChatGPT端口.currentText()
            A0_config.config['ChatGPT版本'] = par.ChatGPT版本.currentText()
            A0_config.config['三方平台'] = par.三方平台.currentText()

            # 保存翻译设置（使用平台管理器）
            if hasattr(par, '翻译平台选择'):
                A0_config.config['translation_platform'] = par.翻译平台选择.currentText()
            if hasattr(par, '翻译模型选择'):
                A0_config.config['translation_model'] = par.翻译模型选择.currentText()

            # 平台配置现在由平台管理器统一管理，不再在这里单独保存
            # 保留特殊平台的配置处理
            platform_name = A0_config.config['ChatGPT端口']

            if '本地' in platform_name:
                # 本地推理仍需要单独配置
                if hasattr(par, 'GPT_Token'):
                    A0_config.config['other_key'] = par.GPT_Token.text().replace(' ', '').replace('\n', '')
                if hasattr(par, '接口地址'):
                    A0_config.config['other接口地址'] = str(par.接口地址.text())
                    if A0_config.config['other接口地址'].endswith('/'):
                        A0_config.config['other接口地址'] = A0_config.config['other接口地址'][:(-1)]
            A0_config.config['推理模型'] = self.任务模式
            A0_config.config['图片高清修复'] = par.高清修复.isChecked()
            A0_config.config['面部修复'] = par.面部修复.isChecked()
            A0_config.config['高清修复算法'] = par.高清修复算法.currentText()
            A0_config.config['放大模式'] = par.放大模式.currentText()
            A0_config.config['极速绘图'] = par.极速绘图.isChecked()
            A0_config.config['海外模式'] = par.海外模式.isChecked()
            A0_config.config['推理增强'] = par.推理增强.isChecked()
            A0_config.config['万相加速'] = par.万相加速.isChecked()
            # 保存推理模板选择
            if hasattr(par, '推理模板选择'):
                A0_config.config['current_template'] = par.推理模板选择.currentText()
            A0_config.config['修复重绘幅度'] = str(par.修复重绘幅度.value() / 100)
            A0_config.config['修复放大倍数'] = str(par.修复放大倍数.value())
            # 已删除的配置项：微软密钥、区域标识、MJ请求参数、MJ频道密钥
            # 这些配置项对应的组件已被删除，不再保存
            if hasattr(par, '微软密钥'):
                A0_config.config['微软密钥'] = str(par.微软密钥.text())
            if hasattr(par, '区域标识'):
                A0_config.config['区域标识'] = str(par.区域标识.text())
            if hasattr(par, 'MJ请求参数'):
                A0_config.config['MJ请求参数'] = str(par.MJ请求参数.text())
            if hasattr(par, 'MJ频道密钥'):
                A0_config.config['MJ频道密钥'] = str(par.MJ频道密钥.text())

            A0_config.config['语音平台'] = str(par.语音平台.currentText())
            A0_config.config['夜间模式'] = A0_config.夜间模式
            A0_config.config['单批数量'] = int(self.视频转绘窗口.单批数量.text())
            if not par.云端地址.text():
                if self.SD版本.currentText() == 'WebUI':
                    A0_config.config['云端地址'] = 'http://127.0.0.1:7860'
                else:  # inserted
                    A0_config.config['云端地址'] = 'http://127.0.0.1:8188'
                A0_config.config['key_xgy'] = ''
            else:  # inserted
                url = par.云端地址.text().split('?')[0]
                if url.endswith('/'):
                    url = url[:(-1)]
                if 'http' in url:
                    A0_config.config['云端地址'] = url
                    A0_config.config['key_xgy'] = ''
                else:  # inserted
                    A0_config.config['key_xgy'] = url
            if not par.CV链接.text():
                par.CV链接.setText('http://127.0.0.1:9233')
            A0_config.config['CV链接'] = par.CV链接.text()
            if A0_config.config['CV链接'].endswith('/'):
                A0_config.config['CV链接'] = A0_config.config['CV链接'][:(-1)]
            A0_config.config['选中CV参考音'] = par.选择底模.currentText()
            A0_config.config['SDurl数量'] = len(A0_config.config['云端地址'].replace('，', ',').replace(' ', '').split(','))
            A0_config.config['镜像数量'] = min(int(par.镜像数量.text() or 1), 10)
            A0_config.config['强制销毁'] = par.强制销毁.isChecked()
            if A0_config.NOVIP:
                A0_config.config['转场样式'] = '视频不加转场'
                A0_config.config['特效样式'] = '视频不加特效'
                A0_config.config['海外模式'] = False
                # A0_config.config['推理增强'] = False  # 允许使用推理增强
                A0_config.config['辅助图片'] = False
                A0_config.config['口型对齐'] = False
                # self.推理增强模式 = False  # 允许使用推理增强
            A0_config.config['字体大小'] = str(par.文字大小.value())
            self.更换图片风格()
            A0_config.修改配置()
        except Exception as e:
            print(e)
            self.提示(e)

    def 更换图片风格(self):
        if 'Flux' in A0_config.config['SD版本']:
            self.通用正面词 = A16_style.fluxstype.get(A0_config.config['图片风格']) if A0_config.config['正提示词'] == '' else A0_config.config['正提示词']
            self.通用反面词 = ''
        else:  # inserted
            if 'SD' in A0_config.config['SD版本']:
                if A0_config.config['正提示词'] == '':
                    self.通用正面词 = A16_style.xlstype.get(A0_config.config['图片风格'])
                else:  # inserted
                    self.通用正面词 = A0_config.config['正提示词']
            else:  # inserted
                if 'kolors' in A0_config.config['当前选中模型'].lower():
                    self.通用正面词 = A16_style.kolorsstype.get(A0_config.config['图片风格']) if A0_config.config['正提示词'] == '' else A0_config.config['正提示词']
                    if A0_config.config['反提示词'] == '':
                        self.通用反面词 = '(worst quality:2), (low quality:2), (normal quality:2), lowres, ((monochrome)), ((grayscale)),'
                    else:  # inserted
                        self.通用反面词 = A0_config.config['反提示词']
                else:  # inserted
                    if 'flux' in A0_config.config['当前选中模型'].lower():
                        self.通用正面词 = A0_config.config['正提示词'] if A0_config.config['正提示词']!= '' else A16_style.fluxstype.get(A0_config.config['图片风格'])
                        self.通用反面词 = ''
                    else:  # inserted
                        if 'MJ' in A0_config.config['SD版本']:
                            self.通用正面词 = ''
                            self.通用反面词 = ''
                        else:  # inserted
                            for data in A16_style.diva:
                                if data['name'] == A0_config.config['图片风格']:
                                    if A0_config.config['正提示词'] == '':
                                        self.通用正面词 = data['prompt']
                                    else:  # inserted
                                        self.通用正面词 = A0_config.config['正提示词']
                                    if A0_config.config['反提示词'] == '':
                                        self.通用反面词 = data['negative_prompt']
                                        return
                                    self.通用反面词 = A0_config.config['反提示词']

    def start_task(self):
        print(self.当前任务)
        if A0_config.NOVIP and self.端口 == 'yun':
            self.提示('您的会员已过期, 请联系管理购买卡密后在软件左侧\"会员充值\"')
        else:  # inserted
            if not self.stop_run:
                self.提示('请等待当前任务结束后继续')
            else:  # inserted
                if self.running:
                    print('有任务运行中')
                    self.提示('有任务运行中')
                else:  # inserted
                    self.running = True
                    self.zhuxian = True
                    self.stop_run = False
                    if self.当前任务 == '一键成片任务':
                        self.任务总数 = 2 * len(self.项目配置['data'])
                        if self.任务模式 == '原创模式' and (not jduvudu312us_usjlq.path.exists(A0_config.改文件名(self.项目配置['音频路径']))):
                            self.任务总数 = 3 * len(self.项目配置['data'])
                        else:  # inserted
                            if self.任务模式 == '克隆模式':
                                合成语音 = False
                                for row, row_data in enumerate(self.项目配置['data']):
                                    if row_data['txt']!= row_data['txtxiugai']:
                                        合成语音 = True
                                if 合成语音:
                                    self.任务总数 = 3 * len(self.项目配置['data'])
                    else:  # inserted
                        if self.当前任务 == '批量计划任务':
                            self.任务总数 = 3
                        else:  # inserted
                            if '镜头分割任务' in self.当前任务:
                                self.任务总数 = 5
                            else:  # inserted
                                try:
                                    self.任务总数 = len(self.项目配置['data'])
                                except:
                                    self.任务总数 = 1
                    self.当前进度 = 0
                    if self.任务总数 > 0:
                        self.worker.start()
                    else:  # inserted
                        print('当前选择可运行任务为 0 ')
                        self.running = False
                        self.stop_run = True
                    if self.任务总数 > 1:
                                self.进度条.setFormat(f'{self.当前任务}: 0% (0/{self.任务总数})')
        self.进度条.show()
        self.进度条.setValue(0)
        # 重置进度变量
        self.当前进度 = 0

    def call_backlog(self, 返回数据):
        try:
            修改内容 = 返回数据[0]
            row_data = 返回数据[1]
            任务 = 返回数据[2]
            if 任务 == '任务完成':
                # 确保进度条显示100%
                self.当前进度 = self.任务总数
                self.更新进度条()
                # 播放完成提示音
                self.播放完成提示音()
                # 延迟隐藏进度条，让用户看到100%完成状态
                QTimer.singleShot(2000, self.进度条.hide)
                if self.当前任务 == '加载项目任务' and A0_config.config['海外模式'] and (not A0_config.NOVIP):
                    self.海外模式点击事件()
            else:  # inserted
                if self.当前任务 == '批量计划任务':
                    if 返回数据[2] == '批量计划任务':
                        row = 返回数据[0]
                        处理状态 = 返回数据[1]
                        item = QTableWidgetItem(处理状态)
                        self.计划列表.setItem(row, 9, item)
                    self.更新进度条()
                else:  # inserted
                    if 任务 == '更新进度':
                        self.更新进度条()
                    else:  # inserted
                        if 任务 == '视频分镜':
                            print('加载列表')
                            self.run_code()
                            self.重绘列表.setCurrentText(self.当前项目)
                            self.加载选中列表()
                            self.table_widget.verticalHeader().setVisible(False)
                            self.setWindowState(Qt.WindowMaximized)
                        else:  # inserted
                            if 任务 == '关键词':
                                if self.任务模式 == '原创模式':
                                    self.修改第二个按钮文字(row_data['当前行'], '推理完成', 1)
                                self.修改单元格文字(row_data['当前行'], self.正面词行, 修改内容)
                                self.更新进度条()
                            else:  # inserted
                                if 任务 == '视频关键词':
                                    if '正在' in 修改内容 or '失败' in 修改内容:
                                        self.修改第二个按钮文字(row_data['当前行'], 修改内容, 1)
                                    else:  # inserted
                                        self.修改第二个按钮文字(row_data['当前行'], '推理完成', 1)
                                        self.修改单元格文字(row_data['当前行'], self.动图提示词行, 修改内容)
                                    self.更新进度条()
                                else:  # inserted
                                    if 任务 == '图片':
                                        图片路径 = row_data['图片路径']
                                        if self.任务模式 == '视频转绘':
                                            self.修改第二个按钮文字(row_data['当前行'], 修改内容, 0)
                                        else:  # inserted
                                            self.修改第二个按钮文字(row_data['当前行'], 修改内容, 2)
                                        if 图片路径:
                                            self.更新表格图片(row_data['当前行'], 图片路径)
                                            self.更新表格备选图片(row_data['当前行'])
                                        self.更新进度条()
                                    else:  # inserted
                                        if 任务 == '语音':
                                            self.修改第二个按钮文字(row_data['当前行'], 修改内容, 3)
                                            self.更新进度条()
                                        else:  # inserted
                                            if 任务 == '视频':
                                                if self.任务模式 == '视频转绘':
                                                    self.修改第二个按钮文字(row_data['当前行'], 修改内容, 1)
                                                    if row_data['当前行'] < len(self.项目配置['data']) - 1:
                                                        if not self.项目配置['data'][row_data['当前行'] + 1]['img']['path']:
                                                            print('抽取尾帧')
                                                            self.img_weizhen(row_data['当前行'] + 1, self.图片行)
                                                else:  # inserted
                                                    self.修改第二个按钮文字(row_data['当前行'], 修改内容, 4)
                                                self.更新进度条()
                                            else:  # inserted
                                                if 任务 == '更新图片':
                                                    图片路径 = row_data['图片路径']
                                                    if not 图片路径 == '':
                                                        self.更新表格图片(row_data['当前行'], 图片路径)
                                                else:  # inserted
                                                    if 任务 == '放大图片':
                                                        图片路径 = row_data['图片路径']
                                                        self.修改第二个按钮文字(row_data['当前行'], 修改内容, 2)
                                                        if not 图片路径 == '':
                                                            self.更新表格图片(row_data['当前行'], 图片路径)
                                                        self.更新进度条()
                                                    else:  # inserted
                                                        if 任务 == '文案改写':
                                                            if 修改内容 == '正在改写':
                                                                self.修改第二个按钮文字(row_data['当前行'], '正在改写', 1)
                                                            else:  # inserted
                                                                改写后内容 = row_data['改写内容']
                                                                self.修改单元格文字(row_data['当前行'], self.修改文案行, 改写后内容)
                                                                self.修改第二个按钮文字(row_data['当前行'], '改写完成', 1)
                                                                self.更新进度条()
                                                        else:  # inserted
                                                            if 任务 == '翻译文案':
                                                                翻译后内容 = row_data['中文内容']
                                                                self.修改单元格文字(row_data['当前行'], self.内容行, 翻译后内容)
                                                                self.更新进度条()
                                                            else:  # inserted
                                                                if 任务 == '合成视频':
                                                                    self.更新进度条()
                                                                else:  # inserted
                                                                    if 任务 == '镜头分割任务':
                                                                        self.加载选中列表()
                                                                        self.run_code()
                                                                    else:  # inserted
                                                                        if 任务 == '加载项目':
                                                                            if self.任务模式 == '克隆模式' or self.任务模式 == '视频转绘':
                                                                                self.zengjiashuju(row_data)
                                                                            else:  # inserted
                                                                                if self.任务模式 == '原创模式':
                                                                                    self.zengjiashuju_yc(row_data)
                                                                            self.更新进度条()
                                                                        else:  # inserted
                                                                            if 任务 == '弹窗':
                                                                                self.弹窗停止(修改内容)
        except Exception as e:
            print('回传处理失败:', e)

    def on_image_label_double_clicked(self, event, image_path):
        try:
            image_path1 = image_path.replace('.png', '.mp4')
            if not jduvudu312us_usjlq.path.exists(image_path1):
                image_path1 = image_path
            print(image_path1)
            jduvudu312us_usjlq.startfile(image_path1)
        except Exception as e:
            print(e)

    def 播放完成提示音(self):
        """播放任务完成提示音"""
        try:
            import winsound
            import os
            # 尝试播放完成提示音
            finish_wav = 'finish.wav'
            if os.path.exists(finish_wav):
                winsound.PlaySound(finish_wav, winsound.SND_FILENAME | winsound.SND_ASYNC)
            else:
                # 如果没有找到finish.wav，播放系统默认声音
                winsound.PlaySound("SystemAsterisk", winsound.SND_ALIAS | winsound.SND_ASYNC)
            print("播放任务完成提示音")
        except Exception as e:
            print(f"播放提示音失败: {e}")

    def 更新进度条(self):
        if self.任务总数 > 0:
            jindu = int(self.当前进度 / self.任务总数 * 100)
            jindu = min(100, max(0, jindu))  # 确保进度在0-100之间
            self.进度条.setValue(jindu)
            # 更新进度条显示文本，包含任务名称和进度
            if hasattr(self, '当前任务') and self.当前任务:
                self.进度条.setFormat(f'{self.当前任务}: {jindu}% ({self.当前进度}/{self.任务总数})')
            else:
                self.进度条.setFormat(f'{jindu}% ({self.当前进度}/{self.任务总数})')
        else:
            self.进度条.setValue(0)
            self.进度条.setFormat('0%')

    def huitu_backlog(self, 返回数据):
        from pyz.任务运行文件.A18_comfyui import Comfyui, 保存图片
        try:
            # 检查返回数据的长度，避免IndexError
            if len(返回数据) < 2:
                print(f"警告：返回数据长度不足 {返回数据}")
                return

            修改内容 = 返回数据[0]

            # 如果只有两个元素，可能是简单的状态更新
            if len(返回数据) == 2:
                row_or_message = 返回数据[1]
                # 根据修改内容的类型判断如何处理
                if isinstance(row_or_message, int):
                    # 可能是行号，进行简单的状态更新
                    print(f"简单状态更新：{修改内容} (行号: {row_or_message})")
                else:
                    # 可能是错误消息或其他信息
                    print(f"状态信息：{修改内容} - {row_or_message}")
                return

            # 如果有3个或更多元素，按原逻辑处理
            row_data = 返回数据[1]
            任务 = 返回数据[2]
        except Exception as e:
            print(f"huitu_backlog数据解析错误: {e}")
            print(f"返回数据: {返回数据}")
            return
        if 任务 == '关键词':
            if self.任务模式 == '原创模式':
                self.修改第二个按钮文字(row_data['当前行'], '推理完成', 1)
            self.修改单元格文字(row_data['当前行'], self.正面词行, 修改内容)
        elif 任务 == '图片':
            图片路径 = row_data['图片路径']
            当前行 = row_data['当前行']
            print(f"[调试] 图片生成完成，准备更新界面 - 行号: {当前行}, 图片路径: {图片路径}")

            if self.任务模式 == '视频转绘':
                self.修改第二个按钮文字(当前行, 修改内容, 0)
            else:
                self.修改第二个按钮文字(当前行, 修改内容, 2)

            # 统一调用界面更新处理函数
            self.处理绘图完成(当前行, 图片路径, False)  # 普通绘图，主图片未更新
        elif 任务 == '绘图完成':
            # 处理Pollinations等后台线程的绘图完成信号
            图片路径 = row_data.get('图片路径')
            当前行 = row_data.get('当前行')
            已更新主图 = row_data.get('已更新主图', False)
            # print(f"[调试] 收到绘图完成信号 - 行号: {当前行}, 图片路径: {图片路径}, 已更新主图: {已更新主图}")

            # 在主线程中安全地更新界面
            if 图片路径 and 当前行 is not None:
                # print(f"[调试] 在主线程中处理绘图完成...")
                self.处理绘图完成(当前行, 图片路径, 已更新主图)
            else:
                # print(f"[调试] 绘图完成信号数据不完整，跳过处理")
                pass
        elif 任务 == '重新绘图':
            图片路径 = row_data['图片路径']
            当前行 = row_data['当前行']
            print(f"[调试] 重新绘图完成，准备更新界面 - 行号: {当前行}, 图片路径: {图片路径}")

            if self.任务模式 == '视频转绘':
                self.修改第二个按钮文字(当前行, 修改内容, 0)
            else:
                self.修改第二个按钮文字(当前行, 修改内容, 2)

            if 图片路径:
                print(f"[调试] 开始更新第{当前行+1}行的主图和备选图片（重新绘图）")
                # 重新绘图更新主图片和备选图片区域
                self.更新表格图片(当前行, 图片路径)
                self.更新表格备选图片(当前行)
                print(f"[调试] 第{当前行+1}行界面更新完成（重新绘图）")
            else:
                print(f"[调试] 重新绘图图片路径为空，跳过界面更新")
        elif 任务 == '视频':
            if self.任务模式 == '视频转绘':
                self.修改第二个按钮文字(row_data['当前行'], 修改内容, 1)
            else:
                self.修改第二个按钮文字(row_data['当前行'], 修改内容, 4)
        elif 任务 == '语音':
            self.修改第二个按钮文字(row_data['当前行'], 修改内容, 3)
        elif 任务 == '改写':
            self.修改单元格文字(row_data['当前行'], self.修改文案行, 修改内容)
            self.修改第二个按钮文字(row_data['当前行'], '改写完成', 1)
        elif 任务 == '视频关键词':
            if '正在' in 修改内容 or '失败' in 修改内容:
                self.修改第二个按钮文字(row_data['当前行'], 修改内容, 1)
            else:
                self.修改第二个按钮文字(row_data['当前行'], '推理完成', 1)
                self.修改单元格文字(row_data['当前行'], self.动图提示词行, 修改内容)
        elif 任务 == '弹窗':
            if row_data == '云端部署':
                self.云端部署.setText(修改内容)
                QTimer.singleShot(0, lambda: self.提示(f'{修改内容}'))
            elif row_data == '云端销毁':
                self.销毁镜像.setText(修改内容)
                QTimer.singleShot(0, lambda: self.提示(f'{修改内容}'))
            else:
                self.弹窗停止(修改内容)
        elif 任务 == 'UI更新':
            # 处理浏览器绘图B的UI状态更新信号
            try:
                if isinstance(row_data, dict) and row_data.get('action') == 'update_button_status':
                    row = row_data.get('row')
                    status = row_data.get('status')
                    details = row_data.get('details', '')

                    print(f"[UI更新] 收到UI状态更新信号 - 第{row + 1}行: {status} - {details}")

                    # 直接更新浏览器绘图B按钮状态
                    self.更新浏览器绘图B按钮状态(row, status, details)
                else:
                    print(f"[UI更新] 未知的UI更新数据格式: {row_data}")
            except Exception as e:
                print(f"[UI更新] 处理UI状态更新信号失败: {e}")
                import traceback
                traceback.print_exc()
        elif 任务 == '参考图':
            # 处理参考图任务完成信号
            if len(row_data) == 4:
                # 新格式: (角色名, file_path, 参考图组件, 按钮组件)
                tab_name, image_path, 参考图, button1 = row_data
            else:
                # 兼容旧格式或其他格式
                tab_name = row_data[0] if len(row_data) > 0 else '未知角色'
                image_path = row_data[1] if len(row_data) > 1 else ''
                参考图 = None
                button1 = None

            print(f"📸 参考图任务完成: {tab_name} -> {image_path}")

            if self.人物设定 and hasattr(self, 'sub_ui'):
                # 调用人物设定界面的画图返回方法
                self.sub_ui.画图返回(tab_name, image_path, 参考图, button1, 修改内容)
            else:
                print(f"⚠️  人物设定界面未打开，无法更新参考图显示")

    def 更新浏览器绘图B按钮状态(self, row, status, details=""):
        """更新浏览器绘图B按钮的状态"""
        try:
            print(f"[UI更新] 开始更新第{row + 1}行浏览器绘图B按钮状态: {status} - {details}")

            # 检查ui对象和绘图按钮列表
            if not hasattr(self, 'ui') or not hasattr(self.ui, '绘图按钮列表'):
                print(f"[UI更新] ❌ 没有找到绘图按钮列表")
                return

            # 检查按钮索引
            if row >= len(self.ui.绘图按钮列表):
                print(f"[UI更新] ❌ 按钮索引超出范围: {row} >= {len(self.ui.绘图按钮列表)}")
                return

            # 获取按钮对象
            按钮 = self.ui.绘图按钮列表[row]
            print(f"[UI更新] ✅ 获取到第{row + 1}行按钮对象: {type(按钮)}")

            # 根据状态更新按钮
            if status == "正在绘图":
                按钮.setText(f"🎨 正在绘图...")
                按钮.setEnabled(False)
                按钮.setStyleSheet("background-color: #FFA500; color: white;")  # 橙色
                print(f"[UI更新] ✅ 按钮状态设置为正在绘图")
            elif status == "完成":
                按钮.setText(f"✅ 绘图完成")
                按钮.setEnabled(True)
                按钮.setStyleSheet("background-color: #4CAF50; color: white;")  # 绿色
                print(f"[UI更新] ✅ 按钮状态设置为完成")

                # 3秒后恢复默认状态
                from PyQt5.QtCore import QTimer
                QTimer.singleShot(3000, lambda: self._恢复浏览器绘图B按钮默认状态(row))
            elif status == "重试":
                按钮.setText(f"🔄 重试中...")
                按钮.setEnabled(False)
                按钮.setStyleSheet("background-color: #FF9800; color: white;")  # 深橙色
                print(f"[UI更新] ✅ 按钮状态设置为重试")
            elif status == "失败":
                按钮.setText(f"❌ 绘图失败")
                按钮.setEnabled(True)
                按钮.setStyleSheet("background-color: #F44336; color: white;")  # 红色
                print(f"[UI更新] ✅ 按钮状态设置为失败")

                # 3秒后恢复默认状态
                from PyQt5.QtCore import QTimer
                QTimer.singleShot(3000, lambda: self._恢复浏览器绘图B按钮默认状态(row))
            else:
                # 默认状态
                按钮.setText("🎨 浏览器绘图B")
                按钮.setEnabled(True)
                按钮.setStyleSheet("")  # 默认样式
                print(f"[UI更新] ✅ 按钮状态设置为默认")

            # 更新工具提示
            if details:
                按钮.setToolTip(f"第{row + 1}行: {details}")

            print(f"[UI更新] ✅ 第{row + 1}行浏览器绘图B按钮状态更新完成")

        except Exception as e:
            print(f"[UI更新] ❌ 更新浏览器绘图B按钮状态失败: {e}")
            import traceback
            traceback.print_exc()

    def _恢复浏览器绘图B按钮默认状态(self, row):
        """恢复浏览器绘图B按钮到默认状态"""
        try:
            if hasattr(self, 'ui') and hasattr(self.ui, '绘图按钮列表'):
                if row < len(self.ui.绘图按钮列表):
                    按钮 = self.ui.绘图按钮列表[row]

                    # 恢复默认状态
                    按钮.setText("🎨 浏览器绘图B")
                    按钮.setEnabled(True)
                    按钮.setStyleSheet("")  # 默认样式
                    按钮.setToolTip("")  # 清除工具提示

                    print(f"[UI更新] 🔄 第{row + 1}行浏览器绘图B按钮状态已恢复默认")
        except Exception as e:
            print(f"[UI更新] ❌ 恢复浏览器绘图B按钮默认状态失败: {e}")

    def update_table(self):
        try:
            # 根据操作栏按钮数量计算合理的行高
            if self.任务模式 == '原创模式':
                # 原创模式有6个按钮垂直排列，每个按钮25px + 间距
                button_count = 6
                button_height = self.缩放(25)
                spacing = self.缩放(5)  # 按钮间距
                button_required_height = button_count * button_height + (button_count - 1) * spacing + self.缩放(20)  # 额外边距

                # 考虑文本内容的高度需求，确保文本有足够的显示空间
                text_required_height = self.缩放(120)  # 为文本内容预留足够的高度

                # 取两者中的较大值，确保既能显示按钮又能显示文本
                required_height = max(button_required_height, text_required_height)

            else:
                # 其他模式按钮较少
                required_height = self.缩放(150)

            # 设置行高，确保操作按钮和文本内容都完全可见
            self.table_widget.verticalHeader().setDefaultSectionSize(required_height)
            self.table_widget.verticalHeader().setMinimumSectionSize(required_height)

            # 获取表格视口的实际可用宽度（这是表格内容区域的真实宽度）
            viewport_width = self.table_widget.viewport().width()
            # 减去一些边距，确保不会出现水平滚动条
            available_width = viewport_width - 20

            if self.任务模式 == '原创模式':
                # 原创模式的列宽比例分配（总共9列）
                # 编号(3%) + 文案(24%) + 分镜(7%) + 提示词(18%) + 新图(18%) + 视频提示词(10%) + 语音(6%) + 操作(8%) + 备选图片(14%)
                column_ratios = {
                    self.图片编号行: 0.03,      # 编号
                    self.内容行: 0.24,          # 文案 - 重要内容，稍微减少
                    self.分镜行: 0.07,          # 分镜 - 稍微减少
                    self.正面词行: 0.18,        # 提示词 - 稍微减少
                    self.图片行: 0.18,          # 新图 - 增加到18%，更好展示图片
                    self.动图提示词行: 0.10,    # 视频提示词 - 稍微减少
                    self.语音行: 0.06,          # 语音 - 稍微减少
                    self.操作行: 0.08,          # 操作
                    self.备选图片行: 0.14       # 备选图片 - 保持不变
                }

                # 计算各列的实际宽度，确保总和不超过可用宽度
                total_width = 0
                column_widths = {}

                # 先计算所有列的宽度
                for col_index, ratio in column_ratios.items():
                    width = int(available_width * ratio)
                    column_widths[col_index] = width
                    total_width += width

                # 如果总宽度超过可用宽度，按比例缩减
                if total_width > available_width:
                    adjustment_ratio = available_width / total_width
                    adjusted_total = 0
                    for col_index in column_widths:
                        column_widths[col_index] = int(column_widths[col_index] * adjustment_ratio)
                        adjusted_total += column_widths[col_index]

                    # 处理舍入误差，将剩余像素分配给最重要的列（文案列）
                    remaining = available_width - adjusted_total
                    if remaining > 0:
                        column_widths[self.内容行] += remaining

                # 设置列宽
                for col_index, width in column_widths.items():
                    self.table_widget.setColumnWidth(col_index, width)

                # 设置不可编辑的列
                for i in range(self.table_widget.rowCount()):
                    item = self.table_widget.item(i, self.图片行)
                    if item:
                        item.setFlags(item.flags() & ~Qt.ItemIsEditable)
                    item = self.table_widget.item(i, self.原图片行)
                    if item:
                        item.setFlags(item.flags() & ~Qt.ItemIsEditable)
                    item = self.table_widget.item(i, self.图片编号行)
                    if item:
                        item.setFlags(item.flags() & ~Qt.ItemIsEditable)

            else:  # 克隆模式或视频转绘模式
                if self.任务模式 == '克隆模式':
                    # 克隆模式的列宽比例分配（总共9列）
                    # 编号(3%) + 原文(15%) + 改后文案(15%) + 原图(12%) + 提示词(18%) + 新图(12%) + 视频提示词(13%) + 操作(8%) + 备选图片(14%)
                    column_ratios = {
                        self.图片编号行: 0.03,      # 编号
                        self.内容行: 0.15,          # 原文
                        self.修改文案行: 0.15,      # 改后文案
                        self.原图片行: 0.12,        # 原图
                        self.正面词行: 0.18,        # 提示词
                        self.图片行: 0.12,          # 新图
                        self.动图提示词行: 0.13,    # 视频提示词
                        self.操作行: 0.08,          # 操作
                        self.备选图片行: 0.14       # 备选图片
                    }
                else:  # 视频转绘模式
                    # 视频转绘模式的列宽比例分配（总共7列）
                    # 编号(3%) + 原图(18%) + 提示词(22%) + 新图(20%) + 视频提示词(17%) + 操作(8%) + 备选图片(14%)
                    column_ratios = {
                        self.图片编号行: 0.03,      # 编号
                        self.原图片行: 0.18,        # 原图 - 增加到18%
                        self.正面词行: 0.22,        # 提示词 - 稍微减少
                        self.图片行: 0.20,          # 新图 - 增加到20%，更好展示图片
                        self.动图提示词行: 0.17,    # 视频提示词 - 稍微减少
                        self.操作行: 0.08,          # 操作
                        self.备选图片行: 0.14       # 备选图片
                    }

                # 计算各列的实际宽度，确保总和不超过可用宽度
                total_width = 0
                column_widths = {}

                # 先计算所有列的宽度
                for col_index, ratio in column_ratios.items():
                    width = int(available_width * ratio)
                    column_widths[col_index] = width
                    total_width += width

                # 如果总宽度超过可用宽度，按比例缩减
                if total_width > available_width:
                    adjustment_ratio = available_width / total_width
                    adjusted_total = 0
                    for col_index in column_widths:
                        column_widths[col_index] = int(column_widths[col_index] * adjustment_ratio)
                        adjusted_total += column_widths[col_index]

                    # 处理舍入误差，将剩余像素分配给最重要的列
                    remaining = available_width - adjusted_total
                    if remaining > 0:
                        if self.任务模式 == '克隆模式':
                            column_widths[self.内容行] += remaining  # 原文列
                        else:
                            column_widths[self.正面词行] += remaining  # 提示词列

                # 设置列宽
                for col_index, width in column_widths.items():
                    self.table_widget.setColumnWidth(col_index, width)

                # 设置不可编辑的列
                for i in range(self.table_widget.rowCount()):
                    item = self.table_widget.item(i, self.图片行)
                    if item:
                        item.setFlags(item.flags() & ~Qt.ItemIsEditable)
                    item = self.table_widget.item(i, self.图片编号行)
                    if item:
                        item.setFlags(item.flags() & ~Qt.ItemIsEditable)

            self.table_widget.verticalHeader().setVisible(True)
            self.table_widget.setEditTriggers(QAbstractItemView.NoEditTriggers)
            self.table_widget.itemDoubleClicked.connect(self.on_cell_double_clicked)
            self.table_widget.verticalHeader().setVisible(False)

            # print(f"[调试] 表格控件宽度: {self.table_widget.width()}, 视口宽度: {viewport_width}, 可用宽度: {available_width}")
            # if self.任务模式 == '原创模式':
            #     print(f"[调试] 各列宽度分配:")
            #     final_total = 0
            #     for col_index, width in column_widths.items():
            #         ratio = column_ratios[col_index]
            #         print(f"  列{col_index}: {ratio*100:.0f}% = {width}px")
            #         final_total += width
            #     print(f"[调试] 总宽度: {final_total}px / {available_width}px")

            # 表格列宽变化后，更新所有备选图片的大小
            if hasattr(self, '项目配置') and self.项目配置 and 'data' in self.项目配置:
                # 创建一个定时器来检查表格是否已完全渲染
                def check_and_update():
                    备选图片列宽度 = self.table_widget.columnWidth(self.备选图片行)
                    if 备选图片列宽度 > 0:
                        # print(f"[调试] 表格已完全渲染，备选图片列宽度：{备选图片列宽度}")
                        # 更新所有行的备选图片
                        for row in range(len(self.项目配置['data'])):
                            self.更新表格备选图片(row, force_width=备选图片列宽度)
                    else:
                        # 如果列宽度还是0，继续等待
                        QTimer.singleShot(100, check_and_update)

                # 启动首次检查
                QTimer.singleShot(100, check_and_update)

        except Exception as e:
            print('调整数据出错', e)

    def 点击单选框(self, 单选框, row):
        镜头 = 单选框.text()
        print(镜头)
        self.项目配置['data'][row]['jingtou'] = 镜头
        self.保存配置()

    def 点击分镜多选框(self, checked, 单选框, row):
        分镜 = 单选框.text()
        已有分镜 = []
        if self.项目配置['data'][row]['fenjing']:
            已有分镜 = self.项目配置['data'][row]['fenjing'].split(',')
        if checked:
            已有分镜.append(分镜)
            self.项目配置['data'][row]['fenjing'] = ','.join(已有分镜)
        else:  # inserted
            修改后分镜 = []
            for j in self.项目配置['data'][row]['fenjing'].split(','):
                if j!= 分镜:
                    修改后分镜.append(j)
            self.项目配置['data'][row]['fenjing'] = ','.join(修改后分镜)
        self.保存配置()

    def 点击语音单选框(self, 单选框, row):
        语音 = 单选框.text()
        self.项目配置['data'][row]['fj_yuyin'] = 语音
        self.保存配置()

    def 点击下拉框(self, event, 文本框, search_model, row, 下拉框):
        点击的文本 = search_model.itemFromIndex(event).text().split(':')[0]
        输入框的文本 = 文本框.toPlainText()
        输入框的文本 = ','.join(输入框的文本.split(',')[:len(输入框的文本.split(',')) - 1])
        if tags.get(点击的文本):
            if 输入框的文本:
                最后的文本 = 输入框的文本 + ', ' + tags.get(点击的文本) + ', '
            else:  # inserted
                最后的文本 = tags.get(点击的文本) + ', '
            文本框.setPlainText(最后的文本)
            search_model.clear()
            self.项目配置['data'][row]['miaoshu'] = 最后的文本
            self.项目配置['data'][row]['prompt'] = 最后的文本
            self.保存配置()
            下拉框.hide()
            文本框.setFocus(Qt.OtherFocusReason)
            cursor = 文本框.textCursor()
            cursor.movePosition(cursor.End)
            文本框.setTextCursor(cursor)
            pass

    def 文字输入监控(self, 文字框, 下拉框, search_model):
        text = 文字框.toPlainText()
        text = re.split('[,，.。 ]', text)[(-1)].replace(' ', '')
        if text!= '':
            filtered_data = {key: value for key, value in tags.items() if value not in 文字框.toPlainText()}
            matches = [f'{key}: {filtered_data[key]}' for key in filtered_data.keys() if text in key]
            search_model.clear()
            search_results = matches if text else []
            if search_results:
                下拉框.show()
                for result in search_results:
                    item = QStandardItem(result)
                    search_model.appendRow(item)
            else:  # inserted
                下拉框.hide()
        else:  # inserted
            下拉框.hide()

    def 视频文本框失焦(self, event, 视频文本框, row):
        Custom_TextEdit.focusOutEvent(视频文本框, event)
        new_data = 视频文本框.toPlainText()
        if new_data.replace(' ', '').replace(',', '').replace('，', '').lower()!= self.项目配置['data'][row]['prompt_video'].replace(' ', '').replace(',', '').replace('，', '').lower():
            self.项目配置['data'][row]['prompt_video'] = new_data
            self.保存配置()
        super().focusOutEvent(event)

    def 文本框失焦(self, event, 文本框, row, 下拉框, search_model):
        Custom_TextEdit.focusOutEvent(文本框, event)
        new_data = 文本框.toPlainText()
        # 🛡️ 修复：使用prompt_cn字段进行比较，不再使用miaoshu字段
        old_data = self.项目配置['data'][row].get('prompt_cn', '')
        if new_data.replace(' ', '').replace(',', '').replace('，', '').lower() != old_data.replace(' ', '').replace(',', '').replace('，', '').lower():
            if 下拉框.isHidden():
                # 🛡️ 修复：只更新prompt_cn字段用于UI显示（中文）
                self.项目配置['data'][row]['prompt_cn'] = new_data  # 用户输入保存到UI显示字段

                # 检查是否包含中文
                import re
                if not re.search(r'[\u4e00-\u9fff]', new_data):
                    # 如果是英文内容，也更新prompt字段（用于生图）
                    self.项目配置['data'][row]['prompt'] = new_data
                    print(f"[修复] 行{row}: 英文内容，同时更新prompt（生图）和prompt_cn（UI显示）字段")
                else:
                    # 如果是中文内容，不更新prompt字段，保持其英文内容用于生图
                    print(f"[修复] 行{row}: 中文内容，只更新prompt_cn（UI显示）字段，保持prompt（生图）字段不变")

                self.保存配置()
        super().focusOutEvent(event)

    def fenjing(self, fenjing, row):
        分镜行_widget = QWidget(self)
        表格布局 = QGridLayout(分镜行_widget)
        row_fj = 0
        col_fj = 0
        单行人物 = 1

        # 只显示当前行实际使用的分镜角色
        if fenjing and fenjing.strip():
            # 获取当前行实际使用的分镜列表
            fenjing_list = [f.strip() for f in fenjing.split(',') if f.strip()]
            # 过滤出存在于项目配置中的分镜角色
            valid_fenjing = [f for f in fenjing_list if f in self.项目配置.get('fenjing', {})]
        else:
            # 如果没有分镜设置，不显示任何按钮
            valid_fenjing = []

        # 根据实际显示的按钮数量调整布局
        button_count = len(valid_fenjing)
        self.table_widget.setColumnWidth(self.分镜行, self.缩放(100))
        if button_count > 9:
            单行人物 = 3
            if self.任务模式 == '原创模式':
                self.table_widget.setColumnWidth(self.分镜行, self.缩放(200))
        elif button_count > 5:
            单行人物 = 2
            if self.任务模式 == '原创模式':
                self.table_widget.setColumnWidth(self.分镜行, self.缩放(150))

        # 只为实际使用的分镜角色创建按钮
        for key in valid_fenjing:
            if key in self.项目配置.get('fenjing', {}):
                button = QPushButton(key)
                button.setFixedHeight(self.缩放(25))
                button.setFixedWidth(self.缩放(60))
                button.setCheckable(True)
                # 所有显示的按钮都应该是选中状态（因为它们都在fenjing列表中）
                button.setChecked(True)
                button.clicked.connect(lambda checked, button_f=button: self.点击分镜多选框(checked, button_f, row))
                表格布局.addWidget(button, row_fj, col_fj)
                col_fj += 1
                if col_fj == 单行人物:
                    col_fj = 0
                    row_fj += 1

        self.table_widget.setCellWidget(row, self.分镜行, 分镜行_widget)

    def fenjing_yy(self, row):
        语音行_widget = QWidget(self)
        垂直布局 = QGridLayout(语音行_widget)
        row_fj = 0
        col_fj = 0
        单行人物 = 1
        self.table_widget.setColumnWidth(self.语音行, self.缩放(100))
        if len(self.项目配置.get('fj_yuyin').items()) > 5:
            单行人物 = 2
            if self.任务模式 == '原创模式':
                self.table_widget.setColumnWidth(self.语音行, self.缩放(150))
        if not self.项目配置.get('fj_yuyin'):
            self.项目配置['fj_yuyin'] = {'旁白': {'声音': '', '模型': ''}}
        语音 = self.项目配置['data'][row].get('fj_yuyin')
        if not 语音 or 语音 not in self.项目配置['fj_yuyin'].keys():
            语音 = '旁白'
            self.项目配置['data'][row]['fj_yuyin'] = 语音
        for i, (k, v) in enumerate(self.项目配置.get('fj_yuyin').items()):
            Button_yuyin = QRadioButton(k)
            垂直布局.addWidget(Button_yuyin, row_fj, col_fj)
            if 语音 == k:
                Button_yuyin.setChecked(True)
            Button_yuyin.clicked.connect(lambda event, index=i, button=Button_yuyin: self.点击语音单选框(button, row))
            col_fj += 1
            if col_fj == 单行人物:
                col_fj = 0
                row_fj += 1
        self.table_widget.setCellWidget(row, self.语音行, 语音行_widget)

    def zengjiashuju_yc(self, data):
        index, txt, fenjing, prompt, _, img_path, 备选图片数组, 新图尺寸, prompt_video = data

        self.增加 = True
        try:
            # 确保表格有正确的列数
            if self.table_widget.columnCount() == 0:
                # print(f"[调试] 表格列数为0，重新设置列数")
                if self.任务模式 == '原创模式':
                    headers = ['编号', '文案', '分镜', '提示词', '新图', '视频提示词', '语音', '操作', '备选图片']
                    self.图片编号行 = 0
                    self.内容行 = 1
                    self.分镜行 = 2
                    self.正面词行 = 3
                    self.图片行 = 4
                    self.动图提示词行 = 5
                    self.语音行 = 6
                    self.操作行 = 7
                    self.备选图片行 = 8
                    self.table_widget.setColumnCount(len(headers))
                    self.table_widget.setHorizontalHeaderLabels(headers)
                    # print(f"[调试] 重新设置表格为{len(headers)}列")

            row = index
            self.table_widget.insertRow(row)
            图片编号行 = QTableWidgetItem(str(int(index + 1)))
            图片编号行.setTextAlignment(Qt.AlignCenter)
            self.table_widget.setItem(row, self.图片编号行, 图片编号行)
            # 使用自定义文本组件显示内容，支持自动换行
            内容行_widget = QWidget(self)
            内容行_layout = QVBoxLayout(内容行_widget)
            内容行_layout.setContentsMargins(0, 0, 0, 0)
            内容文本框 = CustomTextEdit_content(self, row, self)

            内容文本框.setText(txt)
            内容行_layout.addWidget(内容文本框)
            self.table_widget.setCellWidget(row, self.内容行, 内容行_widget)
            self.fenjing(fenjing, row)
            self.fenjing_yy(row)
            正面词行_widget = QWidget(self)
            search_model = QStandardItemModel()
            文本框 = CustomTextEdit(self, self)
            # 优先显示中文prompt，如果没有则显示英文prompt
            显示内容 = self.项目配置['data'][row].get('prompt_cn', prompt)
            文本框.setText(显示内容)
            下拉框 = QListView(self)
            下拉框.setViewMode(QListView.ListMode)
            下拉框.setMovement(QListView.Static)
            下拉框.setVerticalScrollMode(QListView.ScrollPerPixel)
            下拉框.setModel(search_model)
            水平布局 = QHBoxLayout()
            水平布局.addWidget(文本框)
            水平布局.addWidget(下拉框)
            下拉框.hide()
            垂直布局 = QVBoxLayout(正面词行_widget)
            垂直布局.setContentsMargins(0, 0, 0, 0)
            近景 = QRadioButton('近景')
            中景 = QRadioButton('中景')
            远景 = QRadioButton('远景')
            景深布局 = QHBoxLayout()
            景深布局.addWidget(近景)
            景深布局.addWidget(中景)
            景深布局.addWidget(远景)
            垂直布局.addLayout(水平布局)
            垂直布局.addLayout(景深布局)
            镜头 = self.项目配置['data'][row].get('jingtou')
            if 镜头:
                for 按钮 in [近景, 中景, 远景]:
                    if 镜头 == 按钮.text():
                        按钮.setChecked(True)
                        break
            else:
                中景.setChecked(True)
            近景.clicked.connect(lambda event: self.点击单选框(近景, row))
            中景.clicked.connect(lambda event: self.点击单选框(中景, row))
            远景.clicked.connect(lambda event: self.点击单选框(远景, row))
            self.table_widget.setCellWidget(row, self.正面词行, 正面词行_widget)
            下拉框.clicked.connect(lambda event: self.点击下拉框(event, 文本框, search_model, row, 下拉框))
            文本框.textChanged.connect(lambda: self.文字输入监控(文本框, 下拉框, search_model))
            文本框.focusOutEvent = lambda event: self.文本框失焦(event, 文本框, row, 下拉框, search_model)
            视频提示词_w = QWidget(self)
            垂直布局_v = QVBoxLayout(视频提示词_w)
            垂直布局_v.setContentsMargins(0, 0, 0, 0)
            视频文本框 = CustomTextEdit_shipin(self, row, self)
            视频文本框.focusOutEvent = lambda event: self.视频文本框失焦(event, 视频文本框, row)
            视频文本框.setText(prompt_video)
            垂直布局_v.addWidget(视频文本框)
            self.table_widget.setCellWidget(row, self.动图提示词行, 视频提示词_w)
            图片行_item = QTableWidgetItem()
            if img_path:
                try:
                    pixmap = QPixmap(img_path)
                    if not pixmap.isNull():
                        width = int(pixmap.width() * self.缩放(180) / pixmap.height())
                        scaled_pixmap = pixmap.scaledToWidth(width)
                        图片行_item.setIcon(QIcon(scaled_pixmap))
                        图片行_item.setTextAlignment(Qt.AlignCenter)
                        图片行_item.setData(Qt.UserRole, img_path)
                except Exception as e:
                    print(f"Error loading image for table item: {e}")
            self.table_widget.setItem(row, self.图片行, 图片行_item)
            if 新图尺寸:
                try:
                    w, h = map(int, 新图尺寸.split('x'))
                    self.table_widget.setIconSize(QtCore.QSize(w, h))
                except (ValueError, AttributeError):
                    pass
            cell_widget = QWidget(self)
            操作组件 = QGridLayout(cell_widget)
            if self.任务模式 == '原创模式':
                推理改写 = QPushButton('推关键词')
                推理改写.clicked.connect(lambda: self.reasoning(row))
            else:
                推理改写 = QPushButton('改写内容')
                推理改写.clicked.connect(lambda: self.gaixie(row))
            重新绘图 = QPushButton('重新绘图')
            重新绘图.clicked.connect(lambda: self.Redraw(row))
            制作视频 = QPushButton('制作动画')
            制作视频.clicked.connect(lambda: self.make_video(row))
            语音合成 = QPushButton('语音试听')
            语音合成.clicked.connect(lambda: self.yuyin(row))
            向上合并 = QPushButton('向上合并')
            向上合并.clicked.connect(lambda: self.hebing('向上', row))
            向下合并 = QPushButton('分镜设置')
            向下合并.setStyleSheet(f'background-color: {A0_config.警告色}')
            向下合并.clicked.connect(lambda: self.镜头设置(row))
            按钮 = [推理改写, 重新绘图, 制作视频, 语音合成, 向上合并, 向下合并]
            for a in 按钮:
                a.setFixedSize(self.缩放(100), self.缩放(25))
            操作组件.addWidget(向上合并, 0, 0)
            操作组件.addWidget(推理改写, 1, 0)
            操作组件.addWidget(重新绘图, 2, 0)
            操作组件.addWidget(语音合成, 3, 0)
            操作组件.addWidget(制作视频, 4, 0)
            操作组件.addWidget(向下合并, 5, 0)
            self.table_widget.setCellWidget(row, self.操作行, cell_widget)
            # 使用统一的备选图片更新函数，确保固定大小显示
            if 备选图片数组:
                # 使用我们的专用函数来创建备选图片显示，确保60x60固定大小
                self.更新表格备选图片(row)
            else:
                备选图片行 = QTableWidgetItem('')
                self.table_widget.setItem(row, self.备选图片行, 备选图片行)
        except Exception as e:
            import traceback
            print(f'增加表格数据时发生错误: {e}')
            print(traceback.format_exc())

        # 数据添加完成后，更新表格布局
        if row == 0:  # 第一行数据添加时更新表格布局
            QTimer.singleShot(100, self.update_table)  # 延迟更新，确保表格已渲染

        self.增加 = False

    def zengjiashuju(self, data):
        index, txt, txtxiugai, 原图片行, fenjing, prompt, 图片行, img_path, 备选图片数组, 新图尺寸, 原图尺寸, prompt_video = data
        self.增加 = True
        try:
            row = index
            self.table_widget.insertRow(row)
            图片编号行 = QTableWidgetItem(str(int(index + 1)))
            图片编号行.setTextAlignment(Qt.AlignCenter)
            self.table_widget.setItem(row, self.图片编号行, 图片编号行)
            if self.任务模式 == '克隆模式':
                # 使用自定义文本组件显示内容，支持自动换行
                内容行_widget = QWidget(self)
                内容行_layout = QVBoxLayout(内容行_widget)
                内容行_layout.setContentsMargins(0, 0, 0, 0)
                内容文本框 = CustomTextEdit_content(self, row, self)
                内容文本框.setText(txt)
                内容行_layout.addWidget(内容文本框)
                self.table_widget.setCellWidget(row, self.内容行, 内容行_widget)
                内容修改行 = QTableWidgetItem(txtxiugai)
                内容修改行.setTextAlignment(Qt.AlignCenter)
                self.table_widget.setItem(row, self.修改文案行, 内容修改行)
                分镜行 = QTableWidgetItem(fenjing)
                分镜行.setTextAlignment(Qt.AlignCenter)
                self.table_widget.setItem(row, self.分镜行, 分镜行)
            正面词行 = QTableWidgetItem(prompt)
            正面词行.setTextAlignment(Qt.AlignCenter)
            self.table_widget.setItem(row, self.正面词行, 正面词行)
            动图提示词行 = QTableWidgetItem(prompt_video)
            动图提示词行.setTextAlignment(Qt.AlignCenter)
            self.table_widget.setItem(row, self.动图提示词行, 动图提示词行)
            self.table_widget.setItem(row, self.原图片行, 原图片行)
            if 原图尺寸:
                self.table_widget.setIconSize(原图尺寸)
            self.table_widget.setItem(row, self.图片行, 图片行)
            if 新图尺寸:
                self.table_widget.setIconSize(新图尺寸)
            cell_widget = QWidget(self)
            操作组件 = QGridLayout(cell_widget)
            重新绘图 = QPushButton('重新绘图')
            重新绘图.clicked.connect(lambda: self.Redraw(row))
            制作视频 = QPushButton('制作动画')
            制作视频.clicked.connect(lambda: self.make_video(row))
            向下合并 = QPushButton('分镜设置')
            向下合并.setStyleSheet(f'background-color: {A0_config.警告色}')
            向下合并.clicked.connect(lambda: self.镜头设置(row))
            按钮 = [重新绘图, 制作视频, 向下合并]
            for a in 按钮:
                a.setFixedSize(self.缩放(100), self.缩放(25))
            if self.任务模式 == '克隆模式':
                推理改写 = QPushButton('改写内容')
                推理改写.clicked.connect(lambda: self.gaixie(row))
                语音合成 = QPushButton('语音试听')
                语音合成.clicked.connect(lambda: self.yuyin(row))
                向上合并 = QPushButton('向上合并')
                向上合并.clicked.connect(lambda: self.hebing('向上', row))
                按钮 = [推理改写, 语音合成, 向上合并]
                for a in 按钮:
                    a.setFixedSize(self.缩放(100), self.缩放(25))
            if self.任务模式 == '克隆模式':
                操作组件.addWidget(向上合并, 0, 0)
                操作组件.addWidget(推理改写, 1, 0)
                操作组件.addWidget(重新绘图, 2, 0)
                操作组件.addWidget(语音合成, 3, 0)
                操作组件.addWidget(制作视频, 4, 0)
                操作组件.addWidget(向下合并, 5, 0)
            else:  # inserted
                操作组件.addWidget(重新绘图, 0, 0)
                操作组件.addWidget(制作视频, 1, 0)
                操作组件.addWidget(向下合并, 2, 0)
            self.table_widget.setCellWidget(row, self.操作行, cell_widget)
            # 使用统一的备选图片更新函数，确保固定大小显示
            if 备选图片数组:
                # 使用我们的专用函数来创建备选图片显示，确保60x60固定大小
                self.更新表格备选图片(row)
            else:  # inserted
                备选图片行 = QTableWidgetItem('')
                self.table_widget.setItem(row, self.备选图片行, 备选图片行)
        except Exception as e:
            print(f'增加表格数据: {e}')
        self.增加 = False

    def updateButtonData(self):
        for row, data in enumerate(self.项目配置.get('data')):
            widget = self.table_widget.cellWidget(row, self.操作行)
            向上合并 = widget.layout().itemAt(0).widget()
            向上合并.clicked.disconnect()
            向上合并.clicked.connect(lambda _, row=row: self.hebing('向上', row))
            推理按钮 = widget.layout().itemAt(1).widget()
            推理按钮.clicked.disconnect()
            if self.任务模式 == '原创模式':
                推理按钮.clicked.connect(lambda _, row=row: self.reasoning(row))
            else:  # inserted
                推理按钮.clicked.connect(lambda _, row=row: self.gaixie(row))
            绘图按钮 = widget.layout().itemAt(2).widget()
            绘图按钮.clicked.disconnect()
            绘图按钮.clicked.connect(lambda _, row=row: self.Redraw(row))
            视频按钮 = widget.layout().itemAt(4).widget()
            视频按钮.clicked.disconnect()
            视频按钮.clicked.connect(lambda _, row=row: self.make_video(row))
            语音按钮 = widget.layout().itemAt(3).widget()
            语音按钮.clicked.disconnect()
            语音按钮.clicked.connect(lambda _, row=row: self.yuyin(row))
            向下合并 = widget.layout().itemAt(5).widget()
            向下合并.clicked.disconnect()
            向下合并.clicked.connect(lambda: self.镜头设置(row))
            if self.任务模式 == '原创模式':
                self.fenjing(data['fenjing'], row)
                self.fenjing_yy(row)
                正面词布局 = self.table_widget.cellWidget(row, self.正面词行)
                layout = 正面词布局.layout()
                for j in range(layout.count()):
                    item = layout.itemAt(j)
                    if isinstance(item, QLayout):
                        layout_item = item.layout()
                        所有组件 = []
                        for i in range(layout_item.count()):
                            if layout_item.itemAt(i).widget():
                                所有组件.append(layout_item.itemAt(i).widget())
                        for 组件 in 所有组件:
                            if isinstance(组件, CustomTextEdit):
                                if layout_item.indexOf(组件) == 0:
                                    文本框 = 组件
                            if isinstance(组件, QListView) and layout_item.indexOf(组件) == 1:
                                下拉框 = 组件
                            if isinstance(组件, QRadioButton):
                                if layout_item.indexOf(组件) == 0:
                                    近景 = 组件
                                else:  # inserted
                                    if layout_item.indexOf(组件) == 1:
                                        中景 = 组件
                                    else:  # inserted
                                        if layout_item.indexOf(组件) == 2:
                                            远景 = 组件
                try:
                    近景.clicked.disconnect()
                    近景.clicked.connect(lambda _, row=row: self.点击单选框(近景, row))
                    中景.clicked.disconnect()
                    中景.clicked.connect(lambda _, row=row: self.点击单选框(中景, row))
                    远景.clicked.disconnect()
                    远景.clicked.connect(lambda _, row=row: self.点击单选框(远景, row))
                    search_model = QStandardItemModel()
                    self.set_text_changed_and_focus_out_events(row, 文本框, 下拉框, search_model)
                except Exception as e:
                    print('组件合并错误, 可重新加载恢复', e)
                cell_widget = self.table_widget.cellWidget(row, self.动图提示词行)
                layout = cell_widget.layout()
                for j in range(layout.count()):
                    item = layout.itemAt(j).widget()
                    if isinstance(item, CustomTextEdit_shipin):
                        def focus_out_event(event, item=item, row=row):
                            self.视频文本框失焦(event, item, row)
                            item.setRow(row)
                        try:
                            del item.focusOutEvent
                        except AttributeError:
                            pass
                        item.focusOutEvent = focus_out_event
            self.table_widget.item(row, self.图片编号行).setText(str(row + 1))

    def set_text_changed_and_focus_out_events(self, row, 文本框, 下拉框, search_model):
        self.textbox_to_row[文本框] = row
        self.textbox_to_row[下拉框] = row

        def text_changed():
            self.文字输入监控(文本框, 下拉框, search_model)
        文本框.textChanged.disconnect()
        文本框.textChanged.connect(text_changed)

        def viw_changed(event):
            self.点击下拉框(event, 文本框, search_model, self.textbox_to_row[下拉框], 下拉框)
        if hasattr(下拉框, 'model'):
            下拉框.setModel(None)
        下拉框.setModel(search_model)
        下拉框.clicked.disconnect()
        下拉框.clicked.connect(viw_changed)

        def focus_out_event(event):
            self.文本框失焦(event, 文本框, self.textbox_to_row[文本框], 下拉框, search_model)
        try:
            del 文本框.focusOutEvent
        except AttributeError:
            pass
        文本框.focusOutEvent = focus_out_event

    def hebing(self, 方向, row):
        try:
            if 方向 == '向下':
                if row == len(self.项目配置['data']) - 1:
                    return
                修改后原文 = f"{self.项目配置['data'][row]['txt']},{self.项目配置['data'][row + 1]['txt']}"
                修改后改文 = f"{self.项目配置['data'][row]['txtxiugai']},{self.项目配置['data'][row + 1]['txtxiugai']}"
                if jduvudu312us_usjlq.path.exists(A0_config.改文件名(self.项目配置['data'][row]['yuyin'])):
                    jduvudu312us_usjlq.remove(A0_config.改文件名(self.项目配置['data'][row]['yuyin']))
                if jduvudu312us_usjlq.path.exists(A0_config.改文件名(self.项目配置['data'][row + 1]['yuyin'])):
                    jduvudu312us_usjlq.remove(A0_config.改文件名(self.项目配置['data'][row + 1]['yuyin']))
                新图路径 = A0_config.改文件名(self.项目配置['data'][row + 1]['img']['path'])
                if 新图路径!= '':
                    if jduvudu312us_usjlq.path.exists(新图路径):
                        jduvudu312us_usjlq.remove(新图路径)
                        for data in self.项目配置['data'][row + 1]['img_beixuan']:
                            if jduvudu312us_usjlq.path.exists(A0_config.改文件名(data['path'])):
                                print('删除:', A0_config.改文件名(data['path']))
                                jduvudu312us_usjlq.remove(A0_config.改文件名(data['path']))
                原图路径 = A0_config.改文件名(self.项目配置['data'][row + 1]['img_yuantu'])
                if jduvudu312us_usjlq.path.exists(原图路径):
                    jduvudu312us_usjlq.remove(原图路径)
                # 更新内容行的自定义文本组件
                内容行_widget = self.table_widget.cellWidget(row, self.内容行)
                if 内容行_widget:
                    内容文本框 = 内容行_widget.layout().itemAt(0).widget()
                    if isinstance(内容文本框, CustomTextEdit_content):
                        内容文本框.setText(修改后原文)
                if self.任务模式 == '克隆模式':
                    self.table_widget.item(row, self.修改文案行).setText(修改后改文)
                self.table_widget.removeRow(row + 1)
                self.table_widget.viewport().update()
                self.项目配置['data'][row]['txt'] = 修改后原文
                self.项目配置['data'][row]['txt_en'] = 修改后原文
                self.项目配置['data'][row]['txtxiugai'] = 修改后改文
                self.项目配置['data'][row]['end_time'] = self.项目配置['data'][row + 1]['end_time']
                self.项目配置['data'].pop(row + 1)
                self.保存配置()
            else:  # inserted
                if row == 0:
                    return
                修改后原文 = f"{self.项目配置['data'][row - 1]['txt']},{self.项目配置['data'][row]['txt']}"
                修改后改文 = f"{self.项目配置['data'][row - 1]['txtxiugai']},{self.项目配置['data'][row]['txtxiugai']}"
                if jduvudu312us_usjlq.path.exists(A0_config.改文件名(self.项目配置['data'][row]['yuyin'])):
                    jduvudu312us_usjlq.remove(A0_config.改文件名(self.项目配置['data'][row]['yuyin']))
                if jduvudu312us_usjlq.path.exists(A0_config.改文件名(self.项目配置['data'][row - 1]['yuyin'])):
                    jduvudu312us_usjlq.remove(A0_config.改文件名(self.项目配置['data'][row - 1]['yuyin']))
                新图路径 = A0_config.改文件名(self.项目配置['data'][row - 1]['img']['path'])
                if 新图路径!= '' and jduvudu312us_usjlq.path.exists(新图路径):
                    jduvudu312us_usjlq.remove(新图路径)
                    for data in self.项目配置['data'][row - 1]['img_beixuan']:
                        if jduvudu312us_usjlq.path.exists(A0_config.改文件名(data['path'])):
                            jduvudu312us_usjlq.remove(A0_config.改文件名(data['path']))
                原图路径 = A0_config.改文件名(self.项目配置['data'][row - 1]['img_yuantu'])
                if jduvudu312us_usjlq.path.exists(原图路径):
                    jduvudu312us_usjlq.remove(原图路径)
                # 更新内容行的自定义文本组件
                内容行_widget = self.table_widget.cellWidget(row, self.内容行)
                if 内容行_widget:
                    内容文本框 = 内容行_widget.layout().itemAt(0).widget()
                    if isinstance(内容文本框, CustomTextEdit_content):
                        内容文本框.setText(修改后原文)
                if self.任务模式 == '克隆模式':
                    self.table_widget.item(row, self.修改文案行).setText(修改后改文)
                self.table_widget.removeRow(row - 1)
                self.table_widget.viewport().update()
                self.项目配置['data'][row]['txt'] = 修改后原文
                self.项目配置['data'][row]['txt_en'] = 修改后原文
                self.项目配置['data'][row]['txtxiugai'] = 修改后改文
                self.项目配置['data'][row]['start_time'] = self.项目配置['data'][row - 1]['start_time']
                self.项目配置['data'].pop(row - 1)
                self.保存配置()
            self.updateButtonData()
        except Exception as e:
            print('合并错误', e)
            self.提示(f'合并错误: {e}')

    def 加载历史任务(self):
        try:
            self.当前项目 = self.重绘列表.currentText()
            if self.当前项目 == '':
                self.提示('请先新建任务后继续')
            else:  # inserted
                if self.任务模式 == '原创模式':
                    self.当前项目文件夹 = jduvudu312us_usjlq.path.join(self.项目文件夹, '原创项目', self.当前项目)
                else:  # inserted
                    if self.任务模式 == '克隆模式':
                        self.当前项目文件夹 = jduvudu312us_usjlq.path.join(self.项目文件夹, '克隆项目', self.当前项目)
                    else:  # inserted
                        self.当前项目文件夹 = jduvudu312us_usjlq.path.join(self.项目文件夹, '转绘项目', self.当前项目)
                self.图片文件夹 = jduvudu312us_usjlq.path.join(self.当前项目文件夹, '图片文件')
                self.视频文件夹 = jduvudu312us_usjlq.path.join(self.当前项目文件夹, '视频文件')
                self.原始图片文件夹 = jduvudu312us_usjlq.path.join(self.当前项目文件夹, '原始图片')
                self.音频文件夹 = jduvudu312us_usjlq.path.join(self.当前项目文件夹, '音频文件')
                jduvudu312us_usjlq.makedirs(self.当前项目文件夹, exist_ok=True)
                jduvudu312us_usjlq.makedirs(self.图片文件夹, exist_ok=True)
                jduvudu312us_usjlq.makedirs(self.视频文件夹, exist_ok=True)
                jduvudu312us_usjlq.makedirs(self.原始图片文件夹, exist_ok=True)
                jduvudu312us_usjlq.makedirs(self.音频文件夹, exist_ok=True)
                self.项目配置文件 = jduvudu312us_usjlq.path.join(self.当前项目文件夹, f'{self.当前项目}.json')

                # 使用路径管理器加载配置，自动迁移绝对路径
                try:
                    from pyz.任务运行文件.path_manager import get_path_manager
                    path_manager = get_path_manager()

                    # 先尝试迁移现有配置
                    path_manager.migrate_existing_config(self.项目配置文件)

                    # 加载配置并转换为绝对路径供程序使用
                    self.项目配置 = path_manager.load_config_with_absolute_paths(self.项目配置文件)
                except Exception as e:
                    print(f"路径管理器加载失败，使用原始方法: {e}")
                    # 回退到原始加载方法
                    with open(self.项目配置文件, 'r', encoding='utf-8') as file:
                        self.项目配置 = json.load(file)
                if self.任务模式 == '原创模式' or self.任务模式 == '克隆模式' or self.任务模式 == '视频转绘':
                    if isinstance(self.项目配置['fenjing'], list):
                        self.项目配置['fenjing'] = A0_config.分镜数组改字典(self.项目配置['fenjing'])
                        self.保存配置()
                    # 修复备选图片数据
                    self.修复所有备选图片数据()
                    self.加载选中列表()
                    # 播放任务完成提示音
                    self.播放完成提示音()
                    print(f"加载项目任务完成: {self.当前项目}")
        except Exception as e:
            error_message = f'加载任务时遇到一个错误，但这不会影响程序运行。\n\n技术细节: {e}'
            print(f"捕获到加载错误: {error_message}")
            self.弹窗提示(error_message)

    def 删除历史任务(self):
        reply = QMessageBox.question(self, '确认', '确定删除选中项目, 删除后不可恢复', QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        if reply == QMessageBox.Yes:
            if self.任务模式 == '原创模式':
                self.重置表格()
                原项目文件夹 = jduvudu312us_usjlq.path.join(self.项目文件夹, '原创项目', self.重绘列表.currentText())
            else:  # inserted
                if self.任务模式 == '克隆模式':
                    self.重置表格()
                    原项目文件夹 = jduvudu312us_usjlq.path.join(self.项目文件夹, '克隆项目', self.重绘列表.currentText())
                else:  # inserted
                    if self.任务模式 == '视频转绘':
                        原项目文件夹 = jduvudu312us_usjlq.path.join(self.项目文件夹, '转绘项目', self.重绘列表.currentText())
                    else:  # inserted
                        if self.任务模式 == 'AI虚拟人':
                            原项目文件夹 = jduvudu312us_usjlq.path.join(self.项目文件夹, '虚拟人项目', self.重绘列表.currentText())
            try:
                shutil.rmtree(原项目文件夹)
            except FileNotFoundError:
                print(f'{原项目文件夹} 文件夹不存在')
            except OSError as e:
                print(f'删除文件失败, 可能被占用或缺少权限, 请手动删除 {原项目文件夹}: {e.strerror}')
            except Exception as e:
                print('删除文件失败: ', e)
            finally:  # inserted
                self.run_code()
                if self.任务模式 == '原创模式':
                    self.当前项目文件夹 = jduvudu312us_usjlq.path.join(self.项目文件夹, '原创项目', self.重绘列表.currentText())
                else:  # inserted
                    if self.任务模式 == '克隆模式':
                        self.当前项目文件夹 = jduvudu312us_usjlq.path.join(self.项目文件夹, '克隆项目', self.重绘列表.currentText())
                    else:  # inserted
                        if self.任务模式 == '视频转绘':
                            self.当前项目文件夹 = jduvudu312us_usjlq.path.join(self.项目文件夹, '转绘项目', self.重绘列表.currentText())
                            # 播放任务完成提示音
                            self.播放完成提示音()
                            print("删除项目任务完成")
                            return
                        if self.任务模式 == 'AI虚拟人':
                            self.当前项目文件夹 = jduvudu312us_usjlq.path.join(self.项目文件夹, '虚拟人项目', self.重绘列表.currentText())
                # 播放任务完成提示音
                self.播放完成提示音()
                print("删除项目任务完成")

    def 复制当前任务(self):
        if A0_config.NOVIP:
            A0_config.升级提示('复制任务')
            return
        reply = QMessageBox.question(self, '确认', '确定复制选中项目', QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        if reply == QMessageBox.Yes:
            if self.任务模式 == '原创模式':
                原项目文件夹 = jduvudu312us_usjlq.path.join(self.项目文件夹, '原创项目', self.重绘列表.currentText())
                原文件夹 = jduvudu312us_usjlq.path.join(self.项目文件夹, '原创项目')
            else:  # inserted
                原项目文件夹 = jduvudu312us_usjlq.path.join(self.项目文件夹, '克隆项目', self.重绘列表.currentText())
                原文件夹 = jduvudu312us_usjlq.path.join(self.项目文件夹, '克隆项目')
            原项目配置文件 = jduvudu312us_usjlq.path.join(原项目文件夹, f'{self.重绘列表.currentText()}.json')
            with open(原项目配置文件, 'r', encoding='utf-8') as file:
                原项目配置 = json.load(file)
            原视频 = A0_config.改文件名(原项目配置['视频路径'])
            原音频 = A0_config.改文件名(原项目配置['音频路径'])
            原文案 = A0_config.改文件名(原项目配置['文案路径'])
            原字幕 = A0_config.改文件名(原项目配置['字幕路径'])
            folder_names = [f for f in jduvudu312us_usjlq.listdir(原文件夹) if jduvudu312us_usjlq.path.isdir(jduvudu312us_usjlq.path.join(原文件夹, f))]
            文件夹 = self.重绘列表.currentText()
            if f'{文件夹}(1)' in folder_names:
                count = 2
                while f'{文件夹}({count})' in folder_names:
                    count += 1
                new_folder_name = f'{文件夹}({count})'
                print(f'已创建新文件夹: {new_folder_name}')
            else:  # inserted
                new_folder_name = f'{文件夹}(1)'
                print(f'已创建新文件夹: {new_folder_name}')
            try:
                self.当前项目 = new_folder_name
                if self.任务模式 == '原创模式':
                    self.当前项目文件夹 = jduvudu312us_usjlq.path.join(self.项目文件夹, '原创项目', self.当前项目)
                else:  # inserted
                    self.当前项目文件夹 = jduvudu312us_usjlq.path.join(self.项目文件夹, '克隆项目', self.当前项目)
                self.图片文件夹 = jduvudu312us_usjlq.path.join(self.当前项目文件夹, '图片文件')
                self.原始图片文件夹 = jduvudu312us_usjlq.path.join(self.当前项目文件夹, '原始图片')
                self.音频文件夹 = jduvudu312us_usjlq.path.join(self.当前项目文件夹, '音频文件')
                self.视频文件夹 = jduvudu312us_usjlq.path.join(self.当前项目文件夹, '视频文件')
                jduvudu312us_usjlq.makedirs(self.当前项目文件夹, exist_ok=True)
                jduvudu312us_usjlq.makedirs(self.图片文件夹, exist_ok=True)
                jduvudu312us_usjlq.makedirs(self.视频文件夹, exist_ok=True)
                jduvudu312us_usjlq.makedirs(self.原始图片文件夹, exist_ok=True)
                jduvudu312us_usjlq.makedirs(self.音频文件夹, exist_ok=True)
                jduvudu312us_usjlq.makedirs(self.视频文件夹, exist_ok=True)
                datas = []
                for index, data in enumerate(原项目配置['data']):
                    原图 = A0_config.改文件名(data['img_yuantu'].replace(原项目文件夹, self.当前项目文件夹))
                    data['img'] = {'path': '', 'seep': ''}
                    data['video'] = jduvudu312us_usjlq.path.join(self.视频文件夹, f'{index}.mp4')
                    data['video_beixuan'] = []
                    data['img_beixuan'] = []
                    data['img_yuantu'] = A0_config.改文件名(data['img_yuantu'].replace(原项目文件夹, self.当前项目文件夹))
                    data['yuyin'] = A0_config.改文件名(data['yuyin'].replace(原项目文件夹, self.当前项目文件夹))
                    data['video'] = A0_config.改文件名(data['video'].replace(原项目文件夹, self.当前项目文件夹))
                    data['img_cn'] = A0_config.改文件名(data['img_cn'].replace(原项目文件夹, self.当前项目文件夹))
                    data['img_end'] = A0_config.改文件名(data['img_end'].replace(原项目文件夹, self.当前项目文件夹))
                    data['video_cn'] = A0_config.改文件名(data['video_cn'].replace(原项目文件夹, self.当前项目文件夹))
                    datas.append(data)
                    if jduvudu312us_usjlq.path.exists(原图):
                        shutil.copy(原图, A0_config.改文件名(data['img_yuantu']))
                print(A0_config.改文件名(原项目配置['视频路径']), 原视频.split('.')[len(原视频.split('.')) - 1], '{}\\{}'.format(self.当前项目文件夹, self.当前项目))
                print('{}\\{}.{}'.format(self.当前项目文件夹, self.当前项目, 原视频.split('.')[len(原视频.split('.')) - 1]))
                self.项目配置 = {'视频路径': '{}\\{}.{}'.format(self.当前项目文件夹, self.当前项目, 原视频.split('.')[len(原视频.split('.')) - 1]), '音频路径': '{}\\{}.{}'.format(self.当前项目文件夹, self.当前项目, 原音频.split('.')[len(原视频.split('.')) - 1]), '文案路径': '{}\\{}.{}'.format(self.当前项目文件夹, self.当前项目, 原字幕.split('.')[len(原视频.split('.')) - 1]), '字幕路径': '{}\\{}.{}'.format(self.当前项目文件夹, self.当前项目, 原文案.split('.')[len(原视频.split('.')) - 1]), 'fenjing': {}, 'fj_yuyin': 原项目配置['fj_yuyin'], 'data': datas}
                复制分镜名称 = self.重绘列表.currentText()
                for k, v in 原项目配置['fenjing'].items():
                    self.项目配置['fenjing'][k] = v
                    识别词 = v.get('称呼')
                    原图片 = A0_config.改文件名(v.get('img')) if v.get('img') else ''
                    if 原图片 and jduvudu312us_usjlq.path.exists(原图片):
                        目标图片 = 原图片.replace(复制分镜名称, self.当前项目)
                        shutil.copy(原图片, 目标图片)
                        self.项目配置['fenjing'][k]['img'] = 目标图片
                self.项目配置文件 = jduvudu312us_usjlq.path.join(self.当前项目文件夹, f'{self.当前项目}.json')
                self.保存配置()
                if jduvudu312us_usjlq.path.exists(原视频):
                    shutil.copy(原视频, A0_config.改文件名(self.项目配置['视频路径']))
                if jduvudu312us_usjlq.path.exists(原音频):
                    shutil.copy(原音频, A0_config.改文件名(self.项目配置['音频路径']))
                if jduvudu312us_usjlq.path.exists(原文案):
                    shutil.copy(原文案, A0_config.改文件名(self.项目配置['文案路径']))
                if jduvudu312us_usjlq.path.exists(原字幕):
                    shutil.copy(原字幕, A0_config.改文件名(self.项目配置['字幕路径']))
                self.run_code()
                self.重绘列表.setCurrentText(self.当前项目)
                self.加载选中列表()
                # 播放任务完成提示音
                self.播放完成提示音()
                print(f"复制项目任务完成: {self.当前项目}")
            except Exception as e:
                print('复制任务错误{}'.format(e))
                self.提示('复制任务错误{}'.format(e))

    def 重置表格(self):
        for row in range(self.table_widget.rowCount()):
            for col in range(self.table_widget.columnCount()):
                widget = self.table_widget.cellWidget(row, col)
                if widget is not None:
                    widget.setParent(None)
                item = self.table_widget.item(row, col)
                if item and item.data(Qt.DecorationRole):
                    pixmap = item.data(Qt.DecorationRole)
                    if isinstance(pixmap, QPixmap) and (not pixmap.isNull()):
                        pixmap.deleteLater()
                    item.setData(Qt.DecorationRole, None)
        self.table_widget.setRowCount(0)
        self.table_widget.setColumnCount(0)
        self.table_widget.setHorizontalHeaderLabels([])
        self.table_widget.update()

    def 加载选中列表(self):
        try:
            if self.running:
                print('当前有任务正在运行')
                self.提示(f'{A0_config.name}, 有任务运行中')
            else:  # inserted
                self.重置表格()
                self.table_widget.setRowCount(0)
                if self.任务模式 == '原创模式':
                    headers = ['编号', '文案', '分镜', '提示词', '新图', '视频提示词', '语音', '操作', '备选图片']
                    self.图片编号行 = 0
                    self.内容行 = 1
                    self.分镜行 = 2
                    self.正面词行 = 3
                    self.图片行 = 4
                    self.动图提示词行 = 5
                    self.语音行 = 6
                    self.操作行 = 7
                    self.备选图片行 = 8
                    self.修改文案行 = 9
                    self.原图片行 = 10
                    self.table_widget.setColumnCount(len(headers))
                    self.table_widget.setHorizontalHeaderLabels(headers)
                else:  # inserted
                    if self.任务模式 == '视频转绘':
                        headers = ['编号', '原图', '提示词', '新图', '视频提示词', '操作', '备选图片']
                        self.图片编号行 = 0
                        self.原图片行 = 1
                        self.正面词行 = 2
                        self.图片行 = 3
                        self.动图提示词行 = 4
                        self.操作行 = 5
                        self.备选图片行 = 6
                        self.分镜行 = 7
                        self.语音行 = 8
                        self.内容行 = 9
                        self.修改文案行 = 10
                        self.table_widget.setColumnCount(len(headers))
                        self.table_widget.setHorizontalHeaderLabels(headers)
                    else:  # inserted
                        headers = ['编号', '原文', '改后文案', '原图', '提示词', '新图', '视频提示词', '操作', '备选图片']
                        self.图片编号行 = 0
                        self.内容行 = 1
                        self.修改文案行 = 2
                        self.原图片行 = 3
                        self.正面词行 = 4
                        self.图片行 = 5
                        self.动图提示词行 = 6
                        self.操作行 = 7
                        self.备选图片行 = 8
                        self.分镜行 = 9
                        self.语音行 = 10
                        if self.端口 == 'yun':
                            headers = ['编号', '原文', '改后文案', '原图', '新图', '操作', '备选图片']
                            self.图片编号行 = 0
                            self.内容行 = 1
                            self.修改文案行 = 2
                            self.原图片行 = 3
                            self.图片行 = 4
                            self.操作行 = 5
                            self.备选图片行 = 6
                            self.正面词行 = 7
                            self.分镜行 = 8
                            self.语音行 = 9
                            self.动图提示词行 = 10
                        self.table_widget.setColumnCount(len(headers))
                        self.table_widget.setHorizontalHeaderLabels(headers)
                self.设置表格格式()
                self.update_table()
                self.执行任务('加载项目任务')
                # 加载项目的seed配置
                self.加载项目seed配置()
        except Exception as e:
            提示 = f'加载项目错误: {e}'
            print(提示)
            self.提示(提示)

    def 设置表格格式(self):
        try:
            # 设置合适的字体大小，确保可读性
            table_font_size = 20  # 增加到20px，确保文案内容清晰可读
            header_font_size = 22  # 表头稍大一点

            style_sheet = f"""
                QTableWidget {{
                    background-color: {A0_config.背景色};
                    border: 1px solid {A0_config.边框色};
                    font-size: {table_font_size}px;
                    color: {A0_config.文字色};
                    gridline-color: {A0_config.边框色};
                }}
                QHeaderView::section {{
                    background-color: {A0_config.按钮色};
                    color: {A0_config.文字色};
                    border: 1px solid {A0_config.边框色};
                    font-size: {header_font_size}px;
                    padding: 8px;
                    font-weight: bold;
                }}
                QTableCornerButton::section {{
                    background-color: {A0_config.按钮色};
                    border: 1px solid {A0_config.边框色};
                }}
                QTableWidget::item {{
                    background-color: {A0_config.背景色};
                    color: {A0_config.文字色};
                    border: 1px solid {A0_config.边框色};
                    padding: 8px;
                    font-size: {table_font_size}px;
                }}
                QTableWidget::item:selected {{
                    background-color: {A0_config.选中色};
                    color: {A0_config.文字色};
                }}
            """
            # 设置主表格样式
            self.table_widget.setStyleSheet(style_sheet)
            # print(f"[调试] 已设置主表格样式")

            # 设置计划列表样式
            self.计划列表.setStyleSheet(style_sheet)
            for i in range(self.计划列表.columnCount()):
                self.计划列表.horizontalHeader().setSectionResizeMode(i, QHeaderView.Interactive)
            self.计划列表.horizontalHeader().setStretchLastSection(True)
        except Exception as e:
            print(f"设置表格格式时出错: {e}")

    def 获取图片数组(self, index):
        images = []
        for data in self.项目配置['data'][index]['img_beixuan']:
            # 处理不同的数据格式
            if isinstance(data, dict):
                # 新格式：{'path': '路径', 'seep': 种子}
                if 'path' in data:
                    images.append(A0_config.改文件名(data['path']))
            elif isinstance(data, str):
                # 旧格式：直接是路径字符串
                images.append(A0_config.改文件名(data))
            else:
                print(f"⚠️ 未知的图片数据格式: {type(data)} - {data}")
        return images

    def handle_image_click(self, event, path):
        selected_path = path
        cell_widget = QApplication.widgetAt(event.globalPos())
        row = self.table_widget.indexAt(cell_widget.parent().pos()).row()
        self.更新表格图片(row, selected_path)
        self.项目配置['data'][row]['img']['path'] = selected_path
        for data in self.项目配置['data'][row]['img_beixuan']:
            # 处理不同的数据格式
            if isinstance(data, dict):
                if data.get('path') == selected_path:
                    self.项目配置['data'][row]['img']['seep'] = data.get('seep', 0)
            elif isinstance(data, str):
                if data == selected_path:
                    self.项目配置['data'][row]['img']['seep'] = 0  # 旧格式没有种子信息
        self.保存配置()

    def 更新表格备选图片(self, row, force_width=None):
        """
        更新表格中的备选图片
        :param row: 行号
        :param force_width: 强制使用的列宽度，如果不指定则使用当前列宽度
        """
        # print(f"[调试] ========== 更新表格备选图片开始 ==========")
        # print(f"[调试] 目标行: 第{row+1}行")
        # print(f"[调试] 强制宽度: {force_width}")

        # 首先清理重复的备选图片
        # print(f"[调试] 1. 清理重复备选图片...")
        self.清理重复备选图片(row)

        # 检查并修复备选图片数据
        # print(f"[调试] 2. 修复备选图片数据...")
        self.修复备选图片数据(row)

        # 获取图片数组
        # print(f"[调试] 3. 获取图片数组...")
        image_paths = self.获取图片数组(row)
        # print(f"[调试] 第{row+1}行获取到{len(image_paths)}张备选图片")

        # 显示最后几张图片路径
        if len(image_paths) > 0:
            # print(f"[调试] 最后3张图片路径:")
            # for i, path in enumerate(image_paths[-3:]):
            #     filename = path.split('/')[-1].split('\\\\')[-1]
            #     print(f"[调试]   {len(image_paths)-2+i}: {filename}")
            pass
        else:
            # print(f"[调试] ⚠️  没有获取到任何备选图片")
            pass

        if not image_paths:
            # 如果没有备选图片，显示空白
            empty_item = QTableWidgetItem('')
            self.table_widget.setItem(row, self.备选图片行, empty_item)
            print(f"[调试] 第{row+1}行没有备选图片，显示空白")
            return

        # 动态计算组件高度，根据图片大小调整
        基础高度 = self.缩放(160)
        组件高度 = 基础高度
        备选图片列宽度 = force_width if force_width else self.table_widget.columnWidth(self.备选图片行)
        可用宽度 = 备选图片列宽度 - 20  # 减去滚动条宽度

        # 动态调整每行图片数量和大小，充分利用可用空间
        # 根据可用宽度动态决定每行显示的图片数量
        if 可用宽度 >= 300:
            每行数量 = 2  # 宽度足够时显示2张大图
            图片大小 = max(120, (可用宽度 - 10) // 每行数量)  # 更大的图片
        elif 可用宽度 >= 200:
            每行数量 = 2  # 中等宽度显示2张中等图
            图片大小 = max(80, (可用宽度 - 10) // 每行数量)
        else:
            每行数量 = 3  # 宽度较小时显示3张小图
            图片大小 = max(50, (可用宽度 - 10) // 每行数量)

        # print(f"[调试] 第{row+1}行备选图片列宽度：{备选图片列宽度}，可用宽度：{可用宽度}")
        # print(f"[调试] 每行{每行数量}张，图片大小：{图片大小}x{图片大小}")
        # print(f"[调试] 备选图片路径数量：{len(image_paths)}")

        # 创建一个自定义滚动区域，阻止滚动事件传播
        scroll_area = CustomScrollArea()
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background: transparent;
            }
            QScrollBar:vertical {
                border: none;
                background: rgba(128, 128, 128, 0.1);
                width: 8px;
                margin: 0px;
            }
            QScrollBar::handle:vertical {
                background: rgba(128, 128, 128, 0.5);
                min-height: 20px;
                border-radius: 4px;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                border: none;
                background: none;
            }
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background: none;
            }
        """)

        # 创建一个容器widget来包含所有内容
        container_widget = QWidget()
        container_widget.setStyleSheet("background: transparent;")

        # 使用网格布局
        grid_layout = QGridLayout(container_widget)
        grid_layout.setContentsMargins(2, 2, 2, 2)  # 设置小边距
        grid_layout.setSpacing(2)  # 设置网格间距

        # 计算总行数
        total_images = len(image_paths)
        total_rows = (total_images + 每行数量 - 1) // 每行数量  # 向上取整

        # 添加图片到网格布局
        for i, path in enumerate(image_paths):
            row_idx = i // 每行数量
            col_idx = i % 每行数量

            # 创建图片标签
            label = QLabel()
            label.setFixedSize(图片大小, 图片大小)
            label.setStyleSheet("""
                QLabel {
                    border: 1px solid rgba(128, 128, 128, 0.2);
                    border-radius: 4px;
                    padding: 1px;
                    background: transparent;
                }
                QLabel:hover {
                    border: 1px solid rgba(128, 128, 128, 0.5);
                }
            """)

            # 加载并缩放图片
            pixmap = QPixmap(path)
            if not pixmap.isNull():
                scaled_pixmap = pixmap.scaled(图片大小-2, 图片大小-2, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                label.setPixmap(scaled_pixmap)
                label.setAlignment(Qt.AlignCenter)

                # 添加点击事件和双击事件
                label.mousePressEvent = lambda e, p=path, r=row: self.handle_image_click_fixed(e, p, r)
                label.mouseDoubleClickEvent = lambda e, p=path, r=row: self.handle_image_double_click(e, p, r)
                # 新增：右键菜单
                def contextMenuEvent(event, p=path, r=row):
                    from PyQt5.QtWidgets import QMenu, QAction
                    menu = QMenu()

                    # 获取当前行的fenjing值
                    current_fenjing = self.项目配置['data'][r].get('fenjing', '')
                    current_fenjing_list = [f.strip() for f in current_fenjing.split(',') if f.strip()]

                    # 获取所有唯一fenjing
                    fenjing_list = list(self.项目配置.get('fenjing', {}).keys())
                    fenjing_list = [f for f in fenjing_list if f != '全局']

                    # 添加菜单项，默认选中当前行的fenjing值
                    for fj in fenjing_list:
                        # 检查是否是当前行的fenjing值
                        is_current = fj in current_fenjing_list
                        action_text = f"✓ {fj}" if is_current else fj
                        action = QAction(action_text, self)

                        # 如果是当前行的fenjing，设置为默认样式
                        if is_current:
                            action.setCheckable(True)
                            action.setChecked(True)

                        def set_seed(checked=False, fj=fj, p=p, r=r):
                            # 查找该图片的seed
                            for data in self.项目配置['data'][r]['img_beixuan']:
                                if data['path'] == p:
                                    seed = data.get('seep', data.get('seed', data.get('zhongzi', -1)))
                                    # 如果seed为-1，优先使用全局默认seed值，否则生成随机种子
                                    if seed == -1:
                                        global_default_seed = A0_config.config.get('默认seed值', -1)
                                        if global_default_seed != -1 and A0_config.config.get('seed固定模式', False):
                                            seed = global_default_seed
                                        else:
                                            seed = random.randint(1000000000, 9999999999)
                                        # 更新备选图片的seed值
                                        data['seep'] = seed
                                        if 'seed' in data:
                                            data['seed'] = seed
                                        self.保存配置()

                                    # 更新角色专用种子配置
                                    if '角色专用种子配置' not in self.项目配置:
                                        self.项目配置['角色专用种子配置'] = {}

                                    # 为角色设置种子配置
                                    self.项目配置['角色专用种子配置'][fj] = {
                                        'seed': seed,
                                        'enabled': True,  # 设置种子时自动启用
                                        'last_updated': __import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                                    }



                                    self.保存配置()

                                    seed_source = "全局默认" if A0_config.config.get('seed固定模式', False) and A0_config.config.get('默认seed值', -1) == seed else "图片"
                                    self.提示(f"已设定并启用角色[{fj}]的专用种子为{seed}（来源：{seed_source}），已保存到项目文件")
                                    break
                        action.triggered.connect(set_seed)
                        menu.addAction(action)
                    menu.exec_(event.globalPos())
                label.contextMenuEvent = contextMenuEvent

            grid_layout.addWidget(label, row_idx, col_idx)

        # 设置容器的固定宽度，但高度可以自适应
        container_widget.setFixedWidth(可用宽度)

        # 将容器放入滚动区域
        scroll_area.setWidget(container_widget)
        scroll_area.setFixedHeight(组件高度)  # 固定高度为3行图片的高度

        # 创建一个QWidget作为单元格的内容
        cell_widget = QWidget()
        cell_layout = QVBoxLayout(cell_widget)
        cell_layout.setContentsMargins(0, 0, 0, 0)
        cell_layout.addWidget(scroll_area)

        # 将widget设置到表格单元格
        # print(f"[调试] 4. 设置单元格组件...")
        self.table_widget.setCellWidget(row, self.备选图片行, cell_widget)

        # 验证设置结果
        验证单元格 = self.table_widget.cellWidget(row, self.备选图片行)
        # print(f"[调试] 5. 验证单元格设置: {type(验证单元格).__name__ if 验证单元格 else 'None'}")

        # print(f"[调试] ✅ 第{row+1}行备选图片更新完成：总数{len(image_paths)}张，每行{每行数量}张，图片大小{图片大小}x{图片大小}")
        # print(f"[调试] ========== 更新表格备选图片结束 ==========")

    def handle_image_click_fixed(self, event, path, table_row):
        """修复后的图片点击处理函数"""
        # print(f"[调试] 点击了第{table_row+1}行的图片: {path}")
        # 更新当前行的主图片
        self.更新表格图片(table_row, path)
        # 更新项目配置
        if hasattr(self, '项目配置') and self.项目配置 and 'data' in self.项目配置:
            if table_row < len(self.项目配置['data']):
                self.项目配置['data'][table_row]['img']['path'] = path
                # 更新seed值
                for data in self.项目配置['data'][table_row]['img_beixuan']:
                    if data['path'] == path:
                        self.项目配置['data'][table_row]['img']['seep'] = data['seep']
                        break
                self.保存配置()

    def handle_image_double_click(self, event, path, table_row):
        """备选图片双击处理函数"""
        # print(f"[调试] 双击了第{table_row+1}行的备选图片: {path}")
        # 先更新主图片
        self.handle_image_click_fixed(event, path, table_row)
        # 然后打开预览窗口，直接传入点击的图片路径
        from pyz.UI文件.预览图片窗口 import PreviewImageDialog
        dialog = PreviewImageDialog(image_path=path, row=table_row, column=self.图片行, parent=self)
        dialog.exec_()

    def 更新所有备选图片大小(self):
        """当表格列宽变化时，更新所有行的备选图片大小"""
        try:
            if hasattr(self, '项目配置') and self.项目配置 and 'data' in self.项目配置:
                for row in range(len(self.项目配置['data'])):
                    # 检查该行是否有备选图片
                    image_paths = self.获取图片数组(row)
                    if image_paths:  # 只有有备选图片的行才更新
                        self.更新表格备选图片(row)
                        # print(f"[调试] 已更新第{row+1}行备选图片大小")
        except Exception as e:
            print(f"更新备选图片大小时出错: {e}")

    def 更新表格图片(self, row, image_path):
        # print(f"[调试] 更新表格图片 - 行号: {row}, 路径: {image_path}")
        if jduvudu312us_usjlq.path.exists(image_path):
            图片行 = QTableWidgetItem()
            pixmap = QPixmap(image_path)

            # 🎯 关键修复：确保新图片最大化显示
            # 获取表格列宽和行高
            column_width = self.table_widget.columnWidth(self.图片行)
            row_height = self.table_widget.rowHeight(row)

            print(f"[调试] 更新主图 - 第{row+1}行，表格尺寸: {column_width}x{row_height}")

            if column_width > 50:  # 确保列宽有效
                # 计算合适的显示尺寸，确保图片填满可用空间
                target_width = int(column_width * 0.95)  # 留5%边距
                target_height = int(row_height * 0.95) if row_height > 50 else target_width

                # 按比例缩放，保持宽高比
                scaled_pixmap = pixmap.scaled(target_width, target_height, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                print(f"[调试] 主图按表格尺寸缩放: {target_width}x{target_height} -> {scaled_pixmap.width()}x{scaled_pixmap.height()}")
            else:
                # 回退到固定大小，但增大尺寸
                target_size = self.缩放(300)  # 增大默认尺寸
                scaled_pixmap = pixmap.scaledToWidth(target_size, Qt.SmoothTransformation)
                print(f"[调试] 主图使用固定尺寸: {target_size}px -> {scaled_pixmap.width()}x{scaled_pixmap.height()}")

            图片行.setIcon(QIcon(scaled_pixmap))
            图片行.setTextAlignment(Qt.AlignCenter)
            图片行.setData(Qt.UserRole, image_path)
            self.table_widget.setItem(row, self.图片行, 图片行)

            # 🎯 关键修复：设置表格图标大小，确保图片能正确显示
            self.table_widget.setIconSize(scaled_pixmap.size())
            print(f"[调试] 表格图标大小已设置为: {scaled_pixmap.size().width()}x{scaled_pixmap.size().height()}")

            图片行.setTextAlignment(Qt.AlignCenter)

            # 检查是否是第一次绘图（该行之前没有图片），如果是则刷新界面
            try:
                row_data = self.项目配置['data'][row]
                备选图片数量 = len(row_data.get('img_beixuan', []))
                if 备选图片数量 <= 1:  # 第一次绘图或只有一张图片
                    print(f"[调试] 第{row+1}行第一次绘图，延迟刷新界面以正确显示主图大小")
                    # 延迟刷新，确保图片已经设置完成
                    from PyQt5.QtCore import QTimer
                    QTimer.singleShot(200, lambda: self.刷新主图显示(row))
            except Exception as e:
                print(f"[调试] 检查第一次绘图状态失败: {e}")

            print(f"[调试] 主图更新成功 - 第{row+1}行，最终尺寸: {scaled_pixmap.width()}x{scaled_pixmap.height()}")
        else:
            print(f"[调试] 图片文件不存在: {image_path}")
            pass

    def 刷新主图显示(self, row):
        """刷新主图显示，确保第一次绘图时主图能100%占满显示区域"""
        try:
            # 获取当前行的图片路径
            row_data = self.项目配置['data'][row]
            image_path = row_data.get('img', {}).get('path', '')

            if image_path and jduvudu312us_usjlq.path.exists(image_path):
                print(f"[调试] 刷新第{row+1}行主图显示: {image_path}")

                # 重新设置图片，这次会正确计算大小
                图片行 = QTableWidgetItem()
                pixmap = QPixmap(image_path)

                # 获取表格列宽和行高
                column_width = self.table_widget.columnWidth(self.图片行)
                row_height = self.table_widget.rowHeight(row)

                print(f"[调试] 表格尺寸 - 列宽: {column_width}, 行高: {row_height}")

                if column_width > 50:  # 确保列宽有效
                    # 计算合适的显示尺寸，确保图片填满可用空间
                    target_width = int(column_width * 0.95)  # 留5%边距
                    target_height = int(row_height * 0.95) if row_height > 50 else target_width

                    # 按比例缩放，保持宽高比
                    scaled_pixmap = pixmap.scaled(target_width, target_height, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                    print(f"[调试] 按表格尺寸缩放: {target_width}x{target_height}")
                else:
                    # 回退到固定大小
                    target_size = self.缩放(300)  # 增大默认尺寸
                    scaled_pixmap = pixmap.scaledToWidth(target_size, Qt.SmoothTransformation)
                    print(f"[调试] 使用固定尺寸: {target_size}px")

                图片行.setIcon(QIcon(scaled_pixmap))
                图片行.setTextAlignment(Qt.AlignCenter)
                图片行.setData(Qt.UserRole, image_path)
                self.table_widget.setItem(row, self.图片行, 图片行)

                # 关键：设置表格图标大小，确保图片能正确显示
                self.table_widget.setIconSize(scaled_pixmap.size())

                # 强制刷新表格显示
                self.table_widget.viewport().update()

                print(f"[调试] 第{row+1}行主图刷新完成，图片大小: {scaled_pixmap.width()}x{scaled_pixmap.height()}")
                print(f"[调试] 表格图标大小已设置为: {scaled_pixmap.size().width()}x{scaled_pixmap.size().height()}")
            else:
                print(f"[调试] 第{row+1}行没有有效的图片路径")
        except Exception as e:
            print(f"[调试] 刷新主图显示失败: {e}")

    def yuyin(self, row):
        if A0_config.NOVIP and self.端口 == 'yun':
            self.提示('您的会员已过期, 请联系管理购买卡密后在软件左侧\"会员充值\"')
            return
        self.tanchuang = True
        if not self.running:
            self.zhuxian = False
        if self.zhuxian:
            print('有任务运行中')
            self.提示(f'{A0_config.name}, 有任务运行中')
        else:  # inserted
            self.mood = self
            self.mood.yu = True
            self.修改第二个按钮文字(row, '语音合成中', 3)
            self.单次任务 = '语音'
            self.运行行数 = row
            self.worker_solo.add_task(self.单次任务, self.运行行数)

    def Double_click(self, row, column, co=None):
        if column == self.图片行:
            image_path = self.项目配置['data'][row]['img']['path']
            video_path = self.项目配置['data'][row]['video']
            if video_path and jduvudu312us_usjlq.path.exists(video_path) and (not co):
                self.on_image_label_double_clicked('event', video_path)
                return
            if image_path!= '':
                dialog = PreviewImageDialog(image_path=image_path, row=row, column=column, parent=self)
                dialog.exec_()
        else:  # inserted
            if column == self.原图片行:
                print(self.项目配置['data'][row]['img_yuantu'])
                image_path = A0_config.改文件名(self.项目配置['data'][row]['img_yuantu'])
                dialog = PreviewImageDialog(image_path=image_path, row=row, column=column, parent=self)
                dialog.exec_()

    def Redraw(self, row):
        # print(f"Redraw被调用，行号: {row}")
        if A0_config.NOVIP and self.端口 == 'yun':
            self.提示('您的会员已过期, 请联系管理购买卡密后在软件左侧\"会员充值\"')
            return
        self.当前任务 = '单张绘图'
        if not self.running:
            self.zhuxian = False
        if self.zhuxian:
            print('有任务运行中')
            self.提示(f'{A0_config.name}, 有任务运行中')
            return
        # print(f"开始获取插件模型")
        self.获取插件模型()
        # print(f"开始修改按钮文字，任务模式: {self.任务模式}")
        if self.任务模式 == '视频转绘':
            self.修改第二个按钮文字(row, '正在绘图', 0)
        else:  # inserted
            self.修改第二个按钮文字(row, '正在绘图', 2)
        # print(f"设置任务参数：单次任务={self.单次任务}, 运行行数={row}")
        self.单次任务 = '图片'
        self.运行行数 = row
        # print(f"添加任务到队列: {self.单次任务}, {self.运行行数}")
        self.worker_solo.add_task(self.单次任务, self.运行行数)
        # print(f"Redraw方法执行完成")

    def make_video(self, row):
        if '127.0.0.1' in A0_config.config['云端地址'] and '工作流' not in A0_config.config['SD版本'] and A0_config.config['口型对齐']:
            提示 = '动态视频支持云端部署, 或自配工作流, 本地部署包尚未上线, 敬请期待'
            self.提示(提示)
        else:  # inserted
            if A0_config.NOVIP:
                self.提示('VIP专属功能, 请先升级为VIP')
            else:  # inserted
                self.当前任务 = '单个视频'
                if not self.running:
                    self.zhuxian = False
                if self.zhuxian:
                    print('有任务运行中')
                    self.提示(f'{A0_config.name}, 有任务运行中')
                else:  # inserted
                    if self.任务模式 == '视频转绘':
                        self.修改第二个按钮文字(row, '动画制作中', 1)
                    else:  # inserted
                        self.修改第二个按钮文字(row, '动画制作中', 4)
                    self.单次任务 = '视频'
                    self.运行行数 = row
                    self.worker_solo.add_task(self.单次任务, self.运行行数)

    def keep_first_lora(self, text):
        pattern = '<lora:[^>]+>'
        matches = re.findall(pattern, text)
        if len(matches) > 1:
            for match in matches[1:]:
                text = text.replace(match, '')
        return text.replace(',,', ',')

    def reasoning(self, row):
        if A0_config.NOVIP and self.端口 == 'yun':
            self.提示('您的会员已过期, 请联系管理购买卡密后在软件左侧\"会员充值\"')
        else:  # inserted
            if not self.running:
                self.zhuxian = False
            if self.zhuxian:
                print('有任务运行中')
                self.提示('有任务运行中')
            else:  # inserted
                self.修改第二个按钮文字(row, '正在推理', 1)
                self.单次任务 = '关键词'
                self.运行行数 = row
                self.worker_solo.add_task(self.单次任务, self.运行行数)

    def gaixie(self, row):
        if A0_config.NOVIP and self.端口 == 'yun':
            self.提示('您的会员已过期, 请联系管理购买卡密后在软件左侧\"会员充值\"')
        else:  # inserted
            if not self.running:
                self.zhuxian = False
            if self.zhuxian:
                print('有任务运行中')
                self.提示('有任务运行中')
            else:  # inserted
                self.修改第二个按钮文字(row, '正在改写', 1)
                self.单次任务 = '改写'
                self.运行行数 = row
                self.worker_solo.add_task(self.单次任务, self.运行行数)

    def 修改第二个按钮文字(self, row, content, number):
        try:
            cell_widget = self.table_widget.cellWidget(row, self.操作行)
            layout = cell_widget.layout()
            buttons = [layout.itemAt(i).widget() for i in range(layout.count()) if layout.itemAt(i).widget()]
            for button in buttons:
                if layout.indexOf(button) == number:
                    正在结果 = ['正在改写', '正在推理', '正在绘图', '正在放大', '语音合成中', '动画制作中']
                    if content in 正在结果:
                        button.setStyleSheet(f'QPushButton {{background-color: {A0_config.选中色}; color: {A0_config.文字色};}}')
                    else:  # inserted
                        if '完成' in content:
                            button.setStyleSheet(f'QPushButton {{background-color: {A0_config.选中色}; color: {A0_config.文字色};}}')
                        else:  # inserted
                            if '失败' in content:
                                button.setStyleSheet(f'QPushButton {{background-color: {A0_config.失败色}; color: {A0_config.文字色};}}')
                            else:  # inserted
                                button.setStyleSheet('')
                    button.setText(content)
        except Exception as e:
            print(e)

    def 修改单元格文字(self, row, line, content):
        try:
            if self.任务模式 == '原创模式':
                if line == self.内容行:
                    # 更新内容行的自定义文本组件
                    内容行_widget = self.table_widget.cellWidget(row, self.内容行)
                    if 内容行_widget:
                        内容文本框 = 内容行_widget.layout().itemAt(0).widget()
                        if isinstance(内容文本框, CustomTextEdit_content):
                            内容文本框.setText(content)
                else:  # inserted
                    if line == self.动图提示词行:
                        cell_widget = self.table_widget.cellWidget(row, self.动图提示词行)
                        layout = cell_widget.layout()
                        for j in range(layout.count()):
                            item = layout.itemAt(j).widget()
                            if isinstance(item, CustomTextEdit_shipin):
                                item.setPlainText(content)
                    else:  # inserted
                        cell_widget = self.table_widget.cellWidget(row, self.正面词行)
                        layout = cell_widget.layout()
                        for j in range(layout.count()):
                            item = layout.itemAt(j)
                            if isinstance(item, QLayout):
                                layout_item = item.layout()
                                所有组件 = []
                                for i in range(layout_item.count()):
                                    if layout_item.itemAt(i).widget():
                                        所有组件.append(layout_item.itemAt(i).widget())
                                for 组件 in 所有组件:
                                    if isinstance(组件, CustomTextEdit):
                                        if layout_item.indexOf(组件) == 0:
                                            组件.setPlainText(content)
            else:  # inserted
                item = QTableWidgetItem(content)
                self.table_widget.setItem(row, line, item)
        except Exception as e:
            print(e)

    def 图片转换(self, img_file):
        image = Image.open(img_file)
        output = io.BytesIO()
        image.save(output, format='PNG')
        image_bytes = output.getvalue()
        encoded_image = base64.b64encode(image_bytes).decode('utf-8')
        return encoded_image

    def 高清放大(self, json_data, width, height, mood):
        from pyz.任务运行文件.A18_comfyui import Comfyui
        try:
            if self.huitu and (not self.huitu):
                time.sleep(1)
                pass
            self.huitu = True
            图像长度 = width
            图像宽度 = height
            生成数量 = 1
            采样方法 = A0_config.config['图片采样方法']
            调度器 = A0_config.config['调度器']
            采样步数 = int(A0_config.config['图片采样步数'])
            提示词相关性 = float(A0_config.config['提示词相关性'])
            encoded_image = ''
            prompt = json_data['提示词']
            print(prompt)
            if not json_data['提示词']:
                print('先出关键词后再画图')
                return False
            print(f"{self.当前项目} 第 {json_data['当前编号']} 张图放大中")
            pass
            if not self.running:
                print('任务停止')
                self.停止 = True
                self.huitu = False
                return []
            try:
                url = A0_config.config['云端地址'].replace('，', ',').replace(' ', '').split(',')[0]
                if url.endswith('/'):
                    url = url[:(-1)]
                if self.任务模式 == '克隆模式' and A0_config.config['所有调度器']:
                    url = f'{url}/sdapi/v1/img2img'
                    encoded_image = self.图片转换(json_data['图片路径'])
                else:  # inserted
                    url = f'{url}/sdapi/v1/txt2img'
                juese_img = ''
                for k, v in self.项目配置['fenjing'].items():
                    if k == '全局':
                        juese_img = v.get('img')
                        break
                角色 = self.项目配置['data'][json_data.get('当前行')]['fenjing']
                if 角色:
                    for k, v in self.项目配置['fenjing'].items():
                        if k == 角色.split(',')[0]:
                            juese_img = v.get('img')
                            break
                if juese_img and self.ip_adapter == '' and (self.任务模式!= '克隆模式'):
                    print('当前缺少必要模型, 参考图功能未生效, 请使用专用安装包或在使用说明中下载指定模型, 云端请进入仙宫云OS, 双击\"配置环境\"')
                if self.任务模式 == '克隆模式' and self.lineart!= '' and (not A0_config.config['所有调度器']):
                    encoded_image = self.图片转换(json_data['图片路径'])
                    args = [{'input_image': encoded_image, 'module': 'lineart', 'model': self.lineart, 'weight': float(A0_config.config['修复重绘幅度']), 'resize_mode': 1, 'processor_res': 64, 'guidance': 1, 'guidance_start': 0, 'guidance_end': 1, 'control_mode': 0, 'pixel_perfect': True}]
                else:  # inserted
                    if juese_img and self.ip_adapter!= '':
                        encoded_image = self.图片转换(juese_img)
                        if 'xl' in self.ip_adapter.lower():
                            args = [{'input_image': encoded_image, 'module': 'ip-adapter_clip_sdxl', 'model': self.ip_adapter, 'weight': float(A0_config.config['Lora强度']), 'resize_mode': 1, 'processor_res': 64, 'guidance': 1, 'guidance_start': 0, 'guidance_end': 1, 'control_mode': 0, 'pixel_perfect': True}]
                        else:  # inserted
                            args = [{'input_image': encoded_image, 'module': 'ip-adapter_clip_sd15', 'model': self.ip_adapter, 'weight': float(A0_config.config['Lora强度']), 'resize_mode': 1, 'processor_res': 64, 'guidance': 1, 'guidance_start': 0, 'guidance_end': 1, 'control_mode': 0, 'pixel_perfect': True}]
                    else:  # inserted
                        args = []
                if A0_config.config['极速绘图'] and 'turbo' not in A0_config.config['当前选中模型'].lower():
                    if 'xl' in A0_config.config['当前选中模型'].lower():
                        lcm_lora = [key for key in A0_config.config['lora'] if 'lcm' in key.lower() and 'xl' in key.lower()]
                    else:  # inserted
                        lcm_lora = [key for key in A0_config.config['lora'] if 'lcm' in key.lower() and 'xl' not in key.lower()]
                    if lcm_lora and ('LCM' in A0_config.config['采样方法'] or 'lcm' in A0_config.config['采样方法']):
                        prompt = '{},<lora:{}:0.8>'.format(prompt, lcm_lora[0])
                    else:  # inserted
                        提示 = '当前SD缺少必要模型或插件, 无法使用 \"极速绘图\" 模式, 请下载专用版本, 云端请进入仙宫云OS, 双击\"配置环境\"'
                        mood._signal.emit((提示, '弹窗', '弹窗'))
                        self.停止 = True
                        self.huitu = False
                        return []
                data = {'prompt': prompt, 'negative_prompt': self.通用反面词, 'sampler_name': 采样方法, 'batch_size': 1, 'n_iter': int(生成数量), 'steps': int(采样步数), 'cfg_scale': 提示词相关性, 'width': 图像长度, 'height': 图像宽度, 'send_images': True, 'save_images': False, 'alwayson_scripts': {'controlnet': {'args': args}}, 'restore_faces': A0_config.config['面部修复'], 'enable_hr': True, 'denoising_strength': False, '0.3': False, 'firstphase_width': False, 'firstphase_height': False, 'hr_scale': False, '修复放大倍数': False, 'hr_upscaler': False, '高清修复算法': False, 'hr_second_pass_steps': 0, 'hr_resize_x': False, 'hr_resize_y': False, 'seed': False, 'json_data': False, '随机种子':False}  # postinserted
                if A0_config.config['所有调度器']:
                    data['scheduler'] = 调度器
                if self.任务模式 == '克隆模式' and A0_config.config['所有调度器']:
                    data['init_images'] = [encoded_image]
                    data['width'] = int(图像长度 * int(A0_config.config['修复放大倍数']))
                    data['height'] = int(图像宽度 * int(A0_config.config['修复放大倍数']))
                headers = {'accept': 'application/json', 'Content-Type': 'application/json'}
                try:
                    # 使用重试机制发送POST请求
                    response = self._webui_post_重试(url, data, headers, max_retries=6)
                    if response:
                        data = response.json()
                        file_path = ''
                        for i in range(int(生成数量)):
                            base64_data = data['images'][i]
                            image_data = base64.b64decode(base64_data)
                            image = Image.open(BytesIO(image_data))
                            file_name = f"{json_data['当前编号']}(高清).png"
                            file_path = jduvudu312us_usjlq.path.join(self.图片文件夹, file_name)
                            image.save(file_path, compress_level=0)
                        print(f"{self.当前项目} 第 {json_data['当前编号']} 张图放大成功")
                        self.huitu = False
                        return file_path
                    else:  # inserted
                        返回错误 = response.text
                        print(f'重绘放大错误: {返回错误}')
                        if 'not found' in 返回错误:
                            提示 = '重绘放大错误:当前 图片采样方法 不可用,请更换'
                        else:  # inserted
                            if 'CUDA' in 返回错误:
                                提示 = '重绘放大错误:显卡显存不足关闭其他软件, 减小图片尺寸, 或请在SD_UI_高级选项_调整显存优化'
                            else:  # inserted
                                if 'Not Found' in 返回错误:
                                    提示 = '重绘放大错误:SD的API接口没打开,问题主要出现在云端,启动WebUI时勾选 \"API接口\"'
                                else:  # inserted
                                    if 'is a LoRA' in 返回错误:
                                        提示 = '重绘放大错误:SD的模型错误,更换模型'
                                    else:  # inserted
                                        if 'Internal Server Error' in 返回错误:
                                            提示 = '重绘放大错误:SD的高清修复算法匹配不上,更换算法\n或者打开\"SD_高级选项_Channels Last内存格式优化(关闭)\"'
                                        else:  # inserted
                                            if 'in VAE' in 返回错误 or 'and cuda' in 返回错误:
                                                提示 = '重绘放大错误:打开SD_高级设置_计算精度设置_把\"开启VAE模型半精度优化\"关了'
                                            else:  # inserted
                                                if '504' in 返回错误:
                                                    提示 = '重绘放大错误:出图太慢, 失去连接, 部分电脑第一次使用会比较慢,请先看SD日志是否正常\n如果显卡够用可能是SD在下载东西, 下载完就可以了, 云端下载慢开学术加速.\n如果显卡不够用请: 降低图片规格,长宽最小300以上, 或者关闭其他占用资源的软件, 或者升级显卡配置, 或者用云端'
                                                else:  # inserted
                                                    if 'half' in 返回错误:
                                                        提示 = '重绘放大错误:打开SD_高级设置_计算精度设置_把\"开启U-Net 模型半精度优化\"关了'
                                                    else:  # inserted
                                                        提示 = f'重绘放大错误:出图未知错误,尝试换采样方法或放大算法,无法解决请截图联系管理员: {返回错误}'
                        mood._signal.emit((提示, '弹窗', '弹窗'))
                        return []
                except requests.Timeout:
                    print('请求超时')
                    提示 = '重绘放大错误:请求超时,检测SD的日志提示是否正常, 如果显卡够用可能是SD在下载东西, 下载完就可以了, 云端下载慢开学术加速.\n如果显卡不够用请: 降低图片规格,长宽最小300以上, 或者关闭其他占用资源的软件, 或者升级显卡配置, 或者用云端'
                    mood._signal.emit((提示, '弹窗', '弹窗'))
                    return []
                except Exception as e:
                    print('出图异常', e)
                    提示 = '重绘放大错误:图片生成异常,检测SD是否正常'
                    mood._signal.emit((提示, '弹窗', '弹窗'))
                    return []
            except Exception as err:
                print(f'重绘放大错误:: 请检查本地或者云端Stable-diffusion是否正常启动，错误信息：{err}')
                返回错误 = str(err)
                if '无法连接' in 返回错误:
                    提示 = '重绘放大错误:请先启动SD, 或者关闭VPN, 或者使用推荐SD安装包, 也有可能是你云端运行,没有填写地址,或者没有选云端环境'
                else:  # inserted
                    if 'timeout' in 返回错误:
                        提示 = '重绘放大错误:请求超时,如果显卡够用可能是SD在下载东西, 检测SD的日志提示是否正常.\n如果显卡不够用请: 降低图片规格,长宽最小300以上, 或者关闭其他占用资源的软件, 或者升级显卡配置, 或者用云端'
                    else:  # inserted
                        提示 = f'重绘放大错误: 图片生成异常,异常状态:{err}'
                mood._signal.emit((提示, '弹窗', '弹窗'))
                return []
        except Exception as e:
            print(e)

    def 文生图(self, json_data, mood, codes=1, url=None, w=None, h=None):
        from pyz.任务运行文件.A18_comfyui import Comfyui, 保存图片
        # print(f"文生图被调用，json_data keys: {json_data.keys()}")
        # print(f"json_data内容: {json_data}")
        # print(json_data['人物分镜'])  # 注释掉这行，因为可能没有这个键
        if url is None:
            url = A0_config.config['云端地址'].replace('，', ',').replace(' ', '').split(',')[0]
        try:
            if codes!= 1:
                self.running = True
                A0_config.config['每张图片数量'] = 1
            图片路径 = self.项目配置['data'][json_data['当前行']]['img_beixuan']
            已有数量 = len(self.项目配置['data'][json_data['当前行']]['img_beixuan'])
            if A0_config.config['SD版本'] == 'WebUI':
                if self.huitu and (not self.huitu):
                    time.sleep(1)
                    pass
                self.huitu = True
                图片高清修复 = A0_config.config['图片高清修复']
                图像长度 = int(A0_config.config['图片长度'])
                图像宽度 = int(A0_config.config['图片宽度'])
                生成数量 = int(A0_config.config['每张图片数量'])
                采样方法 = A0_config.config['图片采样方法']
                调度器 = A0_config.config['调度器']
                采样步数 = int(A0_config.config['图片采样步数'])
                提示词相关性 = float(A0_config.config['提示词相关性'])
                encoded_image = ''
                提示词 = json_data['提示词']
                if json_data['lora']:
                    prompt = f"{提示词},{','.join(json_data['lora'][0])}".replace(':1>', f":{A0_config.config['Lora强度']}>").replace(',,', ',').replace('lora ', 'lora:')
                else:  # inserted
                    prompt = f'{提示词}'.replace(',,', ',')
                print('正面词: ', prompt)
                folder_path = f'{self.图片文件夹}'
                if not json_data['提示词']:
                    print('先出关键词后再画图')
                    return False
                print(f"{self.当前项目} 第 {json_data['当前编号']} 张图生成中")
                while self.running:
                    if not self.running:
                        print('任务停止')
                        self.停止 = True
                        self.huitu = False
                        return []
                    try:
                        if url.endswith('/'):
                            url = url[:(-1)]
                        if self.任务模式 == '克隆模式' and A0_config.config['所有调度器']:
                            url = f'{url}/sdapi/v1/img2img'
                            encoded_image = self.图片转换(json_data['原图片路径'])
                        else:  # inserted
                            url = f'{url}/sdapi/v1/txt2img'
                        juese_img = self.项目配置['fenjing']['全局'].get('img')
                        角色 = self.项目配置['data'][json_data.get('当前行')]['fenjing']
                        if 角色:
                            角色 = 角色.split(',')
                            角色 = ','.join([sentence.strip() for sentence in 角色 if sentence.strip()])
                            juese_img = self.项目配置['fenjing'][角色.split(',')[0]].get('img')
                        if juese_img and self.ip_adapter == '' and (self.任务模式!= '克隆模式'):
                            print('当前缺少必要模型, 参考图功能未生效, 请使用专用安装包或在使用说明中下载指定模型, 云端请进入仙宫云OS, 双击\"配置环境\"')
                        if self.任务模式 == '克隆模式' and self.lineart!= '' and (not A0_config.config['所有调度器']):
                            encoded_image = self.图片转换(json_data['原图片路径'])
                            args = [{'input_image': encoded_image, 'module': 'lineart', 'model': self.lineart, 'weight': float(A0_config.config['修复重绘幅度']), 'resize_mode': 1, 'processor_res': 64, 'guidance': 1, 'guidance_start': 0, 'guidance_end': 1, 'control_mode': 0, 'pixel_perfect': True}]
                        else:  # inserted
                            if juese_img and self.ip_adapter!= '' and jduvudu312us_usjlq.path.exists(juese_img) and (self.任务模式 == '原创模式'):
                                encoded_image = self.图片转换(juese_img)
                                if 'xl' in self.ip_adapter.lower():
                                    args = [{'input_image': encoded_image, 'module': 'ip-adapter_clip_sdxl', 'model': self.ip_adapter, 'weight': float(A0_config.config['Lora强度']), 'resize_mode': 1, 'processor_res': 64, 'guidance': 1, 'guidance_start': 0.2, 'guidance_end': 1, 'control_mode': 0, 'pixel_perfect': True}]
                                else:  # inserted
                                    args = [{'input_image': encoded_image, 'module': 'ip-adapter_clip_sd15', 'model': self.ip_adapter, 'weight': float(A0_config.config['Lora强度']), 'resize_mode': 1, 'processor_res': 64, 'guidance': 1, 'guidance_start': 0.2, 'guidance_end': 1, 'control_mode': 0, 'pixel_perfect': True}]
                            else:  # inserted
                                args = []
                        if A0_config.config['极速绘图'] and 'turbo' not in A0_config.config['当前选中模型'].lower():
                            if 'xl' in A0_config.config['当前选中模型'].lower():
                                lcm_lora = [key for key in A0_config.config['lora'] if 'lcm' in key.lower() and 'xl' in key.lower()]
                            else:  # inserted
                                lcm_lora = [key for key in A0_config.config['lora'] if 'lcm' in key.lower() and 'xl' not in key.lower()]
                            if lcm_lora and 'LCM' in A0_config.config['采样方法']:
                                prompt = '{},<lora:{}:0.8>'.format(prompt, lcm_lora[0])
                            else:  # inserted
                                提示 = '当前SD缺少必要模型或插件, 无法使用 \"极速绘图\" 模式, 请下载专用版本, 如果是云端请进入仙宫云OS, 双击\"配置环境\"'
                                mood._signal.emit((提示, '弹窗', '弹窗'))
                                self.停止 = True
                                self.huitu = False
                                return []
                        # 获取当前行的角色名
                        seed_to_use = -1
                        当前角色 = self.项目配置['data'][json_data['当前行']].get('fenjing', '')
                        if 当前角色:
                            当前角色 = 当前角色.split(',')[0].strip()  # 取第一个角色名
                            # 从角色专用种子配置中获取种子值
                            if '角色专用种子配置' in self.项目配置:
                                角色配置 = self.项目配置['角色专用种子配置'].get(当前角色, {})
                                if 角色配置.get('enabled', False):
                                    seed_to_use = 角色配置.get('seed')
                                    print(f"使用角色[{当前角色}]的专用种子: {seed_to_use}")

                        # 如果没有找到角色专用种子，生成随机种子
                        if seed_to_use == -1:
                            seed_to_use = random.randint(1000000000, 9999999999)
                            print(f"使用随机种子: {seed_to_use}")
                        data = {'prompt': prompt, 'negative_prompt': self.通用反面词, 'sampler_name': 采样方法, 'batch_size': 1, 'n_iter': int(生成数量), 'steps': int(采样步数), 'cfg_scale': 提示词相关性, 'width': 图像长度, 'height': 图像宽度, 'send_images': True, 'save_images': False, 'alwayson_scripts': {'controlnet': {'args': args}}, 'args': False, 'restore_faces': A0_config.config['修复放大倍数'], 'hr_upscaler': 高清修复算法, 'hr_second_pass_steps': 0, 'hr_resize_x': 0, 'hr_resize_y': 0, 'seed': seed_to_use, 'styles': subseed, 'subseed_strength': seed_resize_from_h, 'seed_resize_from_w': tiling, 'do_not_save_samples': do_not_save_grid, 'eta': s_churn, 's_tmax': s_tmin, 's_noise': override_settings, 'override_settings_restore_afterwards': data}
                        if A0_config.config['所有调度器']:
                            data['scheduler'] = 调度器
                        if self.任务模式 == '克隆模式' and A0_config.config['所有调度器']:
                            data['init_images'] = [encoded_image]
                            data['denoising_strength'] = 1 - float(A0_config.config['修复重绘幅度'])
                            if 图片高清修复:
                                data['width'] = int(图像长度 * int(A0_config.config['修复放大倍数']))
                                data['height'] = int(图像宽度 * int(A0_config.config['修复放大倍数']))
                        headers = {'accept': 'application/json', 'Content-Type': 'application/json'}
                        try:
                            # 使用重试机制发送POST请求
                            response = self._webui_post_重试(url, data, headers, max_retries=6)
                            if response:
                                print('画图返回成功')
                                data = response.json()
                                if codes == 1:
                                    for i in range(int(生成数量)):
                                        base64_data = data['images'][i]
                                        图片种子 = json.loads(data['info'])['all_seeds'][i]
                                        image_data = base64.b64decode(base64_data)
                                        image = Image.open(BytesIO(image_data))
                                        file_name = f"{json_data['当前编号']}({i + 已有数量}).png"
                                        file_path = jduvudu312us_usjlq.path.join(folder_path, file_name)
                                        print(f'图片:{file_name}, 图片种子:{图片种子}')
                                        图片路径.append({'path': file_path, 'seep': 图片种子})
                                        image.save(file_path, compress_level=0)
                                    print(f"{self.当前项目} 第 {json_data['当前编号']} 张图绘制成功")
                                    self.huitu = False
                                    # 注意：不再清空default_seed，保持seed值固定用于后续生成
                                    # self.default_seed = None
                                    # self.default_seed_fenjing = None
                                    return 图片路径
                                else:  # inserted
                                    for i in range(int(生成数量)):
                                        base64_data = data['images'][i]
                                        image_data = base64.b64decode(base64_data)
                                        image = Image.open(BytesIO(image_data))
                                        file_path = json_data['图片路径']
                                        图片路径.append(file_path)
                                        image.save(file_path, compress_level=0)
                                        return 图片路径
                            else:  # inserted
                                返回错误 = response.text
                                print(f'文生图返回: {返回错误}')
                                提示 = ''
                                if 'not found' in 返回错误:
                                    提示 = '当前 图片采样方法 不可用,请更换'
                                else:  # inserted
                                    if 'CUDA' in 返回错误 and 'G' in 返回错误:
                                        提示 = '显卡显存不足关闭其他软件, 减小图片尺寸, 或请在SD_UI_高级选项_调整显存优化'
                                    else:  # inserted
                                        if 'CUDA' in 返回错误 and 'G' not in 返回错误:
                                            提示 = '新增模型后需要刷新网页端, 打开UI后按 F5 刷新, 还提示错误请重启SD'
                                        else:  # inserted
                                            if 'Not Found' in 返回错误:
                                                提示 = 'SD的API接口没打开,问题主要出现在云端,启动WebUI时勾选 \"API接口\"'
                                            else:  # inserted
                                                if 'is a LoRA' in 返回错误:
                                                    提示 = 'SD的模型错误,更换模型'
                                                else:  # inserted
                                                    if 'Internal Server Error' in 返回错误:
                                                        提示 = 'SD的高清修复算法匹配不上,更换算法\n或者打开\"SD_高级选项_Channels Last内存格式优化(关闭)\"'
                                                    else:  # inserted
                                                        if 'in VAE' in 返回错误 or 'and cuda' in 返回错误:
                                                            提示 = '打开SD_高级设置_计算精度设置_把\"开启VAE模型半精度优化\"关了'
                                                        else:  # inserted
                                                            if '504' in 返回错误:
                                                                提示 = '出图太慢, 失去连接, 部分电脑第一次使用会比较慢,请先看SD日志是否正常\n如果显卡够用可能是SD在下载东西, 下载完就可以了, 云端下载慢开学术加速.\n如果显卡不够用请: 降低图片规格,长宽最小300以上, 或者关闭其他占用资源的软件, 或者升级显卡配置, 或者用云端'
                                                            else:  # inserted
                                                                if 'half' in 返回错误:
                                                                    提示 = '打开SD_高级设置_计算精度设置_把\"开启U-Net 模型半精度优化\"关了'
                                                                else:  # inserted
                                                                    提示 = f'出图未知错误,尝试换采样方法或放大算法,无法解决请截图联系管理员: {返回错误}'
                                mood._signal.emit((提示, '弹窗', '弹窗'))
                                return []
                        except requests.Timeout:
                            print('请求超时')
                            提示 = '请求超时,检测SD的日志提示是否正常, 如果显卡够用可能是SD在下载东西, 下载完就可以了, 云端下载慢开学术加速.\n如果显卡不够用请: 降低图片规格,长宽最小300以上, 或者关闭其他占用资源的软件, 或者升级显卡配置, 或者用云端'
                            mood._signal.emit((提示, '弹窗', '弹窗'))
                            return []
                        except Exception as e:
                            print('出图异常', e)
                            提示 = '图片生成异常,检测SD是否正常'
                            mood._signal.emit((提示, '弹窗', '弹窗'))
                            return []
                    except Exception as err:
                        print(f'文生图: 请检查本地或者云端Stable-diffusion是否正常启动，错误信息：{err}')
                        返回错误 = str(err)
                        if '无法连接' in 返回错误:
                            提示 = '请先启动SD, 或者关闭VPN, 或者使用推荐SD安装包, 也有可能是你云端运行,没有填写地址,或者没有选云端环境'
                        else:  # inserted
                            if 'timeout' in 返回错误:
                                提示 = '请求超时,如果显卡够用可能是SD在下载东西, 检测SD的日志提示是否正常.\n如果显卡不够用请: 降低图片规格,长宽最小300以上, 或者关闭其他占用资源的软件, 或者升级显卡配置, 或者用云端'
                            else:  # inserted
                                提示 = f'图片生成异常,异常状态:{err}'
                        mood._signal.emit((提示, '弹窗', '弹窗'))
                        return []
            else:  # inserted
                # 检查是否启用Pollinations云端模型
                if A0_config.config.get('使用Pollinations', False):
                    # 使用Pollinations云端模型
                    print("使用Pollinations云端模型生成图片")
                    from pyz.工作流.A7_Pollinations工作流 import 生成Pollinations图片

                    # 获取Pollinations配置
                    模型名称 = A0_config.config.get('pollinations_model', 'flux')
                    图片宽度 = int(A0_config.config.get('图片长度', 800))  # 图片长度对应宽度
                    图片高度 = int(A0_config.config.get('图片宽度', 600))  # 图片宽度对应高度
                    图片数量 = int(A0_config.config.get('每张图片数量', 2))

                    print(f"[调试] 读取配置 - 图片长度(宽度): {A0_config.config.get('图片长度')}")
                    print(f"[调试] 读取配置 - 图片宽度(高度): {A0_config.config.get('图片宽度')}")
                    print(f"[调试] Pollinations最终尺寸: {图片宽度}x{图片高度}")
                    反面词 = A0_config.config.get('反面词', '')

                    # 从软件设置中获取更多参数
                    增强 = A0_config.config.get('pollinations_enhance', True)
                    安全模式 = A0_config.config.get('pollinations_safe', True)

                    # 获取质量控制参数 - 修复：使用正确的配置键名
                    CFG系数 = float(A0_config.config.get('提示词相关性', 8.7))  # 配置文件中的实际键名
                    采样步数 = int(A0_config.config.get('图片采样步数', 39))    # 配置文件中的实际键名
                    重绘幅度 = float(A0_config.config.get('修复重绘幅度', 0.16))  # 配置文件中的实际键名

                    print(f"[调试] 质量参数 - CFG: {CFG系数}, 步数: {采样步数}, 重绘: {重绘幅度}")
                    print(f"[调试] 配置键名映射:")
                    print(f"   提示词相关性 -> CFG系数: {A0_config.config.get('提示词相关性')}")
                    print(f"   图片采样步数 -> 采样步数: {A0_config.config.get('图片采样步数')}")
                    print(f"   修复重绘幅度 -> 重绘幅度: {A0_config.config.get('修复重绘幅度')}")

                    # 获取风格和种子设置
                    风格 = A0_config.config.get('pollinations_style', '无风格')  # 使用专门的Pollinations风格
                    指定种子 = None

                    # 🎯 种子优先级：组合种子 > 角色专用种子 > 随机种子
                    指定种子 = json_data.get('组合种子')  # 优先使用组合种子
                    if 指定种子:
                        print(f"✅ Pollinations使用多角色组合种子: {指定种子}")
                    else:
                        # 原有的单角色种子逻辑
                        当前角色 = json_data.get('人物分镜', '')
                        if 当前角色:
                            if isinstance(当前角色, str) and ',' in 当前角色:
                                当前角色 = 当前角色.split(',')[0].strip()
                            elif isinstance(当前角色, list) and 当前角色:
                                当前角色 = 当前角色[0]

                            # 检查角色专用种子
                            if '角色专用种子配置' in self.项目配置:
                                角色配置 = self.项目配置['角色专用种子配置'].get(当前角色, {})
                                if 角色配置.get('enabled', False) and 角色配置.get('seed') is not None:
                                    指定种子 = 角色配置['seed']
                                    print(f"✅ Pollinations使用角色[{当前角色}]的专用种子: {指定种子}")

                        # 如果没有角色专用种子，使用随机种子
                        if 指定种子 is None:
                            print("Pollinations将使用随机种子")

                    # 获取当前行的备选图片组
                    当前行 = json_data.get('当前行', 0)
                    备选图片组 = self.项目配置['data'][当前行]['img_beixuan'] if 当前行 < len(self.项目配置['data']) else []

                    # 获取角色名用于参考图
                    角色名 = json_data.get('人物分镜', '')
                    if isinstance(角色名, list):
                        角色名 = ','.join(角色名)  # 多角色用逗号连接

                    print(f"✅ Pollinations使用角色名获取参考图: {角色名}")

                    # 生成图片
                    所有图片 = 生成Pollinations图片(
                        提示词=json_data['提示词'],
                        模型名称=模型名称,
                        图片宽度=图片宽度,
                        图片高度=图片高度,
                        图片数量=图片数量,
                        项目路径=self.图片文件夹,
                        当前编号=json_data['当前编号'],
                        已有数量=已有数量,
                        备选图片组=备选图片组,
                        反面词=反面词,
                        增强=增强,
                        安全模式=安全模式,
                        风格=风格,
                        种子=指定种子,
                        CFG系数=CFG系数,
                        采样步数=采样步数,
                        重绘幅度=重绘幅度,
                        角色名=角色名
                    )

                    if 所有图片:
                        # 更新项目配置中的图片信息 - 追加新图片而不是替换
                        当前行 = json_data['当前行']
                        现有图片 = self.项目配置['data'][当前行]['img_beixuan']

                        # 追加新生成的图片到现有列表
                        现有图片.extend(所有图片)
                        # print(f"[调试] Pollinations追加了{len(所有图片)}张新图片，总数: {len(现有图片)}")

                        # 🔥 关键修复：立即更新主图片路径为新生成的第一张图片
                        新图片路径 = 所有图片[0]['path']
                        self.项目配置['data'][当前行]['img']['path'] = 新图片路径
                        self.项目配置['data'][当前行]['img']['seep'] = 所有图片[0]['seep']
                        print(f"✅ 主图片已更新为: {新图片路径}")

                        self.保存配置()

                        # 🔥 关键修复：通过信号触发界面更新（避免线程冲突）
                        # print(f"[调试] Pollinations通过信号触发界面更新...")
                        图片路径 = 新图片路径  # 使用已经更新的主图路径

                        # 构造表格数据，与其他绘图方式保持一致
                        表格数据 = {
                            '当前行': 当前行,
                            '图片路径': 图片路径,
                            '备选图片路径': 所有图片,
                            '已更新主图': True  # 标记主图片已经更新，避免重复更新
                        }

                        # 通过信号发送到主线程处理（避免线程冲突）
                        mood._signal.emit(('绘图完成', 表格数据, '图片'))

                        print(f"{self.当前项目} 第 {json_data['当前编号']} 张图绘制成功 (Pollinations)")
                        return 所有图片
                    else:
                        if self.running:
                            提示 = 'Pollinations图片生成失败'
                            mood._signal.emit((提示, '弹窗', '弹窗'))
                            return []
                elif A0_config.config['SD版本'] == 'ComfyUI':
                    self.running = True  # 关键修复：启动ComfyUI绘图任务前设置running状态

                    # 检查是否启用多GPU加速
                    if A0_config.config.get('启用多GPU加速', False):
                        print("🚀 使用多GPU加速模式生成图片")
                        from pyz.工作流.A8_多GPU加速工作流 import 生成多GPU加速工作流, 检查多GPU支持

                        # 检查多GPU支持
                        多GPU支持 = 检查多GPU支持()
                        print(f"📊 多GPU状态: {多GPU支持['推荐配置']}")

                        if 多GPU支持['支持多GPU']:
                            # 确定任务类型
                            任务类型 = "标准生成"
                            if A0_config.config.get('InstantID', False):
                                任务类型 = "InstantID"
                                print("📋 使用InstantID工作流")
                            elif A0_config.config.get('图片高清修复', False):
                                任务类型 = "高质量生成"
                                print("📋 使用高质量生成工作流")
                            elif int(A0_config.config.get('每张图片数量', 1)) > 2:
                                任务类型 = "批量生成"
                                print("📋 使用批量生成工作流")
                            else:
                                print("📋 使用标准工作流")

                            print(f"📋 任务类型: {任务类型}")
                            print("✅ 多GPU工作流已配置，开始图像生成...")

                    # 🎯 种子优先级：组合种子 > 角色专用种子 > default_seed > 随机种子
                    随机种子 = json_data.get('组合种子')  # 优先使用组合种子
                    if 随机种子:
                        print(f"✅ ComfyUI使用多角色组合种子: {随机种子}")
                    else:
                        # 原有的单角色种子逻辑
                        # 1. 首先检查角色专用种子
                        人物分镜_原始 = json_data.get('人物分镜', '')

                        当前角色 = ''
                        if 人物分镜_原始:
                            # 处理不同的数据类型
                            if isinstance(人物分镜_原始, list):
                                # 批量绘图：人物分镜是列表
                                if 人物分镜_原始:
                                    当前角色 = 人物分镜_原始[0]  # 取第一个角色
                            elif isinstance(人物分镜_原始, str):
                                # 单个绘图：人物分镜是字符串
                                if ',' in 人物分镜_原始:
                                    当前角色 = 人物分镜_原始.split(',')[0].strip()
                                else:
                                    当前角色 = 人物分镜_原始.strip()

                        if 当前角色:
                            # 从角色专用种子配置中获取种子值
                            if '角色专用种子配置' in self.项目配置:
                                角色配置 = self.项目配置['角色专用种子配置'].get(当前角色, {})
                                if 角色配置.get('enabled', False) and 角色配置.get('seed') is not None:
                                    随机种子 = 角色配置['seed']
                                    print(f"✅ 使用角色[{当前角色}]的专用种子: {随机种子}")

                    # 2. 如果没有角色专用种子，使用随机种子
                    if 随机种子 is None:
                        if hasattr(self, 'default_seed') and self.default_seed is not None and self.default_seed != -1:
                            随机种子 = self.default_seed
                            print(f"使用设置的seed: {随机种子}")
                        else:
                            # 生成随机种子
                            随机种子 = random.randint(1000000000, 9999999999)
                            print(f"使用随机seed: {随机种子}")
                    json_data['随机种子'] = 随机种子
                    所有图片 = Comfyui(mood=self, mod=mood).文生图_comfy(json_data, url=url, w=w, h=h, 随机种子=随机种子)
                    if 所有图片:
                        # ComfyUI已经在内部下载了图片，返回更新后的备选图片列表
                        print(f"{self.当前项目} 第 {json_data['当前编号']} 张图绘制成功")
                        return self.项目配置['data'][json_data['当前行']]['img_beixuan']
                    else:
                        if self.running:
                            提示 = f'出图错误: {所有图片}'
                            mood._signal.emit((提示, '弹窗', '弹窗'))
                else:  # inserted
                    if 'MJ' in A0_config.config['SD版本'] or 'Flux' in A0_config.config['SD版本'] or 'GPT' in A0_config.config['SD版本'] or (A0_config.name in A0_config.config['SD版本']):
                        所有图片 = SDAPI(self, json_data, mood, 已有数量, 图片路径, w=w, h=h)
                        if 所有图片:
                            return 所有图片
                        if self.running:
                            if '自备' in A0_config.config['SD版本']:
                                mood.排队数量 -= 1
                                print(f'部分图片绘图失败, 等待其他任务完成后重新补绘即可: {所有图片}')
                            else:  # inserted
                                print(f'部分图片绘图失败, 等待其他任务完成后重新补绘即可: {所有图片}')
                    else:  # inserted
                        if '工作流' in A0_config.config['SD版本']:
                            # 种子优先级：角色专用种子 > default_seed > 随机种子
                            工作流种子 = None

                            # 1. 首先检查角色专用种子
                            人物分镜_原始 = json_data.get('人物分镜', '')

                            当前角色 = ''
                            if 人物分镜_原始:
                                # 处理不同的数据类型
                                if isinstance(人物分镜_原始, list):
                                    # 批量绘图：人物分镜是列表
                                    if 人物分镜_原始:
                                        当前角色 = 人物分镜_原始[0]  # 取第一个角色
                                elif isinstance(人物分镜_原始, str):
                                    # 单个绘图：人物分镜是字符串
                                    if ',' in 人物分镜_原始:
                                        当前角色 = 人物分镜_原始.split(',')[0].strip()
                                    else:
                                        当前角色 = 人物分镜_原始.strip()

                            if 当前角色:
                                # 从角色专用种子配置中获取种子值
                                if '角色专用种子配置' in self.项目配置:
                                    角色配置 = self.项目配置['角色专用种子配置'].get(当前角色, {})
                                    if 角色配置.get('enabled', False) and 角色配置.get('seed') is not None:
                                        工作流种子 = 角色配置['seed']
                                        print(f"✅ 工作流使用角色[{当前角色}]的专用种子: {工作流种子}")

                            # 2. 如果没有角色专用种子，检查default_seed
                            if 工作流种子 is None:
                                if hasattr(self, 'default_seed') and self.default_seed is not None and self.default_seed != -1:
                                    工作流种子 = self.default_seed
                                    print(f"工作流使用设置的seed: {工作流种子}")
                                else:
                                    # 3. 最后使用随机种子
                                    工作流种子 = random.randint(1000000000, 9999999999)
                                    print(f"工作流使用随机seed: {工作流种子}")

                            json_data['随机种子'] = 工作流种子
                            # 安全检查项目配置
                            if self.项目配置 and 'data' in self.项目配置 and json_data['当前行'] < len(self.项目配置['data']):
                                # print(self.项目配置['data'][json_data['当前行']]['fenjing'])
                                pass
                            try:
                                图片路径 = Comfyui_t2i(self, mood).获取节点信息(json_data, 图片路径, 已有数量, task='画图', url=url)
                                if 图片路径:
                                    return 图片路径
                            except Exception as comfy_error:
                                print(f"Comfyui_t2i调用出错: {comfy_error}")
                                import traceback
                                traceback.print_exc()
                                raise comfy_error
        except Exception as e:
            print(f"文生图方法出错: {e}")
            import traceback
            traceback.print_exc()
            提示 = f'出图错误: {e}'
            mood._signal.emit((提示, '弹窗', '弹窗'))

    def 图片放大(self, img_file, 放大倍数):
        print(img_file, 放大倍数)

        # 检查是否使用Waifu2x放大
        if A0_config.config.get('放大模式') == 'Waifu2x放大':
            try:
                from pyz.任务运行文件.A32_waifu2x放大 import waifu2x_upscale_single
                print(f"🎨 使用Waifu2x免费在线放大: {img_file}")
                result = waifu2x_upscale_single(img_file, 放大倍数)
                if result:
                    print(f"✅ Waifu2x放大成功: {result}")
                    return result
                else:
                    print("❌ Waifu2x放大失败，回退到本地放大")
                    # 继续执行原有的本地放大逻辑
            except Exception as e:
                print(f"❌ Waifu2x放大异常: {e}，回退到本地放大")
                # 继续执行原有的本地放大逻辑

        # 原有的本地放大逻辑
        try:
            if not jduvudu312us_usjlq.path.exists(img_file):
                return
            image = Image.open(img_file)
            width, height = image.size
            if width >= int(A0_config.config['图片长度']) * 4:
                return
            output = io.BytesIO()
            image.save(output, format='PNG')
            image_bytes = output.getvalue()
            encoded_image = base64.b64encode(image_bytes).decode('utf-8')
            data = {'resize_mode': 0, 'show_extras_results': True, 'gfpgan_visibility': 0, 'codeformer_visibility': 0, 'codeformer_weight': 0, 'upscaling_resize': 放大倍数, 'upscaling_crop': True, 'upscaler_1': A0_config.config['高清修复算法'], 'upscaler_2': 'None', 'extras_upscaler_2_visibility': 0, 'upscale_first': False, 'image': encoded_image}
            headers = {'accept': 'application/json', 'Content-Type': 'application/json'}
            url = A0_config.config['云端地址'].replace('，', ',').replace(' ', '').split(',')[0]
            if url.endswith('/'):
                url = url[:(-1)]
            url = f'{url}/sdapi/v1/extra-single-image'
            response = requests.post(url, headers=headers, json=data, timeout=600)
            if response:
                data = response.json()
                base64_data = data['image']
                image_data = base64.b64decode(base64_data)
                image = Image.open(io.BytesIO(image_data))
                file_path = re.sub('\\((\\d+)\\)\\.png', '(高清).png', img_file)
                image.save(file_path, compress_level=0)
                print(file_path, '放大成功')
                return file_path
        except Exception as e:
            print('放大图片出错:', e)

    def mousePressEvent(self, event):
        if self.childAt(event.x(), event.y()) in [self.角伸缩条]:
            self.evn = 1
            self.mouse_x = event.globalX()
            self.mouse_y = event.globalY()
            self.origin_x = self.x()
            self.origin_y = self.y()
        else:  # inserted
            if self.childAt(event.x(), event.y()) in [self.右伸缩条]:
                self.evn = 2
                self.mouse_x = event.globalX()
                self.mouse_y = event.globalY()
                self.origin_x = self.x()
                self.origin_y = self.y()
            else:  # inserted
                if self.childAt(event.x(), event.y()) in [self.底部伸缩条]:
                    self.evn = 3
                    self.mouse_x = event.globalX()
                    self.mouse_y = event.globalY()
                    self.origin_x = self.x()
                    self.origin_y = self.y()

    def mouseReleaseEvent(self, event):
        self.origin_x = None
        self.origin_y = None
        self.start_x = None
        self.start_y = None

    def mouseMoveEvent(self, event):
        if self.evn == 1:
            try:
                if event.x() < 814:
                    x = 814
                else:  # inserted
                    x = event.x()
                if event.y() < 272:
                    y = 272
                else:  # inserted
                    y = event.y()
                self.setGeometry(self.origin_x, self.origin_y, x, y)
                self.全局主窗口.setGeometry(0, 0, x, y)
            except BaseException as f:
                return None
        if self.evn == 2:
            try:
                if event.x() < 814:
                    x = 814
                else:  # inserted
                    x = event.x()
                self.setGeometry(self.origin_x, self.origin_y, x, self.height())
                self.全局主窗口.setGeometry(0, 0, x, self.全局主窗口.height())
            except BaseException as f:
                return None
        if self.evn == 3:
            try:
                if event.y() < 272:
                    y = 272
                else:  # inserted
                    y = event.y()
                self.setGeometry(self.origin_x, self.origin_y, self.width(), y)
                self.全局主窗口.setGeometry(0, 0, self.全局主窗口.width(), y)
            except BaseException as f:
                return

    def resizeEvent(self, event):
        """窗口大小改变时重新调整表格列宽和背景图片"""
        super().resizeEvent(event)
        if hasattr(self, 'table_widget') and self.table_widget and self.任务模式 == '原创模式':
            # 延迟调用，确保窗口大小已经完全更新
            QTimer.singleShot(50, self.update_table)

        # 重新设置背景图片以适应新的窗口大小
        try:
            if hasattr(self, 'background_label'):
                # 调整背景标签大小
                self.background_label.setGeometry(0, 0, self.width(), self.height())
                # 重新缩放图片
                背景图片路径 = A0_config.config.get('背景图片路径', '')
                if 背景图片路径 and jduvudu312us_usjlq.path.exists(背景图片路径):
                    QTimer.singleShot(100, lambda: self.设置背景图片(背景图片路径))
        except Exception as e:
            pass  # 静默处理背景重设错误

    def closeEvent(self, event):
        reply = QMessageBox.question(self, '确认', '确定要关闭窗口吗？', QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        if reply == QMessageBox.Yes:
            # 清理Chrome进程 - 只清理我们管理的指定端口
            try:
                print("🧹 程序关闭，开始清理管理的Chrome进程...")
                from pyz.任务运行文件.浏览器绘图方案C.interceptor import 停止管理的Chrome进程
                stopped_count = 停止管理的Chrome进程()
                if stopped_count > 0:
                    print(f"✅ 已清理 {stopped_count} 个管理的Chrome进程（端口9222-9261）")
                else:
                    print("ℹ️ 没有需要清理的管理Chrome进程")
            except Exception as e:
                print(f"⚠️ 清理Chrome进程时出错: {e}")
                # 如果精确清理失败，询问是否强制清理所有Chrome
                try:
                    from PyQt5.QtWidgets import QMessageBox
                    reply = QMessageBox.question(
                        self,
                        '清理失败',
                        '精确清理失败，是否强制清理所有Chrome进程？\n（这会关闭所有Chrome窗口）',
                        QMessageBox.Yes | QMessageBox.No,
                        QMessageBox.No
                    )
                    if reply == QMessageBox.Yes:
                        from pyz.任务运行文件.浏览器绘图方案C.interceptor import 强制杀死所有Chrome进程
                        killed_count = 强制杀死所有Chrome进程()
                        print(f"🔥 已强制清理 {killed_count} 个Chrome进程")
                except:
                    pass

            gc.collect()
            event.accept()
        else:  # inserted
            event.ignore()

    def 设置布局(self):
        # 统一的、固定的字体大小
        fixed_font_size = 24

        # 背景图片现在通过Qt原生方式设置，这里只设置默认的粉色渐变背景
        background_style = """
            QMainWindow {
                background: linear-gradient(135deg, #FFB6C1 0%, #FFC0CB 50%, #FFCCCB 100%);
            }
        """

        style_sheet = f"""
            {background_style}
            QDialog {{
                background-color: {A0_config.背景色};
            }}
            QTextEdit, QLineEdit {{
                background-color: {A0_config.背景色};
                color: {A0_config.文字色};
                border: 1px solid {A0_config.边框色};
                padding: 5px;
                font-size: {fixed_font_size - 2}px;
            }}
            QTextEdit:focus, QLineEdit:focus {{
                border: 1px solid {A0_config.选中色};
            }}
            QPushButton, QComboBox {{
                background-color: {A0_config.按钮色};
                color: {A0_config.文字色};
                border: 1px solid {A0_config.边框色};
                padding: 5px 10px;
                font-size: {fixed_font_size}px;
                min-height: {self.缩放(28)}px;
            }}
            QPushButton:hover {{
                background-color: {A0_config.悬停色};
            }}
            QPushButton:pressed {{
                background-color: {A0_config.选中色};
            }}
            QCheckBox {{
                color: {A0_config.文字色};
                font-size: {fixed_font_size - 2}px;
            }}
            QComboBox QAbstractItemView {{
                background-color: {A0_config.背景色};
                color: {A0_config.文字色};
                selection-background-color: {A0_config.选中色};
                font-size: {fixed_font_size}px;
            }}
            QLabel {{
                color: {A0_config.文字色};
                font-size: {fixed_font_size - 2}px;
                background-color: transparent;
            }}
            QGroupBox {{
                color: {A0_config.文字色};
                border: 1px solid {A0_config.边框色};
                margin-top: {self.缩放(10)}px;
                font-size: {self.缩放(16)}px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: {self.缩放(10)}px;
                padding: 0 3px 0 3px;
            }}
            QToolTip {{
                color: #ffffff;
                background-color: #2a82da;
                border: 1px solid white;
            }}
            QScrollBar:vertical {{
                border: none;
                background: {A0_config.背景色};
                width: {self.缩放(10)}px;
                margin: 0px 0px 0px 0px;
            }}
            QScrollBar::handle:vertical {{
                background: {A0_config.边框色};
                min-height: {self.缩放(20)}px;
                border-radius: {self.缩放(5)}px;
            }}
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
                height: 0px;
            }}
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {{
                background: none;
            }}
            QScrollBar:horizontal {{
                border: none;
                background: {A0_config.背景色};
                height: {self.缩放(10)}px;
                margin: 0px 0px 0px 0px;
            }}
            QScrollBar::handle:horizontal {{
                background: {A0_config.边框色};
                min-width: {self.缩放(20)}px;
                border-radius: {self.缩放(5)}px;
            }}
            QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {{
                width: 0px;
            }}
            QScrollBar::add-page:horizontal, QScrollBar::sub-page:horizontal {{
                background: none;
            }}
            QTableWidget {{
                background-color: {A0_config.背景色};
                color: {A0_config.文字色};
                border: 1px solid {A0_config.边框色};
                font-size: {fixed_font_size - 6}px;
                font-family: 'Microsoft YaHei';
            }}
            QTableWidget::item {{
                background-color: {A0_config.背景色};
                color: {A0_config.文字色};
                padding: 5px;
                font-size: {fixed_font_size - 6}px;
                font-family: 'Microsoft YaHei';
            }}
            QTableWidget::item:selected {{
                background-color: {A0_config.选中色};
                color: {A0_config.文字色};
            }}
            QHeaderView::section {{
                background-color: {A0_config.按钮色};
                color: {A0_config.文字色};
                border: 1px solid {A0_config.边框色};
                padding: 5px;
                font-size: {fixed_font_size - 4}px;
                font-family: 'Microsoft YaHei';
                font-weight: bold;
            }}
        """
        return style_sheet

    def 设置背景图片(self, img_path):
        """设置主界面背景图片"""
        try:
            if img_path and jduvudu312us_usjlq.path.exists(img_path):
                # 验证是否为有效的图片文件
                valid_extensions = ('.png', '.jpg', '.jpeg', '.bmp', '.gif', '.webp')
                if img_path.lower().endswith(valid_extensions):
                    # 创建一个背景标签来显示图片
                    from PyQt5.QtWidgets import QLabel
                    from PyQt5.QtGui import QPixmap
                    from PyQt5.QtCore import Qt

                    # 如果已经有背景标签，先删除
                    if hasattr(self, 'background_label'):
                        self.background_label.deleteLater()

                    # 创建新的背景标签
                    self.background_label = QLabel(self)
                    pixmap = QPixmap(img_path)

                    if not pixmap.isNull():
                        # 将图片缩放到窗口大小
                        scaled_pixmap = pixmap.scaled(
                            self.size(),
                            Qt.KeepAspectRatioByExpanding,
                            Qt.SmoothTransformation
                        )

                        self.background_label.setPixmap(scaled_pixmap)
                        self.background_label.setAlignment(Qt.AlignCenter)
                        self.background_label.setGeometry(0, 0, self.width(), self.height())

                        # 将背景标签置于最底层
                        self.background_label.lower()
                        self.background_label.show()

                        # 确保背景标签始终在最底层
                        self.background_label.setAttribute(Qt.WA_StyledBackground, True)
                        self.background_label.setStyleSheet("QLabel { border: none; }")

                        # 确保主内容区域有半透明背景
                        self.设置组件透明度()

                        A0_config.config['背景图片路径'] = img_path
                        # print(f"[DEBUG] Background image set using QLabel: {img_path}")

                        # 保存配置以确保持久化
                        A0_config.修改配置()
                    else:
                        print("[WARNING] Failed to load image file")
                        self.清除背景图片()
                else:
                    print("[WARNING] Invalid image file format")
            else:
                print("[WARNING] Background image file does not exist")
        except Exception as e:
            print(f"[ERROR] Failed to set background image: {e}")
            # 不自动清除背景图片配置，只是不显示
            # self.清除背景图片()

    def 设置组件透明度(self):
        """设置主要组件的透明度，让背景图片显示"""
        try:
            # 为主要内容区域设置半透明背景
            if hasattr(self, '文转视频工作窗口'):
                self.文转视频工作窗口.setObjectName("main_frame")
                self.文转视频工作窗口.setStyleSheet("""
                    QFrame#main_frame {
                        background: rgba(255, 255, 255, 200);
                        border-radius: 10px;
                        margin: 10px;
                    }
                """)
                # 确保内容区域在背景标签之上
                if hasattr(self, 'background_label'):
                    self.文转视频工作窗口.raise_()

            # 设置表格半透明
            if hasattr(self, 'table_widget'):
                self.table_widget.setStyleSheet("""
                    QTableWidget {
                        background: rgba(255, 255, 255, 180);
                        border: 1px solid rgba(150, 150, 150, 200);
                    }
                    QTableWidget::item {
                        background: rgba(255, 255, 255, 100);
                    }
                    QTableWidget::item:selected {
                        background: rgba(100, 150, 255, 150);
                    }
                """)
        except Exception as e:
            print(f"[DEBUG] Failed to set component transparency: {e}")

    def 清除背景图片(self):
        """恢复默认粉色背景"""
        try:
            A0_config.config['背景图片路径'] = ''

            # 删除背景标签
            if hasattr(self, 'background_label'):
                self.background_label.deleteLater()
                delattr(self, 'background_label')

            # 重新应用基础样式，显示默认粉色渐变背景
            self.setStyleSheet(self.设置布局())

            # 重置组件样式
            if hasattr(self, '文转视频工作窗口'):
                self.文转视频工作窗口.setStyleSheet("")
            if hasattr(self, 'table_widget'):
                # 恢复表格的正常样式
                self.table_widget.setStyleSheet("")

            # print("[DEBUG] Restored to default pink background")
        except Exception as e:
            print(f"[ERROR] Failed to restore default background: {e}")

    def 隐藏背景图片(self):
        """只隐藏背景图片显示，不清除配置"""
        try:
            # 删除背景标签
            if hasattr(self, 'background_label'):
                self.background_label.deleteLater()
                delattr(self, 'background_label')

            # 重新应用基础样式，显示默认粉色渐变背景
            self.setStyleSheet(self.设置布局())

            # 重置组件样式
            if hasattr(self, '文转视频工作窗口'):
                self.文转视频工作窗口.setStyleSheet("")
            if hasattr(self, 'table_widget'):
                # 恢复表格的正常样式
                self.table_widget.setStyleSheet("")

            print("[DEBUG] Background image hidden, config preserved")
        except Exception as e:
            print(f"[ERROR] Failed to hide background: {e}")

    def check_and_restore_background(self):
        """定期检查并恢复背景图片"""
        try:
            bg_path = A0_config.config.get('背景图片路径', '')
            if bg_path and jduvudu312us_usjlq.path.exists(bg_path):
                # 检查背景标签是否存在且可见
                if not hasattr(self, 'background_label') or not self.background_label or not self.background_label.isVisible():
                    # print("[DEBUG] Background image missing, restoring...")
                    self.设置背景图片(bg_path)
                else:
                    # 检查背景标签是否在正确位置
                    if self.background_label.geometry() != self.rect():
                        # print("[DEBUG] Background image position incorrect, adjusting...")
                        self.background_label.setGeometry(0, 0, self.width(), self.height())
                        self.background_label.lower()
        except Exception as e:
            # print(f"[DEBUG] Background check failed: {e}")
            pass

    def 检查并恢复背景图片(self):
        """兼容性方法，调用英文方法名"""
        return self.check_and_restore_background()

    def _AI智能角色匹配(self, characters, 原文):
        """
        使用AI进行智能角色匹配，支持上下文理解和代词识别

        Args:
            characters: 提取的角色信息字典
            原文: 完整的原文文本
        """
        import json
        from zhipuai import ZhipuAI

        print(f"[AI智能匹配] 开始使用AI进行角色匹配...")

        # 检查API密钥
        api_key = A0_config.config.get('zhipuai_key', '') or A0_config.zhipuai_key
        if not api_key or api_key == "test_key_for_simulation":
            print(f"[AI智能匹配] 智谱AI密钥无效，回退到传统匹配")
            self._传统角色匹配(characters)
            return

        try:
            client = ZhipuAI(api_key=api_key)

            # 准备角色信息
            角色信息文本 = self._生成角色信息文本(characters)

            # 将原文按行分割
            原文行列表 = 原文.strip().split('\n')

            # 逐行进行AI匹配
            for index, data in enumerate(self.项目配置['data']):
                if index >= len(原文行列表):
                    continue

                当前行 = data['txt']
                当前行索引 = index

                # 构建上下文（当前行前后各2行）
                上下文开始 = max(0, 当前行索引 - 2)
                上下文结束 = min(len(原文行列表), 当前行索引 + 3)
                上下文行列表 = 原文行列表[上下文开始:上下文结束]

                # 标记当前行
                带标记的上下文 = []
                for i, 行 in enumerate(上下文行列表):
                    实际索引 = 上下文开始 + i
                    if 实际索引 == 当前行索引:
                        带标记的上下文.append(f">>> 【当前行】{行}")
                    else:
                        带标记的上下文.append(f"第{实际索引+1}行: {行}")

                上下文文本 = '\n'.join(带标记的上下文)

                # 调用AI进行角色匹配
                匹配结果 = self._AI匹配单行角色(client, 上下文文本, 角色信息文本, 当前行)

                # 过滤匹配结果，只保留已知角色或在fenjing配置中的角色
                有效匹配结果 = []
                for 角色名 in 匹配结果:
                    if 角色名 in characters:
                        有效匹配结果.append(角色名)
                    elif 角色名 in self.项目配置.get('fenjing', {}):
                        # 角色在fenjing配置中，也是有效的
                        有效匹配结果.append(角色名)
                        print(f"[AI智能匹配] 使用已配置角色: {角色名}")
                    else:
                        print(f"[AI智能匹配] 忽略未知角色: {角色名}")

                if 有效匹配结果:
                    data['fenjing'] = ','.join(有效匹配结果)
                    print(f"[AI智能匹配] 第{index+1}行匹配到角色: {data['fenjing']}")
                else:
                    data['fenjing'] = ''
                    print(f"[AI智能匹配] 第{index+1}行未匹配到角色")

        except Exception as e:
            print(f"[AI智能匹配] AI匹配失败: {e}，回退到传统匹配")
            self._传统角色匹配(characters)

    def _AI匹配单行角色(self, client, 上下文文本, 角色信息文本, 当前行):
        """
        使用AI匹配单行的角色

        Args:
            client: ZhipuAI客户端
            上下文文本: 包含当前行及上下文的文本
            角色信息文本: 角色信息描述
            当前行: 当前行文本

        Returns:
            匹配到的角色名列表
        """
        try:
            system_prompt = """你是一个专业的文本分析师，擅长根据上下文识别文本中涉及的角色。

你的任务是：
1. 分析标记为【当前行】的文本涉及哪些角色
2. 特别注意代词指代关系：
   - 单数代词（她、他、它）→ 指向最近提到的对应性别角色
   - 复数代词（她们、他们、它们）→ 指向该性别的多个角色
   - 通用复数（两人、两个人、大家）→ 指向上文提到的多个角色
3. 考虑上下文中最近提到的角色
4. 只返回在当前行中真正涉及的角色
5. 重要：只能返回角色信息中明确列出的角色名，不要创造新角色

返回格式：只返回角色名，用逗号分隔，如：艾米丽,露西
如果没有涉及任何角色，返回：无"""

            user_prompt = f"""请分析以下文本中【当前行】涉及的角色：

=== 角色信息 ===
{角色信息文本}

=== 文本上下文 ===
{上下文文本}

请仔细分析【当前行】涉及哪些角色，特别注意：
1. 直接提到的角色名
2. 代词指代：
   - "她"、"他"、"它" → 指向最近的对应性别角色
   - "她们"、"他们" → 指向该性别的所有相关角色
   - "两人"、"两个人"、"大家" → 指向上文提到的多个角色
3. 别名或称谓（小女孩、那个人等）
4. 根据上下文判断代词的指向

重要提醒：只能返回上述角色信息中明确列出的角色名，不要返回其他角色。

只返回角色名，用逗号分隔："""

            response = client.chat.completions.create(
                model='glm-4-flash-250414',
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                stream=False,
                temperature=0.1,  # 降低温度以获得更一致的结果
                max_tokens=100
            )

            if response and response.choices:
                result = response.choices[0].message.content.strip()

                if result == "无" or not result:
                    return []

                # 解析角色名
                角色列表 = [name.strip() for name in result.split(',') if name.strip()]

                # 过滤无效角色名
                有效角色 = []
                for 角色名 in 角色列表:
                    if 角色名 and 角色名 not in ['无', '叙述者', '作者', '我']:
                        有效角色.append(角色名)

                return 有效角色[:4]  # 最多4个角色

        except Exception as e:
            print(f"[AI匹配单行] 匹配失败: {e}")
            return []

        return []

    def _传统角色匹配(self, characters):
        """
        传统的角色匹配方法（作为AI匹配的备选方案）
        """
        print(f"[传统匹配] 使用传统方法进行角色匹配...")

        for index, data in enumerate(self.项目配置['data']):
            已有分镜 = data['fenjing'].split(',') if data['fenjing'] else []
            已有分镜 = [sentence.strip() for sentence in 已有分镜 if sentence.strip()]

            当前文本 = data['txt']

            # 直接匹配角色名和别名
            for name, info in characters.items():
                if name in ['我', '叙述者', '作者', '全局']:
                    continue

                aliases = info.get('aliases', [])
                别名列表 = [name] + aliases if aliases else [name]

                for 词 in 别名列表:
                    if 词 and isinstance(词, str):
                        if 词 in 当前文本 and name not in 已有分镜 and len(已有分镜) < 4:
                            已有分镜.append(name)
                            break

            data['fenjing'] = ','.join(已有分镜)
            print(f"[传统匹配] 第{index+1}行匹配到角色: {data['fenjing']}")

    def _生成角色信息文本(self, characters):
        """
        生成用于AI分析的角色信息文本
        """
        角色信息列表 = []
        for name, info in characters.items():
            if name in ['我', '叙述者', '作者', '全局']:
                continue

            aliases = info.get('aliases', [])
            gender = info.get('gender', 'unknown')
            age_range = info.get('age_range', '未知')

            角色描述 = f"角色名：{name}"
            if aliases:
                角色描述 += f"，别名：{', '.join(aliases)}"
            角色描述 += f"，性别：{gender}，年龄：{age_range}"

            角色信息列表.append(角色描述)

        return '\n'.join(角色信息列表)

    def _补充缺失角色配置(self):
        """
        检查并补充缺失的角色配置
        为在角色专用种子配置中存在但在fenjing中不存在的角色创建基本配置
        """
        try:
            角色专用种子配置 = self.项目配置.get('角色专用种子配置', {})
            fenjing配置 = self.项目配置.get('fenjing', {})

            缺失角色 = []
            for 角色名 in 角色专用种子配置.keys():
                if 角色名 not in fenjing配置:
                    缺失角色.append(角色名)

            if 缺失角色:
                print(f"[补充配置] 发现缺失的角色配置: {', '.join(缺失角色)}")

                for 角色名 in 缺失角色:
                    # 🛡️ 修复：安全创建基本的fenjing配置，区分中英文内容
                    import re
                    包含中文 = bool(re.search(r'[\u4e00-\u9fff]', 角色名))

                    if 包含中文:
                        # 中文角色名只保存到中文字段，英文字段留空等待翻译
                        self.项目配置['fenjing'][角色名] = {
                            '性别': '',
                            '称呼': 角色名,
                            'guanjianci': '',  # 英文字段留空
                            'guanjianci_zh': 角色名,  # 中文保存到中文字段
                            'zhongzi': '',
                            'img': '',
                            'url': '',
                            '备选图片': []
                        }
                        print(f"[补充配置] 已为中文角色[{角色名}]创建基本配置，guanjianci字段留空等待翻译")
                    else:
                        # 英文角色名可以保存到英文字段
                        self.项目配置['fenjing'][角色名] = {
                            '性别': '',
                            '称呼': 角色名,
                            'guanjianci': 角色名,
                            'guanjianci_zh': 角色名,
                            'zhongzi': '',
                            'img': '',
                            'url': '',
                            '备选图片': []
                        }
                        print(f"[补充配置] 已为英文角色[{角色名}]创建基本配置")

                # 保存配置
                self.保存配置()
                print(f"[补充配置] 已保存{len(缺失角色)}个角色的配置")
            else:
                print(f"[补充配置] 所有角色配置完整，无需补充")

        except Exception as e:
            print(f"[补充配置] 补充角色配置时出错: {e}")

    def 初始化背景图片(self):
        """程序启动时初始化背景图片"""
        try:
            # 首先检查配置中的背景图片路径
            bg_path = A0_config.config.get('背景图片路径', '')
            # print(f"[DEBUG] Initializing background image: '{bg_path}'")

            # 如果配置中没有路径或路径无效，尝试使用默认背景图片
            if not bg_path or not jduvudu312us_usjlq.path.exists(bg_path):
                default_bg = A0_config.获取默认背景图片()
                if default_bg:
                    bg_path = default_bg
                    A0_config.config['背景图片路径'] = bg_path
                    # print(f"[DEBUG] Using default background: {bg_path}")

            if bg_path and jduvudu312us_usjlq.path.exists(bg_path):
                # 验证图片文件有效性
                valid_extensions = ('.png', '.jpg', '.jpeg', '.bmp', '.gif', '.webp')
                if bg_path.lower().endswith(valid_extensions):
                    # print(f"[DEBUG] Setting background image: {bg_path}")
                    self.设置背景图片(bg_path)
                else:
                    # 只清除无效格式的配置
                    # print("[DEBUG] Invalid background image format, clearing...")
                    A0_config.config['背景图片路径'] = ''
            else:
                # print(f"[DEBUG] Background image not found: {bg_path}")
                # 不清除配置，保留用户设置，文件可能稍后可用
                pass
        except Exception as e:
            # print(f"[DEBUG] Background initialization failed: {e}")
            pass
            # 不清除配置中的路径，保留用户设置
            # A0_config.config['背景图片路径'] = ''

import ctypes

def hide_console():
    whnd = ctypes.windll.kernel32.GetConsoleWindow()
    if whnd!= 0:
        ctypes.windll.user32.ShowWindow(whnd, 0)

def 启动程序(端口, width, height, 文字大小):
    try:
        hide_console()
        # 检查是否已存在QApplication实例
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)

        translator = QTranslator()
        if jduvudu312us_usjlq.path.exists('./qt_zh_CN.qm'):
            translator.load('./qt_zh_CN.qm')
            app.installTranslator(translator)

        font = QFont()
        比例 = height / 1080
        font.setPointSize(int(A0_config.config['字体大小']))
        font.setFamily('Microsoft YaHei')
        app.setFont(font)

        window = MainWindow(端口, width, height, 文字大小)

        # 设置图标
        icon_path = A0_config.ico if hasattr(A0_config, 'ico') else 'favicon.ico'
        if jduvudu312us_usjlq.path.isfile(icon_path):
            icon = QIcon(icon_path)
            pixmap = icon.pixmap(int(比例 * 32), int(比例 * 32))
            window.setWindowIcon(QIcon(pixmap))

        # 自动加载第一个可用项目
        try:
            if window.重绘列表.count() > 0:
                window.重绘列表.setCurrentIndex(0)
                window.当前项目 = window.重绘列表.currentText()
                if window.当前项目:
                    print(f"自动加载项目: {window.当前项目}")
                    # 延迟加载，确保界面已完全初始化
                    QTimer.singleShot(500, lambda: window.加载历史任务())
        except Exception as e:
            print(f"自动加载项目失败: {e}")

        # 自动加载第一个可用项目
        try:
            if window.重绘列表.count() > 0:
                window.重绘列表.setCurrentIndex(0)
                window.当前项目 = window.重绘列表.currentText()
                if window.当前项目:
                    print(f"自动加载项目: {window.当前项目}")
                    # 立即加载项目数据
                    window.加载历史任务()
        except Exception as e:
            print(f"自动加载项目失败: {e}")

        # 初始化背景图片已在构造函数中通过QTimer调用，无需重复调用

        # 使用原版的完整尺寸设置
        window.resize(width, height)
        window.showMaximized()

        # 不在这里调用app.exec_()，避免事件循环冲突
        return window
    except Exception as e:
        print('主线任务出错:', e)
        import traceback
        traceback.print_exc()
        return None

    # 其他方法根据实际需要补充...