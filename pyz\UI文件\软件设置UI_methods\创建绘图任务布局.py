# 创建绘图任务布局方法
# 从软件设置UI.py拆分出来的方法

from PyQt5.QtWidgets import QGroupBox, QHBoxLayout, QSizePolicy, QGridLayout
from PyQt5 import QtCore

def 创建绘图任务布局(self):
    self.批量绘图组 = QGroupBox('绘图设置')
    self.更换模型布局 = QHBoxLayout()
    self.更换模型布局.addWidget(self.更换SD模型_label)
    self.更换模型布局.addWidget(self.更换SD模型)
    self.更换SD模型.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

    # 多GPU加速布局
    self.多GPU布局 = QHBoxLayout()
    self.多GPU布局.addWidget(self.启用多GPU加速)

    # Pollinations云端模型布局
    self.Pollinations布局 = QHBoxLayout()
    self.Pollinations布局.addWidget(self.使用Pollinations)

    self.Pollinations模型布局 = QHBoxLayout()
    self.Pollinations模型布局.addWidget(self.Pollinations模型_label)
    self.Pollinations模型布局.addWidget(self.Pollinations模型)
    self.Pollinations模型布局.addWidget(self.Pollinations管理)
    self.Pollinations模型.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

    # Pollinations API密钥布局
    self.Pollinations密钥布局 = QHBoxLayout()
    self.Pollinations密钥布局.addWidget(self.Pollinations密钥_label)
    self.Pollinations密钥布局.addWidget(self.Pollinations密钥)
    self.Pollinations密钥布局.addWidget(self.Pollinations密钥显示)
    self.Pollinations密钥.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

    # Pollinations风格布局
    self.Pollinations风格布局 = QHBoxLayout()
    self.Pollinations风格布局.addWidget(self.Pollinations风格_label)
    self.Pollinations风格布局.addWidget(self.Pollinations风格)
    self.Pollinations风格.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

    # 🎯 Pollinations风格设置布局（直接集成）
    self.Pollinations正面风格布局 = QHBoxLayout()
    self.Pollinations正面风格布局.addWidget(self.正面风格标题)
    self.Pollinations正面风格布局.addWidget(self.正面风格提示词)

    self.Pollinations反面风格布局 = QHBoxLayout()
    self.Pollinations反面风格布局.addWidget(self.反面风格标题)
    self.Pollinations反面风格布局.addWidget(self.反面风格提示词)

    self.Pollinations风格按钮布局 = QHBoxLayout()
    self.Pollinations风格按钮布局.addWidget(self.保存风格设置)
    self.Pollinations风格按钮布局.addWidget(self.重置风格设置)
    self.Pollinations风格按钮布局.addStretch()
    self.更换视频布局 = QHBoxLayout()
    self.更换视频布局.addWidget(self.动画模式_label)
    self.更换视频布局.addWidget(self.动画模式)
    self.更换视频布局.addWidget(self.万相加速)
    self.动画模式.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
    self.更换风格布局 = QHBoxLayout()
    self.更换风格布局.addWidget(self.图片风格_label)
    self.更换风格布局.addWidget(self.图片风格)
    self.图片风格.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
    self.更换VAE布局 = QHBoxLayout()
    self.更换VAE布局.addWidget(self.更换VAE模型_label)
    self.更换VAE布局.addWidget(self.更换VAE模型)
    self.更换VAE模型.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
    self.刷新布局 = QHBoxLayout()
    self.刷新布局.addWidget(self.使用云端)
    self.刷新布局.addWidget(self.云端地址)
    self.刷新布局.addWidget(self.刷新SD配置)
    self.剪映布局 = QHBoxLayout()
    self.剪映布局.addWidget(self.剪映草稿文件夹_label)
    self.剪映布局.addWidget(self.剪映草稿文件夹)
    self.剪映布局.addWidget(self.保存参数)
    self.镜像数量布局 = QHBoxLayout()
    self.镜像数量布局.addWidget(self.镜像数量_label)
    self.镜像数量布局.addWidget(self.镜像数量)
    self.镜像数量布局.addWidget(self.强制销毁)
    self.采样布局 = QHBoxLayout()
    self.采样布局.addWidget(self.图片采样方法_label)
    self.采样布局.addWidget(self.图片采样方法)
    self.图片采样方法_label.setSizePolicy(QSizePolicy.Minimum, QSizePolicy.Preferred)
    self.图片采样方法.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
    self.调度布局 = QHBoxLayout()
    self.调度布局.addWidget(self.调度器_label)
    self.调度布局.addWidget(self.调度器)
    self.调度器_label.setSizePolicy(QSizePolicy.Minimum, QSizePolicy.Preferred)
    self.调度器.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
    self.放大模型布局 = QHBoxLayout()
    self.放大模型布局.addWidget(self.高清修复算法_label)
    self.放大模型布局.addWidget(self.高清修复算法)
    self.高清修复算法_label.setSizePolicy(QSizePolicy.Minimum, QSizePolicy.Preferred)
    self.高清修复算法.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
    self.放大模式布局 = QHBoxLayout()
    self.放大模式布局.addWidget(self.放大模式_label)
    self.放大模式布局.addWidget(self.放大模式)
    self.放大模式_label.setSizePolicy(QSizePolicy.Minimum, QSizePolicy.Preferred)
    self.放大模式.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
    self.重绘幅度布局 = QHBoxLayout()
    self.重绘幅度布局.addWidget(self.修复重绘幅度_label)
    self.重绘幅度布局.addLayout(self.修复重绘幅度条)
    self.放大倍数布局 = QHBoxLayout()
    self.放大倍数布局.addWidget(self.修复放大倍数_label)
    self.放大倍数布局.addLayout(self.修复放大倍数条)
    self.采样步数布局 = QHBoxLayout()
    self.采样步数布局.addWidget(self.图片采样步数_label)
    self.采样步数布局.addLayout(self.图片采样步数条)
    self.图片数量布局 = QHBoxLayout()
    self.图片数量布局.addWidget(self.每张图片数量_label)
    self.图片数量布局.addLayout(self.每张图片数量条)
    self.相关性布局 = QHBoxLayout()
    self.相关性布局.addWidget(self.提示词相关性_label)
    self.相关性布局.addLayout(self.提示词相关性条)
    图片长度布局 = QHBoxLayout()
    图片长度布局.setContentsMargins(0, 0, 0, 0)
    图片长度布局.addWidget(self.图片长度_label)
    图片长度布局.addWidget(self.图片长度)
    图片长度布局.addWidget(self.图片宽度_label)
    图片长度布局.addWidget(self.图片宽度)
    风格强度布局 = QHBoxLayout()
    风格强度布局.setContentsMargins(0, 0, 0, 0)
    风格强度布局.addWidget(self.风格强度_label)
    风格强度布局.addWidget(self.风格强度)
    风格强度布局.addWidget(self.Lora强度_label)
    风格强度布局.addWidget(self.Lora强度)
    正提示词布局 = QHBoxLayout()
    正提示词布局.addWidget(self.正提示词_label)
    正提示词布局.addWidget(self.正提示词)
    反提示词布局 = QHBoxLayout()
    反提示词布局.addWidget(self.反提示词_label)
    反提示词布局.addWidget(self.反提示词)

    # 工作流选择布局
    self.工作流选择布局 = QHBoxLayout()
    self.工作流选择布局.setContentsMargins(0, 0, 0, 0)
    self.工作流选择布局.addWidget(self.工作流选择_label)
    self.工作流选择布局.addWidget(self.工作流选择)

    布局组 = [self.更换模型布局, self.多GPU布局, self.Pollinations布局, self.Pollinations模型布局, self.Pollinations密钥布局, self.工作流选择布局, self.更换视频布局, self.更换风格布局, self.修复布局, self.浏览器绘图方案布局, self.浏览器绘图选项布局, self.更换VAE布局, self.采样布局, self.放大模型布局, self.放大模式布局, self.刷新布局, self.剪映布局, self.相关性布局, self.重绘幅度布局, self.重绘幅度布局, self.放大倍数布局, self.采样步数布局, self.图片数量布局, 图片长度布局, 风格强度布局, self.调度布局, 正提示词布局, 反提示词布局, self.镜像数量布局, self.Pollinations风格按钮布局]
    for 布局 in 布局组:
        布局.setContentsMargins(self.缩放(5), self.缩放(3), self.缩放(5), self.缩放(3))
        布局.setSpacing(self.缩放(10))
        for i in range(布局.count()):
            widget = 布局.itemAt(i).widget()
            if widget is not None:
                widget.setFixedHeight(self.行高)
    self.绘图布局 = QGridLayout(self.批量绘图组)
    self.绘图布局.addLayout(self.修复布局, 0, 0)
    self.绘图布局.addLayout(self.浏览器绘图方案布局, 1, 0)  # 添加浏览器绘图方案选择
    self.绘图布局.addLayout(self.浏览器绘图选项布局, 2, 0)  # 添加浏览器绘图选项设置
    self.绘图布局.addLayout(self.更换模型布局, 3, 0)
    self.绘图布局.addLayout(self.多GPU布局, 4, 0)
    self.绘图布局.addLayout(self.Pollinations布局, 5, 0)
    self.绘图布局.addLayout(self.Pollinations模型布局, 6, 0)
    self.绘图布局.addLayout(self.Pollinations密钥布局, 7, 0)
    self.绘图布局.addLayout(self.Pollinations风格布局, 8, 0)
    # 🎯 添加Pollinations风格设置布局
    self.绘图布局.addLayout(self.Pollinations正面风格布局, 9, 0)
    self.绘图布局.addLayout(self.Pollinations反面风格布局, 10, 0)
    self.绘图布局.addLayout(self.Pollinations风格按钮布局, 11, 0)
    self.绘图布局.addLayout(self.工作流选择布局, 12, 0)
    self.绘图布局.addLayout(self.更换VAE布局, 13, 0)
    self.绘图布局.addLayout(self.采样布局, 14, 0)
    self.绘图布局.addLayout(self.放大模式布局, 15, 0)
    self.绘图布局.addLayout(self.放大模型布局, 16, 0)
    self.绘图布局.addLayout(正提示词布局, 17, 0)
    self.绘图布局.addLayout(self.刷新布局, 0, 1)
    self.绘图布局.addLayout(self.更换视频布局, 1, 1)
    self.绘图布局.addLayout(self.更换风格布局, 2, 1)
    self.绘图布局.addLayout(self.调度布局, 3, 1)
    self.绘图布局.addLayout(风格强度布局, 4, 1)
    self.绘图布局.addLayout(图片长度布局, 5, 1)
    self.绘图布局.addLayout(反提示词布局, 7, 1)
    self.绘图布局.addLayout(self.镜像数量布局, 0, 2)
    self.绘图布局.addLayout(self.剪映布局, 1, 2)
    self.绘图布局.addLayout(self.放大倍数布局, 2, 2)
    self.绘图布局.addLayout(self.重绘幅度布局, 3, 2)
    self.绘图布局.addLayout(self.图片数量布局, 4, 2)
    self.绘图布局.addLayout(self.相关性布局, 5, 2)
    self.绘图布局.addLayout(self.采样步数布局, 6, 2)
