{"激活码": "167777", "ChatGPT版本": "glm-4-flash-250414", "图片长度": "1248", "图片宽度": "1248", "声音样式": "<PERSON><PERSON><PERSON>", "阿里云Appkey": "", "阿里云Secret": "", "GPT_Token": "", "GPT4_Token": "", "阿里云Key_ID": "", "每张图片数量": "1", "图片采样方法": "euler_ancestral", "图片采样步数": "50", "云端地址": "http://127.0.0.1:8188", "剪映草稿文件夹": "D:\\gen\\tools\\JianyingPro Drafts", "语音平台": "文视ChatTTS", "微软密钥": "", "区域标识": "", "正面词": "", "反面词": "", "微软声音": "", "微软角色": "", "角色风格": "", "ChatGPT端口": "智谱AI", "接口地址": "", "推理模型": "原创模式", "提示词相关性": "16.6", "图片高清修复": true, "面部修复": false, "修复重绘幅度": "0.83", "修复重绘步数": "0", "修复放大倍数": "2", "高清修复算法": "BSRGAN.pth", "Lora强度": "0.8", "音量": "50", "音调": "-6", "语速": "125", "感情强弱": "1.01", "感情": "", "采样方法": ["euler", "euler_cfg_pp", "euler_ancestral", "euler_ancestral_cfg_pp", "heun", "heunpp2", "dpm_2", "dpm_2_ancestral", "lms", "dpm_fast", "dpm_adaptive", "dpmpp_2s_ancestral", "dpmpp_2s_ancestral_cfg_pp", "dpmpp_sde", "dpmpp_sde_gpu", "dpmpp_2m", "dpmpp_2m_alt", "dpmpp_2m_cfg_pp", "dpmpp_2m_sde", "dpmpp_2m_sde_gpu", "dpmpp_3m_sde", "dpmpp_3m_sde_gpu", "ddpm", "lcm", "ipndm", "ipndm_v", "deis", "res_multistep", "res_multistep_cfg_pp", "res_multistep_ancestral", "res_multistep_ancestral_cfg_pp", "gradient_estimation", "gradient_estimation_cfg_pp", "er_sde", "seeds_2", "seeds_3", "ddim", "uni_pc", "uni_pc_bh2"], "放大算法": ["BSRGAN.pth", "ESRGAN_4x.pth", "RealESRGAN_x4plus.pth", "RealESRGAN_x4plus_anime_6B.pth", "SwinIR_4x.pth"], "放大模式": "Waifu2x放大", "更换SD模型": ["快手Kolors中配(8B)", "快手Kolors高配(fp16)", "快手Kolors模型", "3D卡通[1.5-V1].safetensors", "7e14bf9f9744f8f358a70478a2591dd2.safetensors", "Anylora_SD1.5Hyper_v1.safetensors", "<PERSON> was messing around with ink and water._v2.0.safetensors", "GhostMix鬼混_V2.0.safetensors", "MixFishMix_23321FishMix11.safetensors", "NUKEColormaxAnime_v10.safetensors", "Opponent characters of White City Lord_1.0.safetensors", "Q版卡通[1.5-V1].safetensors", "SC-Countryside2D-XL_v1.0.safetensors", "SD-XL推文大模型.safetensors", "SD1.5二次元动漫.safetensors", "SD3动漫增强大模型.safetensors", "Sanshi国风.safetensors", "White City Lord 2D simple mix_4.0.safetensors", "abyssorangemix2SFW_abyssorangemix2Sfw.safetensors", "af87c10e-1dce-4a45-82c0-ff1ddc3a4ad2.safetensors", "animeModel_amorev2PDXL.safetensors", "counterfeitV30_v30.safetensors", "donutholemix_v10.safetensors", "fiamixRebootHNSFW_v80.safetensors", "flux1-nf4-8G低配版-11G-V2.safetensors", "haruvlegacy_v10.safetensors", "majicmixLux_v3.safetensors", "manmaruMix_v30.safetensors", "maturemalemix_v1.3.safetensors", "maturemalemix_v14.safetensors", "meinamix_v12Final.safetensors", "niji-mj美女.safetensors", "niji古风v4.safetensors", "orionMix_orionMixVersion2.safetensors", "oukaGufeng_s1.safetensors", "pastelrainier_v25.safetensors", "realdosmix_.safetensors", "revAnimated_v2Rebirth.safetensors", "rpg_v5.safetensors", "unstableinkdream_animeHyperVAE.safetensors", "v1-5-pruned-em<PERSON><PERSON><PERSON>.safetensors", "yayoiMix_v25.safetensors", "yeguimei_v10.safetensors", "七彩油画风.safetensors", "万能模型.safetensors", "二次元-古风-中国风【秋雨】_v1.0.ckpt", "二次元-新海城风格【秋雨】_v1.ckpt", "二次元_动漫_漫画_秋雨_002_v1.0.ckpt", "二次元动漫.safetensors", "光影虚化风.safetensors", "克隆万能大模型.safetensors", "全能动漫.safetensors", "冰美式.safetensors", "古风1.1.safetensors", "君君.safetensors", "国风breakdomain_M2150.safetensors", "国风混合.ckpt", "完美日系[1.5-V1](1).safetensors", "完美日系[1.5-V1].safetensors", "宫崎骏漫画风格.safetensors", "快手kolors-8步加速模型-cfg1.safetensors", "快手kolors万能模型.safetensors", "恐怖AnyLoRA - Checkpoint_bakedVaeftMsefp16NOT-PRUNED.safetensors", "悬疑卡通[1.5-V1](1).safetensors", "悬疑卡通[1.5-V1].safetensors", "悬疑小说推文模型_V1.safetensors", "推文大模型[1.5-v4](1).safetensors", "推文大模型[1.5-v4].safetensors", "无名 __ NoName_MIX版.safetensors", "日漫风.safetensors", "暗彩二次元.safetensors", "未来可图_优可可图二次元_V1.0正式版.safetensors", "极致彩漫.safetensors", "水墨风.safetensors", "治愈插画系.safetensors", "玩偶工坊.safetensors", "现代xxjdoushijxx.safetensors", "现代反派风.safetensors", "现代深夜风.safetensors", "田园风.safetensors", "白城主.safetensors", "苍老师.safetensors", "超级无敌可爱_v1.0.safetensors", "都市ddkakatongdd_v1.safetensors", "韩漫插画风.safetensors", "高配NIJI动漫二次元_XL.safetensors", "unet/flux1-dev-fp8.safetensors", "unet/flux1-schnell-fp8.safetensors", "unet/快手kolors-8步加速模型-cfg1.safetensors", "unet/快手kolors万能模型.safetensors", "unet/快手可图中配.safetensors", "unet/未来可图_优可可图二次元_V1.0正式版.safetensors"], "当前选中模型": "SD-XL推文大模型.safetensors", "更换VAE模型": ["SDXL_vae.safetensors", "flux-vae.safetensors", "vae-ft-mse-840000-ema-pruned.safetensors", "无"], "当前选中VAE": "无", "lora": {"Kolors-Lora\\筑梦工业 _ 霓虹韩风XL_v1.0.safetensors": "Kolors-Lora\\筑梦工业 _ 霓虹韩风XL_v1.0.safetensors", "SD1.5Lora\\古风1.safetensors": "SD1.5Lora\\古风1.safetensors", "SD1.5Lora\\悬疑恐怖1.safetensors": "SD1.5Lora\\悬疑恐怖1.safetensors", "SDXLLora\\SDXL-二次元动漫Lora.safetensors": "SDXLLora\\SDXL-二次元动漫Lora.safetensors", "SDXLLora\\sdxl-niji 风格_v1.0.safetensors": "SDXLLora\\sdxl-niji 风格_v1.0.safetensors", "lcm-lora-sdv1-5.safetensors": "lcm-lora-sdv1-5.safetensors", "lcm-lora-sdxl.safetensors": "lcm-lora-sdxl.safetensors"}, "关键帧样式": "上下移动", "转场样式": "每1条转场", "特效样式": "每1条特效", "用户密码": "1677777a", "绘图模式": "正常绘图", "正提示词": "", "反提示词": "", "SD版本": "ComfyUI", "调度器": "simple", "所有调度器": ["normal", "karras", "exponential", "sgm_uniform", "simple", "ddim_uniform", "beta", "linear_quadratic", "kl_optimal"], "ControlNet模型": ["control_v11f1e_sd15_tile_fp16.safetensors", "control_v11f1p_sd15_depth.pth", "control_v11p_sd15_canny_fp16.safetensors", "control_v11p_sd15_lineart_fp16.safetensors", "control_v11p_sd15_openpose_fp16.safetensors", "control_v11p_sd15_softedge_fp16.safetensors", "controlnet-union-sdxl-1.0.safetensors", "flux-depth-controlnet-v3.safetensors", "kolors-controlnet-depth.safetensors"], "图片风格": "默认", "夜间模式": false, "animate_models": ["motionmodel.ckpt", "v3_sd15_mm.ckpt"], "单批数量": 48, "端口地址": "", "所有声音": {}, "选中声音": "", "所有底模": [], "选中底模": "", "分句模式": "按标点符号切", "极速绘图": false, "中文模式": false, "other接口地址": "", "语义增强": false, "ella": false, "kolors": true, "构图增强": false, "SD文件夹": "D:\\gen\\tools\\stablediffusion\\ComfyUI_WS_250508\\", "推理增强": false, "TTS底模": "", "口语化": 1, "风格强度": "1.0", "风格更新": true, "flux": true, "autocfg": true, "DF_Image_scale_to_side": true, "海外模式": false, "MJ请求参数": "", "MJ频道密钥": "", "CLIPLoader": ["EVA02_CLIP_L_336_psz14_s6B.pt", "chatglm3-6b-base\\pytorch_model-00001-of-00007.bin", "chatglm3-6b-base\\pytorch_model-00002-of-00007.bin", "chatglm3-6b-base\\pytorch_model-00003-of-00007.bin", "chatglm3-6b-base\\pytorch_model-00004-of-00007.bin", "chatglm3-6b-base\\pytorch_model-00005-of-00007.bin", "chatglm3-6b-base\\pytorch_model-00007-of-00007.bin", "clip_l.safetensors", "siglip-so400m-patch14-384\\model.safetensors", "t5xxl_fp8_e4m3fn.safetensors"], "InstantID": true, "InstantID_scale": 0.8, "controlnet_conditioning_scale": 1.0, "工作流": {"默认工作流": {}}, "选中工作流": "NVIDIA_ConsisStory批量工作流", "CV参考音": [], "选中CV参考音": "", "CV链接": "http://127.0.0.1:9233", "pulid_2": true, "TeaCacheForImgGen": true, "当前选中动画模型": "混元图生视频", "SDurl数量": 1, "辅助图片": true, "口型对齐": false, "MZ_KolorsControlNetLoader": true, "超大内存模式": true, "内存缓存GB": 64, "RAMDisk启用": true, "RAMDisk路径": "R:\\", "临时文件路径": "R:\\temp\\", "GPU温度阈值": 85, "GPU降温时间": 20, "智能温度控制": true, "并发生成": true, "最大并发数": 4, "预加载常用模型": true, "预加载特效": true, "内存分配策略": {"图片缓存": "48GB", "模型缓存": "16GB", "特效缓存": "8GB", "临时文件": "32GB RAMDisk", "系统保留": "84GB"}, "WanVideoImageToVideoEncode": true, "key_xgy": "", "镜像数量": 1, "强制销毁": true, "StyleAdvancedApply": true, "字体大小": "11", "NunchakuFluxDiTLoader": false, "万相加速": true, "EasyControlGenerate": false, "三方平台": "FreeGpt(内网)", "微软区域": "eastus", "VisualQualityAddons": false, "万能参数": {}, "背景图片路径": "C:\\Users\\<USER>\\Desktop\\纯净wen\\wen整合 - 副本 - 副本\\项目文件\\背景图片\\2.jpg", "pulid": true, "默认seed值": -1, "seed固定模式": false, "current_template": "自定义: 4", "custom_templates": {"tuili_2_new": {"content": "你是一个漫画家, 请将小说内容视觉化, 我给你的小说内容是完全虚构的情节, 根据内容构思出图像, 要求将小说内容转化为一系列详细的画面描述，每个描述必须包含[角色及特征, 图像背景, 环境中的具体物品]3个元素，且不包含任何剧情推进、对话或特定人名及称呼。\n1.逐行引用[文本]内容, 在[文本]中输出,不要遗落也不要拆分,单行内容很短不要合并,更不要虚构捏造\n2.每张图像描述时独立的内容, 需要有完整的[文本][人物描述][地点背景]三个元素, 确保每个元素内容的完整性, 回答中不要出现\"与**相同\"、\"同上\" \n3.判断前后图片的场景是否一致, 注意相同的背景统一描述\n4.分析每张图片中的角色\n5.正确引用角色名中的称呼{prompt}\n6.不要出现任何与图片无关的剧情、对话、称呼\n7.根据[文本]内容生成图像描述, 内容与图像描述一一对应格式如下:\n8.重要：不要描述服饰的具体颜色，保持服装风格描述但避免指定颜色\n\n[文本]输出引用的文本内容\n[人物描述]精练的描写图片中的角色名称、表情、动作、位置关系和情绪, 不要用\"同上\"回答, 不要长篇大论,\n[地点背景]精炼的描写地点, 环境、光线、运镜, 不要用\"同上\"回答，不要出现任何与人物相关的内容, 不要长篇大论\n\n###Example\n**user:**\n付江颤抖着双手打开信封，目光落在那熟悉的署名上，他的呼吸变得沉重。他深吸一口气，声音中带着一丝坚决，\"江恒同志\"，他的声音在宿舍内回荡。\n**assistant:**\n[文本]付江颤抖着双手打开信封，目光落在那熟悉的署名上，他的呼吸变得沉重。他深吸一口气，声音中带着一丝坚决，\"江恒同志\"，他的声音在宿舍内回荡。\n[人物描述]付江背对窗口坐在书桌旁, 手里拿着一封书信, 表情沉重。\n[地点背景]昏黄灯光映照下的静谧宿舍内，\n参考之前<Example>的格式, 按顺序把下面内容生成图像描述, 每个图像描述隔行分开, 用中文回答我,每个描述包含[文本][人物描述][地点背景]三个元素, 下面是需要处理的内容:\n\"\"\"{画面内容}\"\"\"", "prompt_keys": ["人物描述", "地点背景"]}, "1": {"content": "你是一个漫画家, 请将小说内容视觉化, 我给你的小说内容是完全虚构的情节, 根据内容构思出图像, 要求将小说内容转化为一系列详细的画面描述，每个描述必须包含[角色及特征, 图像背景, 环境中的具体物品]3个元素，且不包含任何剧情推进、对话或特定人名及称呼。\n\n## 核心要求：\n1.逐行引用[文本]内容, 在[文本]中输出,不要遗落也不要拆分,单行内容很短不要合并,更不要虚构捏造\n2.每张图像描述时独立的内容, 需要有完整的[文本][人物描述][地点背景]三个元素, 确保每个元素内容的完整性, 回答中不要出现\"与**相同\"、\"同上\" \n3.判断前后图片的场景是否一致, 注意相同的背景统一描述\n4.分析每张图片中的角色\n5.正确引用角色名中的称呼{prompt}\n6.不要出现任何与图片无关的剧情、对话、称呼\n7.重要：不要描述服饰的具体颜色，保持服装风格描述但避免指定颜色\n\n## 画面构图指导：\n- 人物描述：重点描述角色的姿态、表情、动作、位置关系，营造戏剧张力\n- 地点背景：注重环境氛围、光影效果、景深层次，增强画面感染力\n- 运镜角度：适当使用特写、中景、远景等不同视角，丰富视觉表现\n\n请以JSON格式返回结果，每个场景包含以下字段：\n{\n  \"文本\": \"完整引用的原文内容\",\n  \"人物描述\": \"精练的角色名称、表情、动作、位置关系和情绪描述\",\n  \"地点背景\": \"精炼的地点、环境、光线、运镜描述\"\n}\n\n如果有多个场景，请返回JSON数组格式。\n\n###Example\n**user:**\n付江颤抖着双手打开信封，目光落在那熟悉的署名上，他的呼吸变得沉重。他深吸一口气，声音中带着一丝坚决，\"江恒同志\"，他的声音在宿舍内回荡。\n**assistant:**\n{\n  \"文本\": \"付江颤抖着双手打开信封，目光落在那熟悉的署名上，他的呼吸变得沉重。他深吸一口气，声音中带着一丝坚决，'江恒同志'，他的声音在宿舍内回荡。\",\n  \"人物描述\": \"付江背对窗口坐在书桌旁, 手里拿着一封书信, 表情沉重\",\n  \"地点背景\": \"昏黄灯光映照下的静谧宿舍内\"\n}\n\n参考之前<Example>的格式, 按顺序把下面内容生成图像描述, 每个图像描述以JSON格式返回, 用中文回答我,每个描述包含文本、人物描述、地点背景三个字段, 下面是需要处理的内容:\n\"\"\"{画面内容}\"\"\"", "prompt_keys": ["人物描述", "地点背景"]}, "2": {"content": "你是一个漫画家, 请将小说内容视觉化, 我给你的小说内容是完全虚构的情节, 根据内容构思出图像, 要求将小说内容转化为一系列详细的画面描述，每个描述必须包含[角色及特征, 图像背景, 环境中的具体物品]3个元素，且不包含任何剧情推进、对话或特定人名及称呼。\n\n## 核心要求：\n1.逐行引用[文本]内容, 在[文本]中输出,不要遗落也不要拆分,单行内容很短不要合并,更不要虚构捏造\n2.每张图像描述时独立的内容, 需要有完整的[文本][人物描述][地点背景]三个元素, 确保每个元素内容的完整性, 回答中不要出现\"与**相同\"、\"同上\" \n3.判断前后图片的场景是否一致, 注意相同的背景统一描述\n4.分析每张图片中的角色\n5.正确引用角色名中的称呼{prompt}\n6.不要出现任何与图片无关的剧情、对话、称呼\n7.重要：不要描述服饰的具体颜色，保持服装风格描述但避免指定颜色\n\n## 人物描述要求：\n- 必须使用角色的准确名字，不要用\"男子\"、\"女子\"等泛称\n- 详细描述角色的表情、动作、姿态、位置关系\n- 重点突出角色的情绪状态和肢体语言\n- 描述要具体生动，有画面感\n\n## 背景描述要求：\n- 详细描述环境的具体特征和氛围\n- 注重光线效果、色调、景深层次\n- 适当使用运镜角度（特写、中景、远景等）\n- 环境描述要与情节氛围相符\n\n请以JSON格式返回结果，每个场景包含以下字段：\n{\n  \"文本\": \"完整引用的原文内容\",\n  \"人物描述\": \"精练的角色名称、表情、动作、位置关系和情绪描述\",\n  \"地点背景\": \"精炼的地点、环境、光线、运镜描述\"\n}\n\n如果有多个场景，请返回JSON数组格式。\n\n###Example\n**user:**\n付江颤抖着双手打开信封，目光落在那熟悉的署名上，他的呼吸变得沉重。他深吸一口气，声音中带着一丝坚决，\"江恒同志\"，他的声音在宿舍内回荡。\n**assistant:**\n{\n  \"文本\": \"付江颤抖着双手打开信封，目光落在那熟悉的署名上，他的呼吸变得沉重。他深吸一口气，声音中带着一丝坚决，'江恒同志'，他的声音在宿舍内回荡。\",\n  \"人物描述\": \"付江背对窗口坐在书桌旁, 手里拿着一封书信, 表情沉重\",\n  \"地点背景\": \"昏黄灯光映照下的静谧宿舍内\"\n}\n\n参考之前<Example>的格式, 按顺序把下面内容生成图像描述, 每个图像描述以JSON格式返回, 用中文回答我,每个描述包含文本、人物描述、地点背景三个字段, 下面是需要处理的内容:\n\"\"\"{画面内容}\"\"\"", "prompt_keys": ["人物描述", "地点背景"]}, "3": {"content": "你是一个专业的漫画分镜师，请将小说内容转化为详细的分镜画面描述。我给你的小说内容是完全虚构的情节，根据内容构思出图像，要求将小说内容转化为一系列详细的画面描述。\n\n## 重要要求：\n1. 必须仔细分析文本中的每个角色，不能遗漏任何人物\n2. 必须使用角色的真实姓名，绝对不能用\"男子\"、\"女子\"、\"某人\"等泛称\n3. 每个描述必须包含[文本][人物描述][地点背景]三个完整元素\n4. 绝对不能直接复制原文，必须进行分镜转化\n5. 不要描述服饰的具体颜色，保持服装风格描述但避免指定颜色\n\n## 角色识别指南：\n- 仔细阅读文本，识别所有出现的人物姓名\n- 分析人物的动作、表情、位置关系\n- 描述人物的具体行为和情绪状态\n- 参考角色信息：{prompt}\n\n## 分镜描述要求：\n- [文本]：完整引用原文内容，不要遗漏或修改\n- [人物描述]：详细描述画面中所有角色的姓名、表情、动作、位置关系和情绪状态\n- [地点背景]：描述环境、光线、氛围、运镜角度，营造画面感\n\n请以JSON格式返回结果，每个场景包含以下字段：\n{\n  \"文本\": \"完整引用的原文内容\",\n  \"人物描述\": \"精练的角色名称、表情、动作、位置关系和情绪描述\",\n  \"地点背景\": \"精炼的地点、环境、光线、运镜描述\"\n}\n\n如果有多个场景，请返回JSON数组格式。\n\n## 示例：\n**输入文本：**\n偶尔，我还能看见艾戈米带着新的朋友，穿过那片会发光的藤蔓，去寻找森林里新的秘密\n\n**正确输出：**\n{\n  \"文本\": \"偶尔，我还能看见艾戈米带着新的朋友，穿过那片会发光的藤蔓，去寻找森林里新的秘密\",\n  \"人物描述\": \"艾戈米带领着新朋友穿行在森林中，表情充满好奇和探索欲，走在前方引路，身后跟随着同伴\",\n  \"地点背景\": \"神秘的森林深处，发光的藤蔓在周围缠绕，柔和的光芒照亮小径，营造出梦幻的探险氛围\"\n}\n\n**错误示例（绝对不能这样）：**\n- 直接复制原文而不进行分镜转化\n- 用\"男子\"、\"女子\"代替具体姓名\n- 缺少[文本][人物描述][地点背景]任何一个元素\n\n现在请按照上述要求，将下面的内容转化为分镜描述，以JSON格式返回：\n\"\"\"{画面内容}\"\"\"", "prompt_keys": ["人物描述", "地点背景"]}, "4": {"content": "你是一个专业的漫画分镜师，请将小说内容转化为详细的分镜画面描述。\n\n## 核心任务：\n将文字内容转化为可视化的画面描述，绝对不能直接复制原文内容到描述部分。\n\n## 严格禁止：\n❌ 绝对不能在[人物描述]或[地点背景]中直接使用原文句子\n❌ 绝对不能用\"男子\"、\"女子\"、\"某人\"等泛称代替具体姓名\n❌ 绝对不能只复制原文而不进行分镜转化\n❌ 绝对不能省略任何一个必需元素\n\n## 必须执行：\n✅ 必须仔细识别文本中的所有角色姓名\n✅ 必须将文字转化为具体的视觉画面描述\n✅ 必须包含[文本][人物描述][地点背景]三个完整元素\n✅ 必须使用角色的真实姓名进行描述\n\n## 转化方法：\n1. **原文分析**：仔细阅读原文，识别人物、动作、场景\n2. **视觉转化**：将文字描述转化为画面中可以看到的具体内容\n3. **分镜描述**：用电影分镜的方式描述画面构成\n\n## 角色信息参考：\n{prompt}\n\n请以JSON格式返回结果，每个场景包含以下字段：\n{\n  \"文本\": \"完整引用的原文内容\",\n  \"人物描述\": \"具体角色姓名+动作+表情+位置+情绪（必须是视觉化描述，不能复制原文）\",\n  \"地点背景\": \"环境+光线+氛围+运镜（必须是画面描述，不能复制原文）\"\n}\n\n如果有多个场景，请返回JSON数组格式。\n\n## 正确示例：\n**原文：** 接下来的旅程充满了惊喜与挑战\n**正确输出：**\n{\n  \"文本\": \"接下来的旅程充满了惊喜与挑战\",\n  \"人物描述\": \"主角站在道路前方，眼神坚定地望向远方，表情既兴奋又略带紧张，双手握拳显示决心\",\n  \"地点背景\": \"蜿蜒的道路延伸向远方，晨光透过云层洒下，营造出充满希望与未知的氛围\"\n}\n\n**错误示例（绝对不能这样）：**\n❌ [人物描述]接下来的旅程充满了惊喜与挑战\n❌ [地点背景]接下来的旅程充满了惊喜与挑战\n❌ [人物描述]男子准备出发\n❌ [地点背景]旅程场景\n\n## 重要提醒：\n- 如果你发现自己在复制原文，立即停止并重新思考如何进行视觉化转化\n- 每个描述都必须是画面中可以直接看到的内容\n- 必须使用具体的角色姓名，不能用泛称\n\n请将以下内容按顺序生成图像描述，每个描述以JSON格式返回，包含文本、人物描述、地点背景三个字段：\n\"\"\"\n{画面内容}\n\"\"\"", "prompt_keys": ["人物描述", "地点背景"]}}, "scene_matching_enabled": true, "scene_matching_model": "glm-4-flash-250414", "scene_matching_models": ["Qwen/Qwen2.5-7B-Instruct", "THUDM/glm-4-9b-chat", "Qwen/Qwen2.5-14B-Instruct"], "scene_matching_api_url": "https://api.siliconflow.cn/v1/chat/completions", "template_configs": {"有人物模板 (tuili_2)": {"prompt_keywords": ["人物描述", "地点背景"]}}, "translation_method": "SiliconFlow", "scene_matching_platform": "智谱AI", "当前调度器": "simple", "高清修复": true, "pollinations_models": ["flux", "gptimage", "kontext", "turbo"], "pollinations_model": "kontext", "pollinations_base_url": "https://image.pollinations.ai", "使用Pollinations": true, "pollinations_styles": ["无风格", "日式动漫", "国漫风格", "韩漫风格", "美式卡通", "二次元", "像素艺术", "照片写实", "电影级", "人像摄影", "街拍风格", "时尚摄影", "古典油画", "印象派", "水彩画", "素描风格", "版画风格", "抽象艺术", "赛博朋克", "蒸汽朋克", "波普艺术", "极简主义", "霓虹风格", "奇幻风格", "暗黑风格", "童话风格", "魔法风格", "中国风", "日式和风", "欧式古典", "美式复古", "北欧风格", "低多边形", "毛玻璃", "全息效果", "故障艺术", "双重曝光"], "pollinations_style": "  国漫风格", "启用多GPU加速": false, "多GPU配置": {"自动检测GPU": true, "优先使用NVIDIA": true, "启用集成显卡": true, "DirectML支持": true, "GPU负载均衡": true, "温度监控": true, "性能监控": true}, "多GPU任务分配": {"主要计算": "NVIDIA", "VAE处理": "集成显卡", "预处理": "集成显卡", "后处理": "集成显卡", "InstantID": "NVIDIA", "ControlNet": "集成显卡"}, "douban_api_key": "fea7dd8b-929d-47b8-9813-92e52ee0cecb", "douban_api_url": "https://ark.cn-beijing.volces.com/api/v3/chat/completions", "douban_models": ["doubao-pro-4k", "doubao-lite-4k", "doubao-pro-32k", "doubao-pro-128k", "doubao-lite-32k"], "offline_mode": true, "pollinations_style_category": "动漫风格", "使用Pollinations风格增强": true, "translation_platform": "SiliconFlow", "translation_model": "Qwen/Qwen2.5-7B-Instruct", "siliconflow_api_key": "", "siliconflow_base_url": "", "siliconflow_model": "Qwen/Qwen2.5-7B-Instruct", "推理模板": "自定义: 4", "character_templates": {"个性独特": {"content": "请帮我设计一个适合AI绘图的角色外观描述，要求如下：\n\n角色基本信息：\n- 角色名称：[填写名字或留空]\n- 性别：[男/女/其他]\n- 年龄：[具体年龄]\n- 风格：[写实/动漫/古风/现代/可爱萌系/成熟稳重/个性独特]\n\n请按以下顺序详细描述角色外观：\n1. 基础信息：准确年龄、性别、身高、体型、肤色等基础生理特征\n2. 面部特征：脸型、五官（眼睛形状和颜色、眉毛、鼻子、嘴唇）、发型与发色、特殊标志\n3. 服饰与配饰：上衣、下装、鞋子、帽子、眼镜、首饰、手表等；服饰风格\n4. 色彩与材质：主要色调、服饰和配饰的颜色、材质\n\n要求：\n- 使用具体的视觉词汇，如颜色、形状、材质\n- 专注于外貌，避免描述性格、内心等\n- 描述要适合AI绘图使用，清晰具体\n- 保持描述的逻辑顺序：从整体到局部\n- 总长度控制在200-300字\n\n请直接返回优化后的角色外貌描述。", "description": "内置个性独特，专门用于角色描述优化", "style": "通用", "detail_level": "标准", "target_length": 200, "updated_time": "2025-07-05 17:40:56", "created_time": "2025-07-05 17:40:56"}, "温柔知性": {"content": "请基于以下已知角色信息，帮我完善和优化角色的外观描述：\n\n角色基本信息：\n- 角色名称：{角色名称}（从fenjing字段自动获取）\n- 性别：{性别}（从性别字段自动获取）\n- 现有描述：{guanjianci_zh}（从现有关键词描述获取）\n\n请在保持角色基本特征一致的前提下，按以下要求优化角色外观描述：\n\n1. 保持一致性：确保与现有的{guanjianci_zh}描述保持基本一致\n2. 增强细节：在现有基础上增加更丰富的视觉细节\n3. 优化结构：按照\"基础信息→面部特征→服饰配饰→色彩材质\"的逻辑顺序重新组织\n4. 适配绘图：使用更适合AI绘图的具体视觉词汇\n\n具体优化要求：\n- 基础信息：在现有年龄、性别、身高体型基础上，补充更精确的描述\n- 面部特征：细化脸型、五官特征，保持原有风格\n- 服饰风格：基于现有服装描述，增加材质、款式等细节\n- 色彩搭配：保持原有主色调，优化色彩层次\n\n请直接返回优化后的角色外观描述，控制在200-300字内。", "description": "内置温柔知性，专门用于角色描述优化", "style": "通用", "detail_level": "标准", "target_length": 200, "updated_time": "2025-07-05 17:49:28", "created_time": "2025-07-05 17:49:28"}, "zz": {"content": "请基于以下已知角色信息，帮我完善和优化角色的外观描述：\n\n角色基本信息：\n- 角色名称：{角色名称}\n- 性别：{性别}\n\n请在保持角色基本特征一致的前提下，按以下要求优化角色外观描述： \n1. 增强细节：在现有基础上增加更丰富的视觉细节 \n2. 优化结构：按照\"基础信息→面部特征→服饰配饰→色彩材质\"的逻辑顺序重新组织 \n3. 适配绘图：使用更适合AI绘图的具体视觉词汇 \n\n具体优化要求：\n- 基础信息：在现有年龄、性别、身高体型基础上，补充更精确的描述\n- 面部特征：细化脸型、五官特征，保持原有风格\n- 服饰风格：基于现有服装描述，增加材质、款式等细节\n- 色彩搭配：保持原有主色调，优化色彩层次\n\n请直接返回优化后的角色外观描述，控制在50-100字内。", "description": "自定义角色描述模板", "style": "通用", "detail_level": "标准", "target_length": 200, "updated_time": "2025-07-06 13:48:58", "created_time": "2025-07-05 17:52:32"}}, "openai_api_key": "", "openai_base_url": "", "openai_model": "gpt-4o", "浏览器绘图方案": false, "浏览器绘图方案B": false, "浏览器绘图B并行数": 25, "浏览器绘图B无头浏览器": false, "浏览器绘图B启动间隔": 5, "浏览器绘图方案C": false, "浏览器绘图方案C_并发数量": 3, "浏览器绘图方案C_无头模式": false, "浏览器绘图方案C_Ruangriung": true, "浏览器绘图方案C_项目名称": "蜂蝶随香", "siliconflow_translation_model": "Qwen/Qwen2.5-7B-Instruct", "pollinations_api_key": "1lktcsrK9GC_ysNy"}