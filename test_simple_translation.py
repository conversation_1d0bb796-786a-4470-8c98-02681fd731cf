#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试角色名翻译和四个角色固定词生成
"""

import os
import sys

# 添加项目路径到sys.path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from pyz.工作流.A7_Pollinations工作流 import 翻译角色名, 生成角色参考固定词

def test_四个角色翻译():
    """测试四个角色的翻译和固定词生成"""
    print("=" * 60)
    print("测试四个角色的翻译和固定词生成")
    print("=" * 60)
    
    # 四个角色组合
    角色名 = "母亲,父亲,挑水老人,迷路的孩子"
    模拟URL = "https://example.com/four_characters.png"
    
    print(f"🎭 原始角色组合: {角色名}")
    
    # 分别翻译每个角色
    角色列表 = [角色.strip() for 角色 in 角色名.split(',')]
    翻译后角色列表 = []
    
    print(f"\n🌐 翻译过程:")
    for 角色 in 角色列表:
        翻译后 = 翻译角色名(角色)
        翻译后角色列表.append(翻译后)
        print(f"   {角色} → {翻译后}")
    
    print(f"\n📋 翻译后角色列表: {翻译后角色列表}")
    
    # 生成固定词
    print(f"\n🔧 生成角色参考固定词...")
    固定词 = 生成角色参考固定词(角色名, 模拟URL)
    
    if 固定词:
        print(f"✅ 生成成功:")
        print(f"📝 固定词: {固定词}")
        
        # 验证是否包含所有翻译后的角色名
        print(f"\n🔍 验证角色名:")
        for i, 翻译后角色 in enumerate(翻译后角色列表):
            if 翻译后角色 in 固定词:
                print(f"   ✅ {翻译后角色} - 已包含")
            else:
                print(f"   ❌ {翻译后角色} - 未包含")
        
        # 验证位置描述
        位置词 = ["upper left", "upper right", "lower left", "lower right"]
        print(f"\n🔍 验证位置描述:")
        for 位置 in 位置词:
            if 位置 in 固定词:
                print(f"   ✅ {位置} - 已包含")
            else:
                print(f"   ❌ {位置} - 未包含")
        
        # 显示完整的提示词结构
        原始提示词 = "A family scene in a rural village"
        风格词 = "Chinese animation style, high quality"
        完整提示词 = f"{固定词}, {原始提示词}, {风格词}"
        
        print(f"\n🎯 完整提示词示例:")
        print(f"📝 {完整提示词}")
        print(f"📊 长度: {len(完整提示词)} 字符")
        
    else:
        print("❌ 固定词生成失败")

def test_其他角色组合():
    """测试其他角色组合的翻译"""
    print("\n" + "=" * 60)
    print("测试其他角色组合的翻译")
    print("=" * 60)
    
    测试组合 = [
        ("艾米丽", "单个角色"),
        ("艾米丽,露西", "两个角色"),
        ("艾米丽,露西,斑斑", "三个角色"),
        ("老师,学生,医生,护士", "四个职业角色"),
    ]
    
    模拟URL = "https://example.com/reference.png"
    
    for 角色名, 描述 in 测试组合:
        print(f"\n🧪 测试{描述}: {角色名}")
        
        # 翻译角色名
        角色列表 = [角色.strip() for 角色 in 角色名.split(',')]
        翻译后角色列表 = [翻译角色名(角色) for 角色 in 角色列表]
        
        print(f"🌐 翻译结果: {' | '.join([f'{原始}→{翻译}' for 原始, 翻译 in zip(角色列表, 翻译后角色列表)])}")
        
        # 生成固定词
        固定词 = 生成角色参考固定词(角色名, 模拟URL)
        if 固定词:
            print(f"✅ 固定词: {固定词}")
        else:
            print("❌ 固定词生成失败")

if __name__ == "__main__":
    print("🚀 开始简单测试角色名翻译功能")
    
    # 测试四个角色
    test_四个角色翻译()
    
    # 测试其他组合
    test_其他角色组合()
    
    print("\n" + "=" * 60)
    print("✅ 测试完成")
    print("=" * 60)
