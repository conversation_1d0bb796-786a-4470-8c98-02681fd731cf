﻿import os
import random
import copy
from pyz.任务运行文件 import A0_config
from pyz.工作流.A0_常规工作流 import sd工作流

def sd文生图(self, 请求参数, 原始图片, lineart, 运行程序, json_data, depth, false, api = (False, None)):
    print('当前使用SD模型')

    # 192GB内存优化 - 强制垃圾回收释放内存
    import gc
    gc.collect()

    # 加载当前模型到内存
    try:
        from pyz.任务运行文件.A30_模型内存管理 import 获取模型管理器
        管理器 = 获取模型管理器()
        模型名 = A0_config.config['当前选中模型']

        内存模型路径 = 管理器.获取模型(模型名)
        if 内存模型路径:
            print(f"🚀 使用内存加载的模型: {内存模型路径}")

    except Exception as e:
        print(f"模型内存加载失败: {e}")

    # 尊重用户的工作流选择，不强制切换
    try:
        # 检查是否为参考图生成任务
        任务类型 = json_data.get('任务类型', '')
        if 任务类型 == '参考图':
            print(f"   🎨 检测到参考图生成任务，使用专用工作流")

            from pyz.工作流.参考图专用工作流 import 获取参考图专用工作流

            # 获取参考图专用工作流
            角色名 = json_data.get('角色名', '')
            提示词 = json_data.get('提示词', '')
            模型名 = A0_config.config.get('当前选中模型', '')

            参考图工作流 = 获取参考图专用工作流(模型名, 提示词, 角色名)

            # 检查是否为云端参考图工作流
            if isinstance(参考图工作流, dict) and 参考图工作流.get('工作流类型') == '云端参考图':
                print(f"   ☁️  检测到云端参考图工作流，切换到云端生成")

                # 调用云端参考图生成
                return 云端参考图生成(参考图工作流, json_data, mood)
            else:
                # 本地ComfyUI工作流
                请求参数['prompt'] = 参考图工作流
                print(f"   ✅ 使用本地参考图专用工作流")
        else:
            # 普通图片生成，使用用户选择的工作流
            选中工作流名称 = A0_config.config.get('选中工作流', '默认工作流')
            print(f"   🎯 用户选择的工作流: {选中工作流名称}")

            # 检查是否使用新的工作流管理器
            if 选中工作流名称 in ['IP-Adapter FaceID 跨模型工作流', 'InstantID角色一致性工作流', 'Pollinations标准工作流', '云端定妆本地一致性工作流']:
                print(f"   🚀 使用新工作流管理器")

                from pyz.工作流.工作流管理器 import workflow_manager

                # 准备参数
                参数 = {
                    '模型名称': A0_config.config.get('当前选中模型', ''),
                    '提示词': json_data.get('提示词', ''),
                    '负面提示词': '',
                    'width': int(A0_config.config.get('图片长度', 800)),
                    'height': int(A0_config.config.get('图片宽度', 600)),
                    'seed': json_data.get('随机种子', random.randint(1000000000, 9999999999))
                }

                # 尝试获取角色参考图像
                try:
                    if hasattr(self, 'mood') and hasattr(self.mood, '项目配置'):
                        # 获取角色名，支持不同的字段名
                        角色名 = json_data.get('人物分镜', '') or json_data.get('fenjing', '') or json_data.get('角色名', '')

                        # 如果是字符串且包含多个角色，取第一个
                        if isinstance(角色名, str) and ',' in 角色名:
                            角色名 = 角色名.split(',')[0].strip()
                        # 如果是列表，取第一个元素
                        elif isinstance(角色名, list) and 角色名:
                            角色名 = 角色名[0]

                        print(f"   🎭 检测到角色: {角色名}")

                        if 角色名:
                            项目配置 = getattr(self.mood, '项目配置', {})
                            fenjing = 项目配置.get('fenjing', {})
                            角色信息 = fenjing.get(角色名, {})
                            参考图像路径 = 角色信息.get('img', '')

                            if 参考图像路径 and os.path.exists(参考图像路径):
                                参数['参考图片路径'] = 参考图像路径
                                print(f"   📸 找到角色参考图像: {参考图像路径}")
                            else:
                                print(f"   ⚠️  角色 {角色名} 没有有效的参考图像")
                                # 尝试从备选图片中选择一张作为参考图
                                if '备选图片' in 角色信息:
                                    for 备选图片 in 角色信息['备选图片']:
                                        if os.path.exists(备选图片):
                                            参数['参考图片路径'] = 备选图片
                                            print(f"   📸 使用备选图片作为参考: {备选图片}")
                                            break
                except Exception as e:
                    print(f"   ⚠️  获取角色参考图像失败: {e}")

                # 获取工作流
                工作流 = workflow_manager.获取工作流(选中工作流名称, **参数)
                print(f"✅ 使用新工作流管理器生成工作流")
                请求参数['prompt'] = 工作流

            else:
                # 使用传统工作流
                print("---------------------------------------------")
                print(f"现在是{选中工作流名称}工作流")
                print("---------------------------------------------")
                # 检查是否是文本驱动工作流，如果是则动态生成
                if 选中工作流名称.startswith('文本驱动') or 选中工作流名称.startswith('ConsisStory'):
                    try:
                        from pyz.工作流.工作流管理器 import 工作流管理器
                        管理器 = 工作流管理器()
                        工作流 = 管理器.获取工作流(选中工作流名称)
                        if not 工作流:
                            raise KeyError(f"工作流 '{选中工作流名称}' 生成失败")
                    except Exception as e:
                        print(f"⚠️  文本驱动工作流生成失败: {e}")
                        # 回退到默认工作流
                        工作流 = copy.deepcopy(A0_config.config['工作流'].get('默认工作流', {}))
                else:
                    工作流 = copy.deepcopy(A0_config.config['工作流'][选中工作流名称])
                请求参数['prompt'] = 工作流

    except Exception as e:
        print(f"⚠️  工作流处理失败，使用默认工作流: '{选中工作流名称}'")
        print(f"   错误详情: {e}")
        print(f"   错误类型: {type(e).__name__}")

        # 显示详细的错误堆栈信息
        import traceback
        print(f"   完整错误堆栈:")
        traceback.print_exc()

        from pyz.工作流.A0_常规工作流 import sd工作流
        请求参数['prompt'] = sd工作流(self)

    # 获取随机种子
    随机种子 = json_data.get('随机种子')
    if 随机种子 is None or 随机种子 == -1:
        随机种子 = random.randint(1000000000, 0x2540BE3FF)

    # 获取配置参数
    Lora强度 = float(A0_config.config.get('Lora强度', 0.7))
    采样步数 = int(A0_config.config.get('图片采样步数', 30))
    采样方法 = A0_config.config.get('图片采样方法', 'dpmpp_2m')
    调度器 = A0_config.config.get('调度器', 'karras')
    提示词相关性 = float(A0_config.config.get('提示词相关性', 4.5))
    图片长度 = int(A0_config.config.get('图片长度', 640))
    图片宽度 = int(A0_config.config.get('图片宽度', 480))

    # 处理用户提示词
    用户提示词 = json_data.get('提示词', '美丽的风景')

    # 处理LoRA
    lora_list = []
    if json_data.get('lora'):
        for lora_group in json_data['lora']:
            for lora_name in lora_group:
                # 根据模型类型过滤LoRA
                模型 = A0_config.config['当前选中模型'].lower()
                if 'xl' in 模型:
                    if 'sdxl' in lora_name.lower():
                        lora_list.append(lora_name.replace(':1>', f':{Lora强度}>'))
                else:
                    # 过滤不兼容的LoRA
                    不兼容词 = ['flux', 'kolors', 'wan', 'hunyuan', 'sdxl']
                    if not any(word in lora_name.lower() for word in 不兼容词):
                        lora_list.append(lora_name.replace(':1>', f':{Lora强度}>'))

    # 组合最终正面提示词
    基础质量词 = "masterpiece, best quality, highly detailed, ultra-detailed, 8k wallpaper, extremely detailed, professional, sharp focus, vivid colors, perfect composition"

    if lora_list:
        最终正面词 = f"{基础质量词}, {用户提示词}, {', '.join(lora_list)}"
    else:
        最终正面词 = f"{基础质量词}, {用户提示词}"

    # 清理提示词
    for 替换词 in [',,', '(,),', ', ,', ',  ,']:
        最终正面词 = 最终正面词.replace(替换词, ',')
    最终正面词 = 最终正面词.strip(', ')

    # 获取反面词
    反面词 = getattr(self.mood, '通用反面词', 'nsfw, nude, naked, sexual, adult content, explicit, pornographic, erotic, mature content, inappropriate, suggestive, revealing, exposed, intimate, sensual, seductive, provocative, lewd, vulgar, indecent, obscene, text, watermark, signature, username, artist name, copyright, logo, web address, url, website, brand name, company name, title, caption, subtitle, label, tag, stamp, mark, emblem, badge, seal, sticker, overlay, annotation, credit, source, attribution, (text:1.8), (watermark:1.8), (signature:1.8), (logo:1.8), (copyright:1.8), (nsfw:1.5), (nude:1.5), (naked:1.5), (sexual:1.5), (adult:1.5), lowres, bad anatomy, bad hands, error, missing fingers, extra digit, fewer digits, cropped, worst quality, low quality, normal quality, jpeg artifacts, blurry, deformed, ugly, mutilated, disfigured, extra limbs, face cut, head cut, extra fingers, extra arms, poorly drawn face, mutation, bad proportions, malformed limbs, mutated hands, fused fingers, long neck, sketch, amateur, low contrast, underexposed, overexposed, oversaturated, undersaturated')

    # 更新工作流参数
    if '2' in 请求参数['prompt']:  # 正面提示词节点
        请求参数['prompt']['2']['inputs']['text'] = 最终正面词

    if '3' in 请求参数['prompt']:  # 负面提示词节点
        请求参数['prompt']['3']['inputs']['text'] = 反面词

    if '4' in 请求参数['prompt']:  # 潜在图像尺寸
        请求参数['prompt']['4']['inputs']['width'] = 图片长度
        请求参数['prompt']['4']['inputs']['height'] = 图片宽度

    # 检查是否为参考图任务，参考图任务不修改工作流中的种子设置
    任务类型 = json_data.get('任务类型', '')

    # 获取正确的KSampler节点ID
    ksampler_node = None
    for node_id, node_info in 请求参数['prompt'].items():
        if node_info.get('class_type') == 'KSampler':
            ksampler_node = node_id
            break

    if 任务类型 != '参考图' and ksampler_node:  # 只有非参考图任务才修改KSampler参数
        请求参数['prompt'][ksampler_node]['inputs']['seed'] = 随机种子
        请求参数['prompt'][ksampler_node]['inputs']['steps'] = 采样步数
        请求参数['prompt'][ksampler_node]['inputs']['cfg'] = 提示词相关性
        请求参数['prompt'][ksampler_node]['inputs']['sampler_name'] = 采样方法
        请求参数['prompt'][ksampler_node]['inputs']['scheduler'] = 调度器
    elif 任务类型 == '参考图' and '3' in 请求参数['prompt']:  # 参考图任务只调整非种子参数
        请求参数['prompt']['3']['inputs']['steps'] = 采样步数
        请求参数['prompt']['3']['inputs']['cfg'] = 提示词相关性
        请求参数['prompt']['3']['inputs']['sampler_name'] = 采样方法
        请求参数['prompt']['3']['inputs']['scheduler'] = 调度器
        print(f"   🎨 参考图任务保留工作流中的随机种子设置")
    elif 任务类型 == '参考图' and ksampler_node:  # 参考图任务但使用传统节点编号
        请求参数['prompt'][ksampler_node]['inputs']['steps'] = 采样步数
        请求参数['prompt'][ksampler_node]['inputs']['cfg'] = 提示词相关性
        请求参数['prompt'][ksampler_node]['inputs']['sampler_name'] = 采样方法
        请求参数['prompt'][ksampler_node]['inputs']['scheduler'] = 调度器
        print(f"   🎨 参考图任务保留工作流中的随机种子设置")

    print(f"✅ 优化后的提示词: {最终正面词[:100]}...")
    print(f"✅ 采样参数: {采样步数}步, {采样方法}, CFG:{提示词相关性}")
    print(f"✅ 图片尺寸: {图片长度}x{图片宽度}")

    return 请求参数

def 获取基础工作流请求(模型名称, 提示词, 负面提示词, 宽度, 高度, 种子, 步数, cfg, 采样器, 调度器):
    """获取基础工作流请求参数（不使用InstantID）"""
    try:
        from pyz.工作流.A1_基础工作流 import 获取基础工作流

        工作流 = 获取基础工作流(模型名称, "标准", "标准")

        # 设置参数
        if "2" in 工作流:
            工作流["2"]["inputs"]["text"] = 提示词
        if "3" in 工作流:
            工作流["3"]["inputs"]["text"] = 负面提示词
        if "4" in 工作流:
            工作流["4"]["inputs"]["width"] = 宽度
            工作流["4"]["inputs"]["height"] = 高度
        if "5" in 工作流:
            工作流["5"]["inputs"]["seed"] = 种子
            工作流["5"]["inputs"]["steps"] = 步数
            工作流["5"]["inputs"]["cfg"] = cfg
            工作流["5"]["inputs"]["sampler_name"] = 采样器
            工作流["5"]["inputs"]["scheduler"] = 调度器

        请求参数 = {
            "prompt": 工作流
        }

        return 请求参数
    except Exception as e:
        print(f"获取基础工作流失败: {e}")
        return None

def 云端参考图生成(云端工作流, json_data, mood):
    """
    处理云端参考图生成任务

    Args:
        云端工作流: 云端工作流配置
        json_data: 任务数据
        mood: 主窗口对象

    Returns:
        生成结果
    """
    try:
        print("☁️  开始云端参考图生成")

        # 导入云端生成函数
        from pyz.工作流.A7_Pollinations工作流 import 生成Pollinations图片

        # 获取参数
        提示词 = 云端工作流.get('提示词', '')
        角色名 = 云端工作流.get('角色名', '')
        图片宽度 = 云端工作流.get('图片宽度', 800)
        图片高度 = 云端工作流.get('图片高度', 600)
        云端模型 = 云端工作流.get('云端模型', 'flux')
        云端配置 = 云端工作流.get('云端配置', {})

        # 获取当前项目文件夹路径
        if hasattr(mood, '当前项目文件夹') and mood.当前项目文件夹:
            项目路径 = mood.当前项目文件夹
        else:
            项目路径 = mood.项目文件夹

        print(f"☁️  云端模型: {云端模型}")
        print(f"📐 图片尺寸: {图片宽度}x{图片高度}")
        print(f"✨ 提示词: {提示词}")
        print(f"🎭 角色名: {角色名}")
        print(f"📁 项目路径: {项目路径}")

        print("🔍 调试 - 准备调用Pollinations生成")
        # 调用云端生成
        生成结果 = 生成Pollinations图片(
            提示词=提示词,
            模型名称=云端模型,
            图片宽度=图片宽度,
            图片高度=图片高度,
            图片数量=1,  # 参考图只生成一张
            项目路径=项目路径,
            当前编号=1,
            已有数量=0,
            备选图片组=None,
            反面词=云端工作流.get('负面提示词', ''),
            增强=云端配置.get('增强', True),
            安全模式=云端配置.get('安全模式', True),
            风格=云端配置.get('风格', ''),
            种子=None,  # 让云端随机生成
            CFG系数=云端配置.get('CFG系数', 7.5),
            采样步数=云端配置.get('采样步数', 25),
            重绘幅度=云端配置.get('重绘幅度', 0.15),
            角色名=角色名
        )

        print(f"🔍 调试 - 生成结果: {生成结果}")
        print(f"🔍 调试 - 生成结果类型: {type(生成结果)}")
        print(f"🔍 调试 - 生成结果长度: {len(生成结果) if 生成结果 else 0}")

        if 生成结果 and len(生成结果) > 0:
            # 获取生成的图片路径
            图片信息 = 生成结果[0]
            图片路径 = 图片信息.get('path', '')
            种子值 = 图片信息.get('seep', '')  # 修复：使用正确的键名 'seep'

            print(f"✅ 云端参考图生成成功: {图片路径}")
            print(f"🌱 种子值: {种子值}")
            print(f"🔍 调试 - 图片信息: {图片信息}")  # 调试信息

            # 移动图片到角色参考图片文件夹
            import os
            import shutil

            if 图片路径 and os.path.exists(图片路径):
                # 创建角色参考图片文件夹
                参考图文件夹 = os.path.join(项目路径, '角色参考图片', 角色名)
                os.makedirs(参考图文件夹, exist_ok=True)

                # 生成新的文件名 - 优先使用种子值
                if 种子值:
                    新文件名 = f"{角色名}_seed{种子值}.png"
                else:
                    # 如果没有种子值，使用时间戳作为备用
                    import time
                    新文件名 = f"{角色名}_cloud_{int(time.time())}.png"
                    print(f"⚠️ 未获取到种子值，使用时间戳命名: {新文件名}")

                新文件路径 = os.path.join(参考图文件夹, 新文件名)

                # 移动文件
                shutil.move(图片路径, 新文件路径)
                print(f"📸 参考图已保存到: {新文件路径}")

                # 发送完成信号
                if hasattr(mood, 'worker_solo') and hasattr(mood.worker_solo, '_signal'):
                    mood.worker_solo._signal.emit(('绘图完成', (角色名, 新文件路径, None, None), '参考图'))

                return 新文件路径
            else:
                print(f"❌ 云端生成的图片文件不存在: {图片路径}")
                return None
        else:
            print(f"❌ 云端参考图生成失败")
            return None

    except Exception as e:
        print(f"❌ 云端参考图生成异常: {e}")
        import traceback
        traceback.print_exc()
        return None