#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试UI导入修复
"""

import os
import sys

# 添加项目路径到sys.path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_导入修复():
    """测试导入修复"""
    print("=" * 60)
    print("测试UI导入修复")
    print("=" * 60)
    
    try:
        # 测试导入绘图模型按钮方法
        from pyz.UI文件.软件设置UI_methods.绘图模型按钮 import 绘图模型按钮
        print("✅ 绘图模型按钮方法导入成功")
        
        # 测试导入更新密钥方法
        from pyz.UI文件.软件设置UI_methods.更新Pollinations密钥 import 更新Pollinations密钥, 切换Pollinations密钥显示
        print("✅ 更新Pollinations密钥方法导入成功")
        
        # 测试PyQt5组件导入
        from PyQt5.QtWidgets import QLineEdit, QLabel, QComboBox, QCheckBox, QPushButton
        print("✅ PyQt5组件导入成功")
        
        # 测试创建QLineEdit实例
        line_edit = QLineEdit()
        line_edit.setPlaceholderText("测试")
        line_edit.setEchoMode(QLineEdit.Password)
        print("✅ QLineEdit实例创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_模拟UI创建():
    """模拟UI创建过程"""
    print("\n" + "=" * 60)
    print("模拟UI创建过程")
    print("=" * 60)
    
    try:
        from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QLineEdit, QLabel, QPushButton
        from PyQt5.QtCore import Qt
        
        # 创建应用（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建测试窗口
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 模拟创建Pollinations密钥控件
        密钥标签 = QLabel('API密钥')
        密钥标签.setToolTip('Pollinations API密钥')
        
        密钥输入框 = QLineEdit()
        密钥输入框.setPlaceholderText('输入API密钥')
        密钥输入框.setEchoMode(QLineEdit.Password)
        
        显示按钮 = QPushButton('👁')
        显示按钮.setToolTip('显示/隐藏密钥')
        
        # 添加到布局
        layout.addWidget(密钥标签)
        layout.addWidget(密钥输入框)
        layout.addWidget(显示按钮)
        
        print("✅ UI控件创建成功")
        print(f"   📋 标签文本: {密钥标签.text()}")
        print(f"   📝 输入框占位符: {密钥输入框.placeholderText()}")
        print(f"   🔒 密码模式: {密钥输入框.echoMode() == QLineEdit.Password}")
        print(f"   👁 按钮文本: {显示按钮.text()}")
        
        # 测试密钥显示切换
        print(f"\n🔄 测试密钥显示切换:")
        print(f"   初始模式: {'密码' if 密钥输入框.echoMode() == QLineEdit.Password else '普通'}")
        
        # 切换到普通模式
        密钥输入框.setEchoMode(QLineEdit.Normal)
        显示按钮.setText('🙈')
        print(f"   切换后模式: {'密码' if 密钥输入框.echoMode() == QLineEdit.Password else '普通'}")
        print(f"   按钮文本: {显示按钮.text()}")
        
        # 切换回密码模式
        密钥输入框.setEchoMode(QLineEdit.Password)
        显示按钮.setText('👁')
        print(f"   再次切换模式: {'密码' if 密钥输入框.echoMode() == QLineEdit.Password else '普通'}")
        print(f"   按钮文本: {显示按钮.text()}")
        
        return True
        
    except Exception as e:
        print(f"❌ UI创建测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_配置集成():
    """测试配置集成"""
    print("\n" + "=" * 60)
    print("测试配置集成")
    print("=" * 60)
    
    try:
        from pyz.任务运行文件 import A0_config
        
        # 测试密钥配置
        测试密钥 = "test_key_123"
        原密钥 = A0_config.config.get('pollinations_api_key', '')
        
        print(f"📋 原始密钥: {'已设置' if 原密钥 else '未设置'}")
        
        # 设置测试密钥
        A0_config.config['pollinations_api_key'] = 测试密钥
        print(f"🔧 设置测试密钥: {测试密钥}")
        
        # 验证设置
        验证密钥 = A0_config.config.get('pollinations_api_key', '')
        if 验证密钥 == 测试密钥:
            print("✅ 密钥设置验证成功")
        else:
            print("❌ 密钥设置验证失败")
        
        # 恢复原密钥
        A0_config.config['pollinations_api_key'] = 原密钥
        print(f"🔄 已恢复原密钥")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_方法调用():
    """测试方法调用"""
    print("\n" + "=" * 60)
    print("测试方法调用")
    print("=" * 60)
    
    try:
        from PyQt5.QtWidgets import QApplication, QWidget, QLineEdit
        from pyz.任务运行文件 import A0_config
        
        # 创建应用（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建模拟的self对象
        class 模拟UI:
            def __init__(self):
                self.Pollinations密钥 = QLineEdit()
                self.Pollinations密钥显示 = None  # 简化测试
                
            def 缩放(self, value):
                return value
        
        模拟ui = 模拟UI()
        
        # 测试更新密钥方法
        from pyz.UI文件.软件设置UI_methods.更新Pollinations密钥 import 更新Pollinations密钥
        
        # 设置测试密钥
        模拟ui.Pollinations密钥.setText("test_api_key")
        
        # 调用更新方法
        更新Pollinations密钥(模拟ui)
        
        # 验证结果
        保存的密钥 = A0_config.config.get('pollinations_api_key', '')
        if 保存的密钥 == "test_api_key":
            print("✅ 更新密钥方法调用成功")
        else:
            print("❌ 更新密钥方法调用失败")
        
        # 清理
        A0_config.config['pollinations_api_key'] = ''
        
        return True
        
    except Exception as e:
        print(f"❌ 方法调用测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 开始测试UI导入修复")
    
    # 测试导入
    导入成功 = test_导入修复()
    
    # 测试UI创建
    UI创建成功 = test_模拟UI创建()
    
    # 测试配置集成
    配置集成成功 = test_配置集成()
    
    # 测试方法调用
    方法调用成功 = test_方法调用()
    
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    print(f"📦 导入测试: {'✅ 通过' if 导入成功 else '❌ 失败'}")
    print(f"🎨 UI创建测试: {'✅ 通过' if UI创建成功 else '❌ 失败'}")
    print(f"⚙️ 配置集成测试: {'✅ 通过' if 配置集成成功 else '❌ 失败'}")
    print(f"🔧 方法调用测试: {'✅ 通过' if 方法调用成功 else '❌ 失败'}")
    
    if all([导入成功, UI创建成功, 配置集成成功, 方法调用成功]):
        print("\n🎉 所有测试通过！UI导入问题已修复")
        print("\n💡 现在可以正常使用:")
        print("1. Pollinations API密钥输入框")
        print("2. 密钥显示/隐藏切换")
        print("3. 自动保存密钥配置")
        print("4. kontext模型认证")
    else:
        print("\n⚠️ 部分测试失败，需要进一步检查")
    
    print("=" * 60)
