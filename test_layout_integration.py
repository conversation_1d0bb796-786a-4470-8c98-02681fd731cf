#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试布局集成
"""

import os
import sys

# 添加项目路径到sys.path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_布局导入():
    """测试布局相关导入"""
    print("=" * 60)
    print("测试布局导入")
    print("=" * 60)
    
    try:
        # 测试绘图模型按钮导入
        from pyz.UI文件.软件设置UI_methods.绘图模型按钮 import 绘图模型按钮
        print("✅ 绘图模型按钮导入成功")
        
        # 测试创建绘图任务布局导入
        from pyz.UI文件.软件设置UI_methods.创建绘图任务布局 import 创建绘图任务布局
        print("✅ 创建绘图任务布局导入成功")
        
        # 测试更新显示状态导入
        from pyz.UI文件.软件设置UI_methods.更新Pollinations显示状态 import 更新Pollinations显示状态
        print("✅ 更新Pollinations显示状态导入成功")
        
        # 测试密钥方法导入
        from pyz.UI文件.软件设置UI_methods.更新Pollinations密钥 import 更新Pollinations密钥, 切换Pollinations密钥显示
        print("✅ 密钥方法导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_配置状态():
    """测试配置状态"""
    print("\n" + "=" * 60)
    print("测试配置状态")
    print("=" * 60)
    
    try:
        from pyz.任务运行文件 import A0_config
        
        # 检查关键配置
        使用Pollinations = A0_config.config.get('使用Pollinations', False)
        当前模型 = A0_config.config.get('pollinations_model', 'flux')
        当前密钥 = A0_config.config.get('pollinations_api_key', '')
        
        print(f"📋 当前配置状态:")
        print(f"   ☁️ 使用Pollinations: {使用Pollinations}")
        print(f"   🎯 当前模型: {当前模型}")
        print(f"   🔑 API密钥: {'已设置' if 当前密钥 else '未设置'}")
        
        if 当前模型 == 'kontext':
            if 当前密钥:
                print(f"\n✅ 配置完整：kontext模型 + API密钥")
                状态 = "完整"
            else:
                print(f"\n⚠️ 配置不完整：kontext模型但缺少API密钥")
                状态 = "不完整"
        else:
            print(f"\n🔍 使用{当前模型}模型")
            状态 = "其他模型"
        
        return 状态
        
    except Exception as e:
        print(f"❌ 配置检查失败: {e}")
        return False

def test_布局顺序():
    """测试布局顺序"""
    print("\n" + "=" * 60)
    print("测试布局顺序")
    print("=" * 60)
    
    try:
        # 模拟布局顺序
        预期布局顺序 = [
            "Pollinations复选框",
            "云端模型选择",
            "API密钥输入",  # 新增的位置
            "云端风格选择",
            "风格设置区域"
        ]
        
        print("📋 预期的布局顺序:")
        for i, 布局名 in enumerate(预期布局顺序, 1):
            if 布局名 == "API密钥输入":
                print(f"   {i}. {布局名} ⭐ 新增位置")
            else:
                print(f"   {i}. {布局名}")
        
        print(f"\n✅ API密钥输入框现在位于云端模型和云端风格之间")
        print(f"🎯 这样的布局更加紧凑和直观")
        
        return True
        
    except Exception as e:
        print(f"❌ 布局顺序测试失败: {e}")
        return False

def test_UI预期效果():
    """测试UI预期效果"""
    print("\n" + "=" * 60)
    print("测试UI预期效果")
    print("=" * 60)
    
    try:
        print("🎨 预期的UI效果:")
        print()
        print("┌─────────────────────────────────────────┐")
        print("│ ☑ 使用Pollinations云端模型              │")
        print("├─────────────────────────────────────────┤")
        print("│ 云端模型: [kontext        ▼] [模型管理] │")
        print("├─────────────────────────────────────────┤")
        print("│ API密钥:  [••••••••••••••••••] [👁]     │") # ⭐ 新位置
        print("├─────────────────────────────────────────┤")
        print("│ 云端风格: [无风格         ▼]           │")
        print("├─────────────────────────────────────────┤")
        print("│ 正面提示词: [文本框...]                 │")
        print("│ 反面提示词: [文本框...]                 │")
        print("│ [保存设置] [重置为默认]                 │")
        print("└─────────────────────────────────────────┘")
        print()
        
        print("💡 优势:")
        print("   1. API密钥紧邻模型选择，逻辑清晰")
        print("   2. 用户选择模型后立即看到密钥输入")
        print("   3. 布局更紧凑，减少视觉跳跃")
        print("   4. 符合用户的操作流程")
        
        return True
        
    except Exception as e:
        print(f"❌ UI预期效果测试失败: {e}")
        return False

def test_功能完整性():
    """测试功能完整性"""
    print("\n" + "=" * 60)
    print("测试功能完整性")
    print("=" * 60)
    
    try:
        from pyz.任务运行文件 import A0_config
        
        # 模拟完整的使用流程
        print("🔄 模拟完整使用流程:")
        
        # 1. 启用Pollinations
        print("   1. 用户勾选'使用Pollinations云端模型'")
        A0_config.config['使用Pollinations'] = True
        
        # 2. 选择kontext模型
        print("   2. 用户选择'kontext'模型")
        A0_config.config['pollinations_model'] = 'kontext'
        
        # 3. 输入API密钥
        print("   3. 用户在紧邻的密钥框中输入API密钥")
        测试密钥 = "sk-test123456789"
        A0_config.config['pollinations_api_key'] = 测试密钥
        
        # 4. 验证配置
        print("   4. 系统验证配置完整性")
        
        验证结果 = {
            'Pollinations启用': A0_config.config.get('使用Pollinations', False),
            '模型选择': A0_config.config.get('pollinations_model', ''),
            'API密钥': A0_config.config.get('pollinations_api_key', '')
        }
        
        print(f"\n📊 配置验证结果:")
        for 项目, 值 in 验证结果.items():
            if 项目 == 'API密钥':
                显示值 = '已设置' if 值 else '未设置'
                状态 = '✅' if 值 else '❌'
            else:
                显示值 = 值
                状态 = '✅' if 值 else '❌'
            print(f"   {状态} {项目}: {显示值}")
        
        # 清理测试数据
        A0_config.config['pollinations_api_key'] = ''
        
        所有配置正确 = all([
            验证结果['Pollinations启用'],
            验证结果['模型选择'] == 'kontext',
            验证结果['API密钥']
        ])
        
        if 所有配置正确:
            print(f"\n🎉 功能完整性测试通过！")
            print(f"   用户可以在一个紧凑的区域完成所有配置")
        else:
            print(f"\n⚠️ 部分配置缺失")
        
        return 所有配置正确
        
    except Exception as e:
        print(f"❌ 功能完整性测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始测试布局集成")
    
    # 测试导入
    导入成功 = test_布局导入()
    
    # 测试配置
    配置状态 = test_配置状态()
    
    # 测试布局顺序
    布局成功 = test_布局顺序()
    
    # 测试UI效果
    UI成功 = test_UI预期效果()
    
    # 测试功能完整性
    功能成功 = test_功能完整性()
    
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    print(f"📦 导入测试: {'✅ 通过' if 导入成功 else '❌ 失败'}")
    print(f"⚙️ 配置测试: {配置状态 if isinstance(配置状态, str) else ('✅ 通过' if 配置状态 else '❌ 失败')}")
    print(f"📐 布局测试: {'✅ 通过' if 布局成功 else '❌ 失败'}")
    print(f"🎨 UI测试: {'✅ 通过' if UI成功 else '❌ 失败'}")
    print(f"🔧 功能测试: {'✅ 通过' if 功能成功 else '❌ 失败'}")
    
    if all([导入成功, 布局成功, UI成功, 功能成功]):
        print("\n🎉 布局集成完成！")
        print("\n💡 现在API密钥输入框已经放在了最合适的位置：")
        print("   📍 位于云端模型选择和云端风格之间")
        print("   🎯 用户选择模型后立即看到密钥输入")
        print("   ✨ 布局更加紧凑和直观")
    else:
        print("\n⚠️ 部分测试失败，需要进一步检查")
    
    print("=" * 60)
