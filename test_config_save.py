#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试配置保存功能
"""

import os
import sys
import json

# 添加项目路径到sys.path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from pyz.任务运行文件 import A0_config

def test_配置读取():
    """测试当前配置读取"""
    print("=" * 60)
    print("测试当前配置读取")
    print("=" * 60)
    
    print(f"📁 配置文件路径: {A0_config.配置文件}")
    print(f"📄 配置文件存在: {'✅' if os.path.exists(A0_config.配置文件) else '❌'}")
    
    # 读取当前配置
    当前Pollinations模型 = A0_config.config.get('pollinations_model', '未设置')
    当前Pollinations风格 = A0_config.config.get('pollinations_style', '未设置')
    当前使用Pollinations = A0_config.config.get('使用Pollinations', False)
    
    print(f"🎯 当前Pollinations模型: {当前Pollinations模型}")
    print(f"🎨 当前Pollinations风格: {当前Pollinations风格}")
    print(f"☁️ 当前使用Pollinations: {当前使用Pollinations}")

def test_配置修改和保存():
    """测试配置修改和保存"""
    print("\n" + "=" * 60)
    print("测试配置修改和保存")
    print("=" * 60)
    
    # 备份原始配置
    原始模型 = A0_config.config.get('pollinations_model', 'flux')
    原始风格 = A0_config.config.get('pollinations_style', '无风格')
    
    print(f"📋 原始模型: {原始模型}")
    print(f"📋 原始风格: {原始风格}")
    
    # 修改配置
    测试模型 = 'kontext'
    测试风格 = '测试风格'
    
    print(f"\n🔧 修改模型为: {测试模型}")
    A0_config.config['pollinations_model'] = 测试模型
    
    print(f"🔧 修改风格为: {测试风格}")
    A0_config.config['pollinations_style'] = 测试风格
    
    # 保存配置
    print(f"\n💾 保存配置...")
    try:
        A0_config.修改配置()
        print("✅ 配置保存成功")
    except Exception as e:
        print(f"❌ 配置保存失败: {e}")
        return False
    
    # 验证保存
    print(f"\n🔍 验证保存结果...")
    
    # 重新读取配置文件
    try:
        with open(A0_config.配置文件, 'r', encoding='utf-8') as f:
            文件配置 = json.load(f)
        
        文件中的模型 = 文件配置.get('pollinations_model', '未找到')
        文件中的风格 = 文件配置.get('pollinations_style', '未找到')
        
        print(f"📄 文件中的模型: {文件中的模型}")
        print(f"📄 文件中的风格: {文件中的风格}")
        
        # 验证是否正确保存
        模型保存正确 = (文件中的模型 == 测试模型)
        风格保存正确 = (文件中的风格 == 测试风格)
        
        print(f"✅ 模型保存正确: {'是' if 模型保存正确 else '否'}")
        print(f"✅ 风格保存正确: {'是' if 风格保存正确 else '否'}")
        
        # 恢复原始配置
        print(f"\n🔄 恢复原始配置...")
        A0_config.config['pollinations_model'] = 原始模型
        A0_config.config['pollinations_style'] = 原始风格
        A0_config.修改配置()
        print("✅ 原始配置已恢复")
        
        return 模型保存正确 and 风格保存正确
        
    except Exception as e:
        print(f"❌ 验证配置失败: {e}")
        return False

def test_模拟UI更换模型():
    """模拟UI更换模型的过程"""
    print("\n" + "=" * 60)
    print("模拟UI更换模型的过程")
    print("=" * 60)
    
    # 模拟UI类
    class 模拟UI:
        def __init__(self):
            # 模拟下拉框
            class 模拟下拉框:
                def __init__(self, 当前文本):
                    self._当前文本 = 当前文本
                
                def currentText(self):
                    return self._当前文本
                
                def setCurrentText(self, 文本):
                    self._当前文本 = 文本
            
            self.Pollinations模型 = 模拟下拉框('flux')
            self.Pollinations风格 = 模拟下拉框('无风格')
    
    # 创建模拟UI实例
    ui = 模拟UI()
    
    # 导入更换函数
    from pyz.UI文件.软件设置UI_methods.更换Pollinations模型 import 更换Pollinations模型
    from pyz.UI文件.软件设置UI_methods.更换Pollinations风格 import 更换Pollinations风格
    
    # 测试模型更换
    print("🧪 测试模型更换...")
    原始模型 = A0_config.config.get('pollinations_model', 'flux')
    
    # 设置新模型
    ui.Pollinations模型.setCurrentText('kontext')
    print(f"📝 UI选择模型: {ui.Pollinations模型.currentText()}")
    
    # 调用更换函数
    更换Pollinations模型(ui)
    
    # 验证结果
    配置中的模型 = A0_config.config.get('pollinations_model')
    print(f"📋 配置中的模型: {配置中的模型}")
    
    if 配置中的模型 == 'kontext':
        print("✅ 模型更换成功")
    else:
        print("❌ 模型更换失败")
    
    # 恢复原始模型
    A0_config.config['pollinations_model'] = 原始模型
    A0_config.修改配置()
    print(f"🔄 已恢复原始模型: {原始模型}")

def test_配置文件完整性():
    """测试配置文件完整性"""
    print("\n" + "=" * 60)
    print("测试配置文件完整性")
    print("=" * 60)
    
    try:
        with open(A0_config.配置文件, 'r', encoding='utf-8') as f:
            配置内容 = json.load(f)
        
        print(f"📊 配置文件包含 {len(配置内容)} 个配置项")
        
        # 检查关键配置项
        关键配置 = [
            'pollinations_model',
            'pollinations_style', 
            '使用Pollinations',
            '图片长度',
            '图片宽度',
            '每张图片数量'
        ]
        
        print(f"\n🔍 检查关键配置项:")
        for 配置名 in 关键配置:
            if 配置名 in 配置内容:
                值 = 配置内容[配置名]
                print(f"   ✅ {配置名}: {值}")
            else:
                print(f"   ❌ {配置名}: 缺失")
        
        return True
        
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始测试配置保存功能")
    
    # 测试配置读取
    test_配置读取()
    
    # 测试配置修改和保存
    保存测试结果 = test_配置修改和保存()
    
    # 模拟UI更换模型
    test_模拟UI更换模型()
    
    # 测试配置文件完整性
    完整性测试结果 = test_配置文件完整性()
    
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    print(f"💾 配置保存测试: {'✅ 通过' if 保存测试结果 else '❌ 失败'}")
    print(f"📄 配置完整性测试: {'✅ 通过' if 完整性测试结果 else '❌ 失败'}")
    
    if 保存测试结果 and 完整性测试结果:
        print("🎉 所有测试通过！配置保存功能正常")
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
    
    print("=" * 60)
