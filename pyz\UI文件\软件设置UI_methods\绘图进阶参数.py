# 绘图进阶参数方法
# 从软件设置UI.py拆分出来的方法

from PyQt5.QtWidgets import QLabel, QComboBox, QLineEdit
from pyz.任务运行文件 import A0_config

def 绘图进阶参数(self):
    self.图片采样方法_label = QLabel('采样方法')
    self.图片采样方法_label.setToolTip('画图时候的算法, 开启极速绘图建议用lcm,euler,euler-a\n没开启建议用DPMPP 2++ Karras')
    self.图片采样方法 = QComboBox()
    self.图片采样方法.setToolTip('画图时候的算法, 开启极速绘图建议用lcm,euler,euler-a\n没开启建议用DPMPP 2++ Karras')
    self.图片采样方法.setCurrentText(A0_config.config['图片采样方法'])
    图片采样方法_options = A0_config.config['采样方法']
    for option in 图片采样方法_options:
        self.图片采样方法.addItem(option)
        if option == A0_config.config['图片采样方法']:
            self.图片采样方法.setCurrentText(option)
    self.调度器_label = QLabel('降噪方法')
    self.调度器_label.setToolTip('Comfy才有, 就是WebUI采样算法后面的"Karras", \n开启极速模式建议选"normal", 没开建议选"Karras"')
    self.调度器 = QComboBox()
    self.调度器.setToolTip('Comfy才有, 就是WebUI采样算法后面的"Karras", \n开启极速模式建议选"normal", 没开建议选"Karras"')
    self.调度器.setCurrentText(A0_config.config['调度器'])
    所有调度器 = A0_config.config['所有调度器']
    for option in 所有调度器:
        self.调度器.addItem(option)
        if option == A0_config.config['调度器']:
            self.调度器.setCurrentText(option)
    self.高清修复算法_label = QLabel('放大模型')
    self.高清修复算法_label.setToolTip('放大图片时的算法模型, 建议选最长的')
    self.高清修复算法 = QComboBox()
    self.高清修复算法.setToolTip('放大图片时的算法模型, 建议选最长的')
    self.高清修复算法.setCurrentText(A0_config.config['高清修复算法'])
    高清修复算法_options = A0_config.config['放大算法']
    for option in 高清修复算法_options:
        self.高清修复算法.addItem(option)
        if option == A0_config.config['高清修复算法']:
            self.高清修复算法.setCurrentText(option)
    self.放大模式_label = QLabel('放大模式')
    self.放大模式_label.setToolTip('直接放大: 通过放大模型放大, 速度快, 质量稍低; \n重绘放大: 根据原图重新画一遍达到放大效果, 速度慢, 效果好; \nWaifu2x放大: 免费在线AI放大, 无需本地显卡, 适合动漫图片')
    self.放大模式 = QComboBox()
    self.放大模式.setToolTip('直接放大: 通过放大模型放大, 速度快, 质量稍低; \n重绘放大: 根据原图重新画一遍达到放大效果, 速度慢, 效果好; \nWaifu2x放大: 免费在线AI放大, 无需本地显卡, 适合动漫图片')
    self.放大模式.setCurrentText(A0_config.config['放大模式'])
    for option in ['重绘放大', '直接放大', 'Waifu2x放大']:
        self.放大模式.addItem(option)
        if option == A0_config.config['放大模式']:
            self.放大模式.setCurrentText(option)
    self.更换VAE模型_label = QLabel('选择 VAE')
    self.更换VAE模型 = QComboBox()
    self.修改vae节点()
    self.正提示词_label = QLabel('正提示词')
    self.正提示词_label.setToolTip('正向提示词, 希望图片中出现的内容, \n会替换掉内置默认正向词, 全局都会生效')
    self.正提示词 = QLineEdit()
    self.正提示词.setToolTip('正向提示词, 希望图片中出现的内容, \n会替换掉内置默认正向词, 全局都会生效')
    self.正提示词.setPlaceholderText('留空用默认值, 填写会替换掉风格中的正提示词')
    self.正提示词.setText(A0_config.config['正提示词'])
    self.反提示词_label = QLabel('反提示词')
    self.反提示词_label.setToolTip('负向提示词, 不希望图片中出现的内容, \n会替换掉内置默认反向词, 全局都会生效')
    self.反提示词 = QLineEdit()
    self.反提示词.setToolTip('负向提示词, 不希望图片中出现的内容, \n会替换掉内置默认反向词, 全局都会生效')
    self.反提示词.setPlaceholderText('留空用默认值, 填写会替换掉风格中的反提示词')
    self.反提示词.setText(A0_config.config['反提示词'])
