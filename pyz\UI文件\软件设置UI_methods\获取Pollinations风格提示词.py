# 获取Pollinations风格提示词工具方法
# 用于在绘图时获取对应模型的风格提示词

from pyz.任务运行文件 import A0_config

def 获取Pollinations风格提示词(模型名称=None, 类型='positive'):
    """
    获取指定Pollinations模型的风格提示词

    参数:
        模型名称: Pollinations模型名称，如果为None则使用当前选中的模型
        类型: 'positive' 或 'negative'

    返回:
        风格提示词字符串
    """
    try:
        # 检查是否启用了风格增强
        if not A0_config.config.get('使用Pollinations风格增强', False):
            return ''

        # 获取模型名称
        if 模型名称 is None:
            模型名称 = A0_config.config.get('pollinations_model', 'flux')

        # 🎨 优先从分类风格系统获取当前选择的风格提示词
        当前风格 = A0_config.config.get('pollinations_style', '无风格')
        if 当前风格 and 当前风格.strip() and 当前风格 != '无风格':
            try:
                from pyz.UI文件.软件设置UI_methods.Pollinations风格分类 import 获取风格提示词 as 获取分类风格提示词

                if 类型 == 'positive':
                    # 清理风格名称（移除可能的缩进空格）
                    清理后风格名 = 当前风格.strip()
                    分类风格提示词 = 获取分类风格提示词(清理后风格名)
                    if 分类风格提示词:
                        print(f"使用分类风格系统的正面提示词: {清理后风格名}")
                        return 分类风格提示词
                elif 类型 == 'negative':
                    # 对于反面提示词，使用通用的默认值
                    print(f"使用分类风格系统的反面提示词")
                    return get_default_negative_prompts()

            except Exception as e:
                print(f"分类风格系统获取失败，回退到传统方式: {e}")

        # 🔄 回退到传统的模型设置方式
        风格设置 = A0_config.config.get('pollinations_style_settings', {})
        模型设置 = 风格设置.get(模型名称, {})

        # 获取对应类型的提示词
        if 类型 == 'positive':
            return 模型设置.get('positive', get_default_positive_prompts())
        elif 类型 == 'negative':
            return 模型设置.get('negative', get_default_negative_prompts())
        else:
            return ''

    except Exception as e:
        print(f"获取Pollinations风格提示词失败: {e}")
        return ''

def get_default_positive_prompts():
    """获取默认正面提示词"""
    return "masterpiece, best quality, ultra detailed, 8k resolution, photorealistic, professional photography, perfect lighting, vibrant colors, sharp focus, high contrast, detailed textures, cinematic composition, award winning photography"

def get_default_negative_prompts():
    """获取默认反面提示词（原始格式，用于转换为not格式）"""
    return "low quality, blurry, distorted, watermark, signature, text, logo, worst quality, low resolution, pixelated, artifacts, noise, oversaturated, undersaturated, overexposed, underexposed, bad anatomy, deformed, mutated"

def 转换负面词为not格式(负面词):
    """
    将负面提示词转换为not格式

    参数:
        负面词: 原始负面提示词字符串

    返回:
        转换后的not格式字符串
    """
    if not 负面词 or not 负面词.strip():
        return ""

    # 分割负面词
    词汇列表 = [词.strip() for 词 in 负面词.split(',') if 词.strip()]

    # 转换为not格式
    not格式词汇 = [f"not {词}" for 词 in 词汇列表]

    # 用逗号连接
    return ", ".join(not格式词汇)

def 应用Pollinations风格提示词(原始提示词, 模型名称=None):
    """
    将风格提示词应用到原始提示词中

    参数:
        原始提示词: 原始的绘图提示词
        模型名称: Pollinations模型名称

    返回:
        包含风格提示词的完整提示词
    """
    try:
        # 🛡️ 修复重复拼接：在应用风格提示词前先清理原始提示词中的重复内容
        if 原始提示词.strip():
            # 导入清理函数
            try:
                import sys
                import os
                sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '任务运行文件'))
                from A0_通用函数 import 清理重复内容

                # 清理原始提示词中的重复内容
                清理后提示词 = 清理重复内容(原始提示词)
                if 清理后提示词 != 原始提示词:
                    print(f"[风格应用] 清理了重复内容，原长度:{len(原始提示词)}，清理后长度:{len(清理后提示词)}")
                原始提示词 = 清理后提示词
            except Exception as e:
                print(f"[风格应用] 清理重复内容失败: {e}")

        # 获取正面风格提示词
        正面风格 = 获取Pollinations风格提示词(模型名称, 'positive')

        if 正面风格:
            # 如果原始提示词不为空，在前面添加风格提示词
            if 原始提示词.strip():
                return f"{正面风格}, {原始提示词}"
            else:
                return 正面风格
        else:
            return 原始提示词

    except Exception as e:
        print(f"应用Pollinations风格提示词失败: {e}")
        return 原始提示词

def 获取Pollinations反面提示词(模型名称=None):
    """
    获取Pollinations模型的反面提示词

    参数:
        模型名称: Pollinations模型名称

    返回:
        反面提示词字符串
    """
    return 获取Pollinations风格提示词(模型名称, 'negative')

def 检查Pollinations风格设置(模型名称=None):
    """
    检查指定模型是否有自定义风格设置

    参数:
        模型名称: Pollinations模型名称

    返回:
        True/False
    """
    try:
        if 模型名称 is None:
            模型名称 = A0_config.config.get('pollinations_model', 'flux')

        风格设置 = A0_config.config.get('pollinations_style_settings', {})
        return 模型名称 in 风格设置

    except Exception as e:
        print(f"检查Pollinations风格设置失败: {e}")
        return False

def 获取所有Pollinations风格设置():
    """
    获取所有Pollinations模型的风格设置

    返回:
        字典，包含所有模型的风格设置
    """
    try:
        return A0_config.config.get('pollinations_style_settings', {})
    except Exception as e:
        print(f"获取所有Pollinations风格设置失败: {e}")
        return {}

def 重置Pollinations风格设置(模型名称=None):
    """
    重置指定模型的风格设置为默认值

    参数:
        模型名称: Pollinations模型名称，如果为None则重置所有
    """
    try:
        风格设置 = A0_config.config.get('pollinations_style_settings', {})

        默认设置 = {
            'positive': get_default_positive_prompts(),
            'negative': get_default_negative_prompts()
        }

        if 模型名称:
            风格设置[模型名称] = 默认设置
        else:
            # 重置所有模型
            for 模型 in 风格设置.keys():
                风格设置[模型] = 默认设置.copy()

        A0_config.config['pollinations_style_settings'] = 风格设置
        A0_config.修改配置()

        print(f"已重置Pollinations风格设置: {模型名称 or '所有模型'}")

    except Exception as e:
        print(f"重置Pollinations风格设置失败: {e}")
