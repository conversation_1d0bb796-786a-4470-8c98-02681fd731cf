#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试四个角色文件名解析功能
"""

import os
import sys
import re

# 添加项目路径到sys.path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_四个角色文件名解析():
    """测试四个角色文件名解析功能"""
    print("=" * 60)
    print("测试四个角色文件名解析功能")
    print("=" * 60)
    
    # 模拟四个角色的文件名格式
    测试文件名 = [
        "左上角艾米丽_右上角露西_左下角斑斑_右下角小明.png",
        "左上角小红_右上角小绿_左下角小蓝_右下角小黄.png",
        "左上角A_右上角B_左下角C_右下角D.jpg",
        "左边艾米丽_右边露西.png",  # 两个角色
        "左边艾米丽_中间露西_右边斑斑.png",  # 三个角色
        "艾米丽_露西_斑斑_小明.png",  # 无位置信息的四个角色
    ]
    
    for 文件名 in 测试文件名:
        print(f"\n🧪 测试文件名: {文件名}")
        
        # 使用与实际代码相同的解析逻辑
        文件中的角色 = []
        
        # 匹配模式：支持多种位置描述
        角色匹配 = re.findall(r'(?:左边|中间|右边|左上角|右上角|左下角|右下角)([^_\.]+)', 文件名)
        if 角色匹配:
            文件中的角色 = 角色匹配
            print(f"✅ 使用位置匹配解析出角色: {文件中的角色}")
        else:
            # 如果没有位置信息，尝试直接按下划线分割
            name_without_ext = 文件名.replace('.png', '').replace('.jpg', '').replace('.jpeg', '')
            文件中的角色 = [part for part in name_without_ext.split('_') if part and not any(part.startswith(pos) for pos in ['左边', '中间', '右边', '左上角', '右上角', '左下角', '右下角'])]
            print(f"✅ 使用下划线分割解析出角色: {文件中的角色}")
        
        print(f"📊 解析出 {len(文件中的角色)} 个角色")

def test_四个角色匹配逻辑():
    """测试四个角色的匹配逻辑"""
    print("\n" + "=" * 60)
    print("测试四个角色的匹配逻辑")
    print("=" * 60)
    
    # 模拟请求的角色组合
    请求角色组合 = [
        ["艾米丽", "露西", "斑斑", "小明"],
        ["小红", "小绿", "小蓝", "小黄"],
        ["A", "B", "C", "D"],
        ["艾米丽", "露西"],  # 两个角色
        ["艾米丽", "露西", "斑斑"],  # 三个角色
    ]
    
    # 模拟文件中的角色组合
    文件角色组合 = [
        (["艾米丽", "露西", "斑斑", "小明"], "左上角艾米丽_右上角露西_左下角斑斑_右下角小明.png"),
        (["小红", "小绿", "小蓝", "小黄"], "左上角小红_右上角小绿_左下角小蓝_右下角小黄.png"),
        (["A", "B", "C", "D"], "左上角A_右上角B_左下角C_右下角D.jpg"),
        (["艾米丽", "露西"], "左边艾米丽_右边露西.png"),
        (["艾米丽", "露西", "斑斑"], "左边艾米丽_中间露西_右边斑斑.png"),
    ]
    
    for 请求角色 in 请求角色组合:
        print(f"\n🧪 请求角色组合: {请求角色}")
        
        找到匹配 = False
        for 文件角色, 文件名 in 文件角色组合:
            # 检查角色列表是否完全匹配（不区分顺序）
            if set(请求角色) == set(文件角色):
                print(f"✅ 找到完全匹配: {文件名}")
                print(f"   请求角色: {sorted(请求角色)}")
                print(f"   文件角色: {sorted(文件角色)}")
                找到匹配 = True
                break
        
        if not 找到匹配:
            print("❌ 未找到匹配的文件")

def test_四个角色固定词生成():
    """测试四个角色固定词生成"""
    print("\n" + "=" * 60)
    print("测试四个角色固定词生成")
    print("=" * 60)
    
    from pyz.工作流.A7_Pollinations工作流 import 生成角色参考固定词
    
    模拟URL = "https://example.com/four_characters.png"
    
    测试案例 = [
        ("艾米丽,露西,斑斑,小明", "四个角色"),
        ("小红,小绿,小蓝,小黄", "四个角色（不同名字）"),
        ("A,B,C,D", "四个角色（简单名字）"),
    ]
    
    for 角色名, 描述 in 测试案例:
        print(f"\n🧪 测试{描述}: {角色名}")
        
        固定词 = 生成角色参考固定词(角色名, 模拟URL)
        print(f"📝 生成的固定词: {固定词}")
        
        # 验证是否包含四个角色的位置描述
        if "upper left" in 固定词 and "upper right" in 固定词 and "lower left" in 固定词 and "lower right" in 固定词:
            print("✅ 包含所有四个位置描述")
            
            # 验证是否包含所有角色名
            角色列表 = 角色名.split(',')
            所有角色都在 = all(角色.strip() in 固定词 for 角色 in 角色列表)
            if 所有角色都在:
                print("✅ 包含所有角色名")
            else:
                print("❌ 缺少部分角色名")
        else:
            print("❌ 缺少位置描述")

if __name__ == "__main__":
    print("🚀 开始测试四个角色功能")
    
    # 测试文件名解析
    test_四个角色文件名解析()
    
    # 测试匹配逻辑
    test_四个角色匹配逻辑()
    
    # 测试固定词生成
    test_四个角色固定词生成()
    
    print("\n" + "=" * 60)
    print("✅ 测试完成")
    print("=" * 60)
