#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试方法绑定
"""

import os
import sys

# 添加项目路径到sys.path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_方法导入():
    """测试方法导入"""
    print("=" * 60)
    print("测试方法导入")
    print("=" * 60)
    
    try:
        # 测试导入装饰器
        from pyz.UI文件.软件设置UI_methods.导入所有方法 import 添加所有方法到类
        print("✅ 装饰器导入成功")
        
        # 测试导入密钥方法
        from pyz.UI文件.软件设置UI_methods.更新Pollinations密钥 import 更新Pollinations密钥, 切换Pollinations密钥显示
        print("✅ 密钥方法导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_方法绑定():
    """测试方法绑定"""
    print("\n" + "=" * 60)
    print("测试方法绑定")
    print("=" * 60)
    
    try:
        # 创建一个测试类
        class 测试UI类:
            pass
        
        # 应用装饰器
        from pyz.UI文件.软件设置UI_methods.导入所有方法 import 添加所有方法到类
        测试UI类 = 添加所有方法到类(测试UI类)
        
        # 检查密钥方法是否被绑定
        密钥方法列表 = [
            '更新Pollinations密钥',
            '切换Pollinations密钥显示'
        ]
        
        绑定成功 = True
        for 方法名 in 密钥方法列表:
            if hasattr(测试UI类, 方法名):
                print(f"✅ 方法已绑定: {方法名}")
            else:
                print(f"❌ 方法未绑定: {方法名}")
                绑定成功 = False
        
        return 绑定成功
        
    except Exception as e:
        print(f"❌ 方法绑定测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_配置保存():
    """测试配置保存功能"""
    print("\n" + "=" * 60)
    print("测试配置保存功能")
    print("=" * 60)
    
    try:
        from pyz.任务运行文件 import A0_config
        
        # 备份原配置
        原密钥 = A0_config.config.get('pollinations_api_key', '')
        
        # 测试设置密钥
        测试密钥 = "sk-test123456789abcdef"
        print(f"🧪 设置测试密钥: {测试密钥}")
        
        A0_config.config['pollinations_api_key'] = 测试密钥
        A0_config.修改配置()
        
        # 重新读取配置验证
        A0_config.重新加载配置()
        验证密钥 = A0_config.config.get('pollinations_api_key', '')
        
        if 验证密钥 == 测试密钥:
            print("✅ 配置保存和读取成功")
            保存成功 = True
        else:
            print(f"❌ 配置保存失败，期望: {测试密钥}，实际: {验证密钥}")
            保存成功 = False
        
        # 恢复原配置
        A0_config.config['pollinations_api_key'] = 原密钥
        A0_config.修改配置()
        
        return 保存成功
        
    except Exception as e:
        print(f"❌ 配置保存测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_模拟UI调用():
    """测试模拟UI调用"""
    print("\n" + "=" * 60)
    print("测试模拟UI调用")
    print("=" * 60)
    
    try:
        from PyQt5.QtWidgets import QApplication, QLineEdit
        from pyz.任务运行文件 import A0_config
        
        # 创建应用（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建模拟UI类
        class 模拟UI:
            def __init__(self):
                self.Pollinations密钥 = QLineEdit()
                self.Pollinations密钥显示 = None
        
        # 应用装饰器
        from pyz.UI文件.软件设置UI_methods.导入所有方法 import 添加所有方法到类
        模拟UI = 添加所有方法到类(模拟UI)
        
        # 创建实例
        ui实例 = 模拟UI()
        
        # 设置测试密钥
        测试密钥 = "sk-ui-test-123456"
        ui实例.Pollinations密钥.setText(测试密钥)
        
        print(f"🧪 通过UI设置密钥: {测试密钥}")
        
        # 调用更新方法
        ui实例.更新Pollinations密钥()
        
        # 验证结果
        保存的密钥 = A0_config.config.get('pollinations_api_key', '')
        
        if 保存的密钥 == 测试密钥:
            print("✅ UI方法调用成功，密钥已保存")
            调用成功 = True
        else:
            print(f"❌ UI方法调用失败，期望: {测试密钥}，实际: {保存的密钥}")
            调用成功 = False
        
        # 清理
        A0_config.config['pollinations_api_key'] = ''
        A0_config.修改配置()
        
        return 调用成功
        
    except Exception as e:
        print(f"❌ 模拟UI调用测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_实际工作流调用():
    """测试实际工作流调用"""
    print("\n" + "=" * 60)
    print("测试实际工作流调用")
    print("=" * 60)
    
    try:
        from pyz.任务运行文件 import A0_config
        
        # 设置测试环境
        测试密钥 = "sk-workflow-test-987654"
        A0_config.config['pollinations_api_key'] = 测试密钥
        A0_config.config['使用Pollinations'] = True
        A0_config.config['pollinations_model'] = 'kontext'
        
        print(f"🧪 设置测试环境:")
        print(f"   🔑 API密钥: {测试密钥}")
        print(f"   ☁️ 启用Pollinations: True")
        print(f"   🎯 模型: kontext")
        
        # 模拟工作流中的参数构建
        params = {}
        
        # 获取API密钥（模拟工作流逻辑）
        api_key = A0_config.config.get('pollinations_api_key', '').strip()
        if api_key:
            params['token'] = api_key
            print(f"   ✅ 添加token参数")
        else:
            print(f"   ❌ 未添加token参数")
        
        # 添加其他参数
        params.update({
            'model': 'kontext',
            'width': 1280,
            'height': 960
        })
        
        print(f"\n🌐 构建的API参数:")
        for key, value in params.items():
            if key == 'token':
                print(f"   - {key}: {'*' * len(str(value))} 🔑 API密钥")
            else:
                print(f"   - {key}: {value}")
        
        # 检查结果
        包含token = 'token' in params
        token正确 = params.get('token') == 测试密钥 if 包含token else False
        
        print(f"\n📊 验证结果:")
        print(f"   🔑 包含token: {'✅' if 包含token else '❌'}")
        print(f"   🎯 token正确: {'✅' if token正确 else '❌'}")
        
        # 清理
        A0_config.config['pollinations_api_key'] = ''
        
        return 包含token and token正确
        
    except Exception as e:
        print(f"❌ 实际工作流调用测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 开始测试方法绑定")
    
    # 测试导入
    导入成功 = test_方法导入()
    
    # 测试绑定
    绑定成功 = test_方法绑定()
    
    # 测试配置保存
    配置成功 = test_配置保存()
    
    # 测试UI调用
    UI成功 = test_模拟UI调用()
    
    # 测试工作流调用
    工作流成功 = test_实际工作流调用()
    
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    print(f"📦 方法导入: {'✅ 通过' if 导入成功 else '❌ 失败'}")
    print(f"🔗 方法绑定: {'✅ 通过' if 绑定成功 else '❌ 失败'}")
    print(f"💾 配置保存: {'✅ 通过' if 配置成功 else '❌ 失败'}")
    print(f"🎨 UI调用: {'✅ 通过' if UI成功 else '❌ 失败'}")
    print(f"⚙️ 工作流调用: {'✅ 通过' if 工作流成功 else '❌ 失败'}")
    
    if all([导入成功, 绑定成功, 配置成功, UI成功, 工作流成功]):
        print("\n🎉 所有测试通过！")
        print("\n💡 现在您可以:")
        print("1. 在软件设置中输入API密钥")
        print("2. 密钥会正确保存到配置文件")
        print("3. 工作流会正确使用API密钥")
        print("4. 生成图片时会包含token参数")
    else:
        print("\n⚠️ 部分测试失败，需要进一步检查")
    
    print("=" * 60)
