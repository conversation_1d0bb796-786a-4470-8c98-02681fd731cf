#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Pollinations API密钥功能
"""

import os
import sys

# 添加项目路径到sys.path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from pyz.任务运行文件 import A0_config

def test_API密钥配置():
    """测试API密钥配置功能"""
    print("=" * 80)
    print("测试Pollinations API密钥配置")
    print("=" * 80)
    
    # 检查当前配置
    当前密钥 = A0_config.config.get('pollinations_api_key', '')
    print(f"📋 当前配置的API密钥: {'已设置' if 当前密钥 else '未设置'}")
    
    if 当前密钥:
        print(f"🔑 密钥长度: {len(当前密钥)} 字符")
        print(f"🔑 密钥预览: {当前密钥[:4]}...{当前密钥[-4:] if len(当前密钥) > 8 else ''}")
    
    # 测试密钥设置
    测试密钥 = "test_api_key_12345678"
    print(f"\n🧪 测试设置API密钥: {测试密钥}")
    
    # 备份原密钥
    原密钥 = 当前密钥
    
    # 设置测试密钥
    A0_config.config['pollinations_api_key'] = 测试密钥
    A0_config.修改配置()
    
    # 验证设置
    验证密钥 = A0_config.config.get('pollinations_api_key', '')
    if 验证密钥 == 测试密钥:
        print("✅ API密钥设置成功")
    else:
        print("❌ API密钥设置失败")
    
    # 恢复原密钥
    A0_config.config['pollinations_api_key'] = 原密钥
    A0_config.修改配置()
    print(f"🔄 已恢复原密钥设置")

def test_API参数构建():
    """测试API参数构建（包含token）"""
    print("\n" + "=" * 80)
    print("测试API参数构建")
    print("=" * 80)
    
    # 模拟不同的密钥情况
    测试场景 = [
        ("无密钥", ""),
        ("有密钥", "sk-test123456789abcdef"),
        ("短密钥", "short"),
        ("长密钥", "very_long_api_key_with_many_characters_1234567890")
    ]
    
    for 场景名, 测试密钥 in 测试场景:
        print(f"\n🧪 测试场景: {场景名}")
        print(f"🔑 测试密钥: {测试密钥 if 测试密钥 else '(空)'}")
        
        # 临时设置密钥
        原密钥 = A0_config.config.get('pollinations_api_key', '')
        A0_config.config['pollinations_api_key'] = 测试密钥
        
        # 模拟参数构建
        params = {}
        
        # 添加token（如果有）
        api_key = A0_config.config.get('pollinations_api_key', '').strip()
        if api_key:
            params['token'] = api_key
            print(f"   🔑 添加token参数 (长度: {len(api_key)} 字符)")
        else:
            print(f"   ⚪ 无token参数")
        
        # 添加其他参数
        params.update({
            'model': 'kontext',
            'width': 1280,
            'height': 960,
            'seed': 123456
        })
        
        print(f"   📊 参数总数: {len(params)}")
        print(f"   🌐 参数列表:")
        for key, value in params.items():
            if key == 'token':
                print(f"      - {key}: {'*' * min(len(str(value)), 20)} 🔑")
            else:
                print(f"      - {key}: {value}")
        
        # 检查token位置
        参数键列表 = list(params.keys())
        if 'token' in 参数键列表:
            token位置 = 参数键列表.index('token')
            if token位置 == 0:
                print(f"   ✅ token参数在第一位")
            else:
                print(f"   ⚠️ token参数在第{token位置 + 1}位")
        
        # 恢复原密钥
        A0_config.config['pollinations_api_key'] = 原密钥

def test_URL构建():
    """测试包含token的URL构建"""
    print("\n" + "=" * 80)
    print("测试包含token的URL构建")
    print("=" * 80)
    
    # 设置测试密钥
    测试密钥 = "sk-test123456789"
    原密钥 = A0_config.config.get('pollinations_api_key', '')
    A0_config.config['pollinations_api_key'] = 测试密钥
    
    try:
        from urllib.parse import quote
        
        # 模拟完整的URL构建过程
        提示词 = "A beautiful landscape"
        编码提示词 = quote(提示词)
        
        base_url = f"https://image.pollinations.ai/prompt/{编码提示词}"
        
        # 构建参数
        params = {}
        
        # 添加token
        api_key = A0_config.config.get('pollinations_api_key', '').strip()
        if api_key:
            params['token'] = api_key
        
        # 添加其他参数
        params.update({
            'model': 'kontext',
            'width': 1280,
            'height': 960,
            'seed': 123456,
            'enhance': 'true',
            'safe': 'true'
        })
        
        # 构建参数字符串
        param_strings = []
        for key, value in params.items():
            param_strings.append(f"{key}={value}")
        
        完整URL = f"{base_url}?{'&'.join(param_strings)}"
        
        print(f"📝 基础URL: {base_url}")
        print(f"🔗 参数字符串: {'&'.join(param_strings)}")
        print(f"📊 完整URL长度: {len(完整URL)} 字符")
        
        # 检查token是否在URL中
        if 'token=' in 完整URL:
            print("✅ URL中包含token参数")
            
            # 检查token位置
            if 完整URL.find('token=') < 完整URL.find('model='):
                print("✅ token参数在model参数之前")
            else:
                print("⚠️ token参数不在第一位")
        else:
            print("❌ URL中缺少token参数")
        
        # 显示URL（隐藏token值）
        显示URL = 完整URL.replace(测试密钥, '*' * len(测试密钥))
        print(f"🔗 完整URL (隐藏token): {显示URL}")
        
    finally:
        # 恢复原密钥
        A0_config.config['pollinations_api_key'] = 原密钥

def test_kontext模型认证():
    """测试kontext模型的认证需求"""
    print("\n" + "=" * 80)
    print("测试kontext模型认证需求")
    print("=" * 80)
    
    # 检查当前配置
    当前模型 = A0_config.config.get('pollinations_model', 'flux')
    当前密钥 = A0_config.config.get('pollinations_api_key', '')
    使用Pollinations = A0_config.config.get('使用Pollinations', False)
    
    print(f"📋 当前配置:")
    print(f"   🎯 模型: {当前模型}")
    print(f"   🔑 API密钥: {'已设置' if 当前密钥 else '未设置'}")
    print(f"   ☁️ 启用Pollinations: {使用Pollinations}")
    
    # 分析配置状态
    if 当前模型 == 'kontext':
        print(f"\n🎯 使用kontext模型:")
        if 当前密钥:
            print(f"   ✅ 已设置API密钥，应该可以正常使用")
        else:
            print(f"   ⚠️ 未设置API密钥，可能会遇到认证错误")
            print(f"   💡 建议设置API密钥：https://auth.pollinations.ai")
    else:
        print(f"\n🔍 使用{当前模型}模型:")
        if 当前密钥:
            print(f"   ✅ 已设置API密钥，可以使用高级功能")
        else:
            print(f"   ⚪ 未设置API密钥，使用免费功能")

if __name__ == "__main__":
    print("🚀 开始测试Pollinations API密钥功能")
    
    # 测试API密钥配置
    test_API密钥配置()
    
    # 测试API参数构建
    test_API参数构建()
    
    # 测试URL构建
    test_URL构建()
    
    # 测试kontext模型认证
    test_kontext模型认证()
    
    print("\n" + "=" * 80)
    print("✅ 测试完成")
    print("=" * 80)
    
    print("\n💡 使用说明:")
    print("1. 在软件设置中找到'API密钥'输入框")
    print("2. 输入您的Pollinations API密钥")
    print("3. 密钥会自动保存并在请求中使用")
    print("4. 获取密钥：https://auth.pollinations.ai")
    print("5. 有了密钥就可以使用kontext等高级模型了")
