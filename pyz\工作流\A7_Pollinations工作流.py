#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Pollinations工作流 - 基于云端AI的图像生成工作流
支持多种先进的AI模型，包括Flux、DALL-E等
"""

import json
import copy
import os
from pyz.任务运行文件 import A0_config

# Pollinations支持的模型列表
POLLINATIONS_MODELS = [
    "flux",
    "flux-realism",
    "flux-cablyai",
    "flux-anime",
    "flux-3d",
    "any-dark",
    "flux-pro",
    "turbo",
    "kontext",  # 新增kontext模型
    "dall-e-3",
    "playground-v2.5",
    "dreamshaper-xl",
    "juggernaut-xl-v9",
    "realvisxl-v4.0",
    "stable-diffusion-xl",
    "stable-diffusion-3",
    "midjourney",
    "leonardo"
]

# Pollinations风格映射
POLLINATIONS_STYLES = {
    "无风格": "",

    # 动漫系列
    "日式动漫": "anime style, manga style, Japanese animation, cel shading, Studio Ghibli style",
    "国漫风格": "Chinese animation style, donghua, Chinese manga, oriental anime, traditional Chinese art influence",
    "韩漫风格": "Korean webtoon style, manhwa, Korean animation, soft colors, romantic style",
    "美式卡通": "western cartoon style, Disney style, Pixar animation, American animation",
    "二次元": "2D anime style, otaku culture, moe style, kawaii, Japanese pop culture",

    # 写实系列
    "照片写实": "photorealistic, realistic, detailed, high quality photography, lifelike",
    "电影级": "cinematic, movie quality, film photography, dramatic lighting, professional",
    "人像摄影": "portrait photography, professional headshot, studio lighting, detailed face",

    # 艺术系列
    "古典油画": "classical oil painting, Renaissance style, baroque art, traditional painting",
    "印象派": "impressionist style, soft brushstrokes, light and color, Monet style",
    "水彩画": "watercolor painting, soft colors, flowing, artistic brush strokes, transparent",
    "素描风格": "pencil sketch, line art, black and white, hand-drawn, charcoal drawing",
    "版画风格": "woodcut style, printmaking, bold lines, high contrast",

    # 现代风格
    "赛博朋克": "cyberpunk, neon lights, futuristic, sci-fi, dystopian, high-tech",
    "蒸汽朋克": "steampunk, Victorian era, brass and copper, mechanical, retro-futuristic",
    "波普艺术": "pop art style, bright colors, comic book style, Andy Warhol influence",
    "极简主义": "minimalist style, clean lines, simple composition, modern design",

    # 幻想系列
    "奇幻风格": "fantasy art, magical, mystical, ethereal, enchanted, dragons and magic",
    "暗黑风格": "dark fantasy, gothic, horror elements, dramatic shadows, mysterious",
    "童话风格": "fairy tale style, whimsical, magical creatures, storybook illustration",

    # 文化风格
    "中国风": "Chinese traditional style, ink painting, oriental art, classical Chinese aesthetics",
    "日式和风": "Japanese traditional style, ukiyo-e, traditional Japanese art, zen aesthetics",
    "欧式古典": "European classical style, baroque, rococo, neoclassical art",
    "美式复古": "American vintage, retro style, 1950s aesthetic, pin-up style",

    # 特殊效果
    "霓虹风格": "neon style, glowing effects, electric colors, synthwave, retrowave",
    "像素艺术": "pixel art, 8-bit style, retro gaming, pixelated, digital art",
    "低多边形": "low poly style, geometric, 3D modeling, faceted surfaces",
    "毛玻璃": "frosted glass effect, translucent, soft focus, dreamy atmosphere"
}

# 模型分类和描述
MODEL_CATEGORIES = {
    "flux": {
        "category": "通用模型",
        "description": "Flux基础模型，平衡质量和速度",
        "recommended": True,
        "quality": "高",
        "speed": "快"
    },
    "flux-realism": {
        "category": "写实模型",
        "description": "Flux写实风格，适合真实人物和场景",
        "recommended": True,
        "quality": "极高",
        "speed": "中"
    },
    "flux-cablyai": {
        "category": "艺术模型",
        "description": "Flux艺术风格，适合创意作品",
        "recommended": False,
        "quality": "高",
        "speed": "中"
    },
    "flux-anime": {
        "category": "动漫模型",
        "description": "Flux动漫风格，适合二次元角色",
        "recommended": True,
        "quality": "高",
        "speed": "快"
    },
    "flux-3d": {
        "category": "3D模型",
        "description": "Flux 3D风格，适合立体效果",
        "recommended": False,
        "quality": "高",
        "speed": "中"
    },
    "any-dark": {
        "category": "暗黑模型",
        "description": "暗黑风格，适合神秘主题",
        "recommended": False,
        "quality": "中",
        "speed": "快"
    },
    "flux-pro": {
        "category": "专业模型",
        "description": "Flux专业版，最高质量",
        "recommended": True,
        "quality": "极高",
        "speed": "慢"
    },
    "turbo": {
        "category": "快速模型",
        "description": "极速生成，适合快速预览",
        "recommended": False,
        "quality": "中",
        "speed": "极快"
    },
    "dall-e-3": {
        "category": "OpenAI模型",
        "description": "OpenAI DALL-E 3，创意表现优秀",
        "recommended": True,
        "quality": "极高",
        "speed": "慢"
    },
    "playground-v2.5": {
        "category": "通用模型",
        "description": "Playground v2.5，平衡性能",
        "recommended": False,
        "quality": "高",
        "speed": "中"
    },
    "dreamshaper-xl": {
        "category": "艺术模型",
        "description": "DreamShaper XL，梦幻风格",
        "recommended": False,
        "quality": "高",
        "speed": "中"
    },
    "juggernaut-xl-v9": {
        "category": "写实模型",
        "description": "Juggernaut XL v9，写实效果佳",
        "recommended": False,
        "quality": "高",
        "speed": "中"
    },
    "realvisxl-v4.0": {
        "category": "写实模型",
        "description": "RealVis XL v4.0，超写实效果",
        "recommended": False,
        "quality": "极高",
        "speed": "慢"
    },
    "stable-diffusion-xl": {
        "category": "通用模型",
        "description": "Stable Diffusion XL，经典选择",
        "recommended": False,
        "quality": "高",
        "speed": "中"
    },
    "stable-diffusion-3": {
        "category": "通用模型",
        "description": "Stable Diffusion 3，最新版本",
        "recommended": True,
        "quality": "极高",
        "speed": "中"
    },
    "midjourney": {
        "category": "艺术模型",
        "description": "Midjourney风格，艺术感强",
        "recommended": True,
        "quality": "极高",
        "speed": "慢"
    },
    "leonardo": {
        "category": "艺术模型",
        "description": "Leonardo AI，创意丰富",
        "recommended": False,
        "quality": "高",
        "speed": "中"
    }
}

def 翻译角色名(角色名):
    """
    将中文角色名翻译为英文

    参数:
        角色名: 中文角色名

    返回:
        str: 英文角色名，翻译失败时返回原名
    """
    try:
        from translate import Translator

        # 创建翻译器实例
        translator = Translator(from_lang="zh", to_lang="en")

        # 直接使用翻译库翻译
        translated = translator.translate(角色名)
        if translated and translated.strip():
            return translated.strip()
        else:
            return 角色名

    except Exception as e:
        print(f"⚠️ 角色名翻译失败: {e}")
        return 角色名

def 生成角色参考固定词(角色名, 参考图URL):
    """
    根据角色信息生成参考图固定描述词

    参数:
        角色名: 角色名称，支持多角色用逗号分隔
        参考图URL: 参考图URL

    返回:
        str: 角色参考固定词，无参考图时返回空字符串
    """
    if not 参考图URL or not 角色名 or 角色名.strip() == "" or 角色名.strip() == "全局":
        return ""

    # 处理角色名并翻译为英文
    角色列表 = [翻译角色名(角色.strip()) for 角色 in 角色名.split(',') if 角色.strip() and 角色.strip() != "全局"]

    if not 角色列表:
        return ""

    # 单个角色
    if len(角色列表) == 1:
        return "Same character and costume as the reference image"

    # 多个角色 - 根据角色数量生成位置描述
    else:
        if len(角色列表) == 2:
            # 两个角色：左边和右边
            return f"Refer to the picture for introduction. The character on the left is {角色列表[0]}, and the character on the right is {角色列表[1]}."
        elif len(角色列表) == 3:
            # 三个角色：左边、中间、右边
            return f"Refer to the picture for introduction. The character on the left is {角色列表[0]}, the character in the middle is {角色列表[1]}, and the character on the right is {角色列表[2]}."
        elif len(角色列表) == 4:
            # 四个角色：左上角、右上角、左下角、右下角
            return f"Refer to the picture for introduction. The character in the upper left is {角色列表[0]}, the character in the upper right is {角色列表[1]}, the character in the lower left is {角色列表[2]}, and the character in the lower right is {角色列表[3]}."
        else:
            # 超过4个角色的情况，使用通用描述
            角色描述 = ", ".join([f"{角色}" for 角色 in 角色列表])
            return f"Refer to the picture for introduction. The characters are {角色描述}."

def 获取角色参考图URL(项目路径, 角色名):
    """
    从upload_records.json获取角色参考图的URL

    参数:
        项目路径: 项目路径（可能是图片文件夹路径）
        角色名: 角色名称，支持多角色用逗号分隔

    返回:
        str: 参考图URL，无角色或找不到时返回空字符串
    """
    if not 角色名 or 角色名.strip() == "" or 角色名.strip() == "全局":
        return ""

    try:
        # 如果传入的是图片文件夹路径，需要获取项目根目录
        if 项目路径.endswith('图片文件'):
            项目根目录 = os.path.dirname(项目路径)
        else:
            项目根目录 = 项目路径

        # 构建upload_records.json路径
        upload_records_path = os.path.join(项目根目录, "角色参考图片", "upload_records.json")

        if not os.path.exists(upload_records_path):
            print(f"⚠️ 未找到upload_records.json: {upload_records_path}")
            return ""

        # 读取upload_records.json
        with open(upload_records_path, 'r', encoding='utf-8') as f:
            upload_data = json.load(f)

        # 处理角色名
        角色列表 = [角色.strip() for 角色 in 角色名.split(',') if 角色.strip() and 角色.strip() != "全局"]

        if not 角色列表:
            return ""

        # 单个角色的情况
        if len(角色列表) == 1:
            角色 = 角色列表[0]
            if 角色 in upload_data and 'single' in upload_data[角色]:
                single_images = upload_data[角色]['single']
                if single_images:
                    # 返回第一张图片的URL
                    return single_images[0].get('upload_url', '')

        # 多个角色的情况，查找拼接图片
        else:
            if '拼接图片' in upload_data and 'combined' in upload_data['拼接图片']:
                combined_images = upload_data['拼接图片']['combined']

                # 遍历所有拼接图片，找到名字完全匹配的图片
                for img_info in combined_images:
                    original_name = img_info.get('original_name', '')

                    # 从文件名中提取角色名（去掉位置信息）
                    # 支持多种格式：
                    # 2个角色："左边艾米丽_右边露西.png" -> ["艾米丽", "露西"]
                    # 3个角色："左边艾米丽_中间露西_右边斑斑.png" -> ["艾米丽", "露西", "斑斑"]
                    # 4个角色："左上角艾米丽_右上角露西_左下角斑斑_右下角小明.png" -> ["艾米丽", "露西", "斑斑", "小明"]
                    文件中的角色 = []
                    import re

                    # 匹配模式：支持多种位置描述
                    # 左边/中间/右边 + 角色名 (2-3个角色)
                    # 左上角/右上角/左下角/右下角 + 角色名 (4个角色)
                    角色匹配 = re.findall(r'(?:左边|中间|右边|左上角|右上角|左下角|右下角)([^_\.]+)', original_name)
                    if 角色匹配:
                        文件中的角色 = 角色匹配
                    else:
                        # 如果没有位置信息，尝试直接按下划线分割
                        # 例如："艾米丽_露西.png" -> ["艾米丽", "露西"]
                        name_without_ext = original_name.replace('.png', '').replace('.jpg', '').replace('.jpeg', '')
                        文件中的角色 = [part for part in name_without_ext.split('_') if part and not any(part.startswith(pos) for pos in ['左边', '中间', '右边', '左上角', '右上角', '左下角', '右下角'])]

                    # 检查角色列表是否完全匹配（不区分顺序）
                    if set(角色列表) == set(文件中的角色):
                        print(f"✅ 找到完全匹配的多角色拼接图片: {original_name}")
                        print(f"   请求角色: {sorted(角色列表)}")
                        print(f"   文件角色: {sorted(文件中的角色)}")
                        return img_info.get('upload_url', '')

                print(f"⚠️ 未找到完全匹配的拼接图片")
                print(f"   请求角色: {sorted(角色列表)}")
                print("   可用的拼接图片:")
                for img_info in combined_images:
                    original_name = img_info.get('original_name', '')
                    print(f"     - {original_name}")

        return ""

    except Exception as e:
        print(f"❌ 获取角色参考图URL失败: {e}")
        return ""

def 获取Pollinations工作流(模型名称="flux", 图片宽度=1280, 图片高度=720, 图片数量=2, 项目路径="", 角色名=""):
    """
    获取Pollinations工作流配置

    参数:
        模型名称: Pollinations模型名称
        图片宽度: 生成图片宽度
        图片高度: 生成图片高度
        图片数量: 生成图片数量
        项目路径: 项目路径，用于获取参考图URL
        角色名: 角色名称，用于获取参考图URL
    """

    # 验证模型名称
    if 模型名称 not in POLLINATIONS_MODELS:
        print(f"警告: 模型 {模型名称} 不在支持列表中，使用默认模型 flux")
        模型名称 = "flux"

    # 根据模型类型设置默认负面词
    默认负面词 = 获取模型默认负面词(模型名称)

    # 获取角色参考图URL
    参考图URL = 获取角色参考图URL(项目路径, 角色名) if 项目路径 and 角色名 else ""

    # 基础工作流配置
    workflow_inputs = {
        "prompt": "",
        "model": 模型名称,
        "width": 图片宽度,
        "height": 图片高度,
        "num_images": 图片数量,
        "default_negative_words": 默认负面词,  # 保存默认负面词，但不作为negative_prompt使用
        "seed": 0,
        "enhance": True,
        "safe_mode": True,
        "private": True,
        "nologo": False
    }

    # 如果有参考图URL，添加image参数
    if 参考图URL:
        workflow_inputs["image"] = 参考图URL
        print(f"✅ 添加参考图URL到工作流: {参考图URL}")

    workflow = {
        "1": {
            "inputs": workflow_inputs,
            "class_type": "PollinationsImageGen",
            "_meta": {
                "title": "Pollinations图像生成"
            }
        },
        "2": {
            "inputs": {
                "images": ["1", 0]
            },
            "class_type": "PreviewImage",
            "_meta": {
                "title": "预览图像"
            }
        },
        "3": {
            "inputs": {
                "filename_prefix": "Pollinations",
                "images": ["1", 0]
            },
            "class_type": "SaveImage",
            "_meta": {
                "title": "保存图像"
            }
        }
    }

    return workflow

def 获取模型默认负面词(模型名称):
    """
    根据模型类型获取默认负面词

    参数:
        模型名称: Pollinations模型名称

    返回:
        str: 默认负面词
    """

    # 模型分类默认负面词
    模型负面词映射 = {
        # Flux系列 - 简洁负面词
        "flux": "low quality, blurry",
        "flux-realism": "cartoon, anime, illustration, low quality, blurry",
        "flux-anime": "realistic, photorealistic, low quality, blurry",
        "flux-3d": "2d, flat, low quality, blurry",

        # 经典模型 - 标准负面词
        "stable-diffusion-xl": "low quality, bad anatomy, blurry, deformed, ugly",
        "dall-e-3": "low quality, blurry, distorted",
        "midjourney": "low quality, blurry, bad composition",

        # 特色模型 - 针对性负面词
        "turbo": "low quality, blurry, rushed, poor detail",
        "any-dark": "bright, cheerful, light colors, low quality",
        "playground-v2.5": "low quality, bad anatomy, blurry, deformed",

        # 角色一致性相关 - 增强负面词
        "photon": "different person, inconsistent face, low quality, blurry, bad anatomy",
        "deliberate": "different person, inconsistent face, low quality, blurry, bad anatomy"
    }

    # 获取对应模型的默认负面词，如果没有则使用通用默认值
    默认负面词 = 模型负面词映射.get(模型名称, "low quality, blurry")

    print(f"模型 {模型名称} 使用默认负面词: {默认负面词}")
    return 默认负面词

def 转换负面词为not格式(负面词):
    """
    将负面提示词转换为not格式

    参数:
        负面词: 原始负面提示词字符串

    返回:
        转换后的not格式字符串
    """
    if not 负面词 or not 负面词.strip():
        return ""

    # 分割负面词
    词汇列表 = [词.strip() for 词 in 负面词.split(',') if 词.strip()]

    # 转换为not格式
    not格式词汇 = [f"not {词}" for 词 in 词汇列表]

    # 用逗号连接
    return ", ".join(not格式词汇)

def 获取Pollinations文本生成工作流(模型名称="OpenAI GPT-4.1-mini"):
    """
    获取Pollinations文本生成工作流配置

    参数:
        模型名称: 文本生成模型名称
    """

    workflow = {
        "1": {
            "inputs": {
                "prompt": "create a text to image prompt for a love story",
                "model": 模型名称,
                "seed": 0,
                "randomize": True,
                "enhance": [False, True]
            },
            "class_type": "PollinationsTextGen",
            "_meta": {
                "title": "Pollinations文本生成"
            }
        },
        "2": {
            "inputs": {
                "prompt": ["1", 0],
                "model": "flux",
                "width": 1280,
                "height": 720,
                "num_images": 2,
                "default_negative_words": 获取模型默认负面词("flux"),  # 保存默认负面词，但不作为negative_prompt使用
                "seed": 0,
                "enhance": True,
                "safe_mode": True,
                "private": True,
                "nologo": False
            },
            "class_type": "PollinationsImageGen",
            "_meta": {
                "title": "Pollinations图像生成"
            }
        },
        "3": {
            "inputs": {
                "images": ["2", 0]
            },
            "class_type": "PreviewImage",
            "_meta": {
                "title": "预览图像"
            }
        },
        "4": {
            "inputs": {
                "text": ["1", 0]
            },
            "class_type": "ShowText|pysssss",
            "_meta": {
                "title": "显示生成的文本"
            }
        }
    }

    return workflow

def 获取推荐模型列表():
    """获取推荐的Pollinations模型列表"""
    推荐模型 = []
    for model, info in MODEL_CATEGORIES.items():
        if info.get("recommended", False):
            推荐模型.append({
                "模型名称": model,
                "分类": info["category"],
                "描述": info["description"],
                "质量": info["quality"],
                "速度": info["speed"]
            })
    return 推荐模型

def 获取模型分类():
    """获取模型分类信息"""
    分类 = {}
    for model, info in MODEL_CATEGORIES.items():
        category = info["category"]
        if category not in 分类:
            分类[category] = []
        分类[category].append({
            "模型名称": model,
            "描述": info["description"],
            "推荐": info.get("recommended", False),
            "质量": info["quality"],
            "速度": info["speed"]
        })
    return 分类

def 检查Pollinations兼容性():
    """检查Pollinations工作流兼容性"""
    try:
        # 检查是否有Pollinations节点
        import requests
        response = requests.get("http://127.0.0.1:8188/object_info", timeout=5)
        if response.status_code == 200:
            nodes = response.json()
            required_nodes = ['PollinationsImageGen', 'PollinationsTextGen']
            支持 = all(node in nodes for node in required_nodes)
            return {
                "支持": 支持,
                "缺失节点": [node for node in required_nodes if node not in nodes] if not 支持 else [],
                "说明": "Pollinations节点已安装" if 支持 else "需要安装Pollinations节点"
            }
        else:
            return {
                "支持": False,
                "缺失节点": [],
                "说明": "无法连接到ComfyUI服务器"
            }
    except Exception as e:
        return {
            "支持": False,
            "缺失节点": [],
            "说明": f"检查失败: {str(e)}"
        }

# 便捷函数
def Pollinations标准工作流(**参数):
    """标准Pollinations工作流"""
    return 获取Pollinations工作流(**参数)

def Pollinations快速工作流(**参数):
    """快速Pollinations工作流"""
    参数.setdefault("模型名称", "turbo")
    参数.setdefault("图片数量", 1)
    return 获取Pollinations工作流(**参数)

def Pollinations高质量工作流(**参数):
    """高质量Pollinations工作流"""
    参数.setdefault("模型名称", "flux-pro")
    参数.setdefault("图片宽度", 1920)
    参数.setdefault("图片高度", 1080)
    return 获取Pollinations工作流(**参数)

def Pollinations写实工作流(**参数):
    """写实风格Pollinations工作流"""
    参数.setdefault("模型名称", "flux-realism")
    return 获取Pollinations工作流(**参数)

def Pollinations动漫工作流(**参数):
    """动漫风格Pollinations工作流"""
    参数.setdefault("模型名称", "flux-anime")
    return 获取Pollinations工作流(**参数)

def 生成Pollinations图片(提示词, 模型名称="flux", 图片宽度=1280, 图片高度=720, 图片数量=2, 项目路径="", 当前编号=1, 已有数量=0, 备选图片组=None, 反面词="", 增强=True, 安全模式=True, 风格="", 种子=None, CFG系数=4.5, 采样步数=20, 重绘幅度=0.15, 角色名=""):
    """
    使用Pollinations API生成图片

    参数:
        提示词: 图片生成提示词
        模型名称: Pollinations模型名称
        图片宽度: 生成图片宽度
        图片高度: 生成图片高度
        图片数量: 生成图片数量
        项目路径: 项目图片保存路径
        当前编号: 当前图片编号
        已有数量: 已有图片数量
        备选图片组: 现有备选图片列表，用于计算连续编号
        反面词: 负面提示词
        增强: 是否启用提示词增强
        安全模式: 是否启用安全模式
        风格: 图片风格（可选）
        种子: 指定种子值（可选，如果不指定则随机生成）
        CFG系数: 提示词遵循强度 (1.0-20.0)
        采样步数: 生成步数 (1-100)
        重绘幅度: 重绘强度 (0.0-1.0)
        角色名: 角色名称，用于获取参考图片URL（可选）

    返回:
        list: 生成的图片信息列表，格式为 [{'path': 路径, 'seep': 种子}, ...]
    """
    import requests
    import os
    import time
    import random
    from urllib.parse import quote

    try:
        # 验证模型名称 - 优先使用云端动态列表，允许新模型
        try:
            from pyz.任务运行文件.云端模型管理器 import 获取云端模型列表
            可用模型列表 = 获取云端模型列表('Pollinations')

            if 模型名称 in 可用模型列表:
                print(f"✅ 模型 {模型名称} 在云端支持列表中")
            else:
                print(f"🔍 模型 {模型名称} 不在已知列表中，但仍尝试使用（Pollinations可能支持新模型）")
                # 不强制回退到flux，让Pollinations API自己处理

        except Exception as e:
            print(f"获取云端模型列表失败: {e}")
            print(f"🔍 直接使用模型 {模型名称}，让API自行处理")

        # 确保项目路径存在
        if not os.path.exists(项目路径):
            os.makedirs(项目路径, exist_ok=True)

        # 计算起始编号
        if 备选图片组:
            # 导入文件名最大数函数
            import sys
            import os
            sys.path.append(os.path.join(os.path.dirname(__file__), '..', '任务运行文件'))
            from A0_通用函数 import 文件名最大数

            起始编号 = 文件名最大数(备选图片组) + 1
            print(f"[调试] Pollinations使用连续编号，起始编号: {起始编号}")
        else:
            起始编号 = 已有数量
            print(f"[调试] Pollinations使用传统编号，起始编号: {起始编号}")

        生成的图片 = []

        for i in range(图片数量):
            try:
                # 生成或使用指定的种子
                if 种子 is not None:
                    当前种子 = 种子 + i  # 如果指定了种子，为每张图片使用递增的种子
                else:
                    当前种子 = random.randint(1000000000, 9999999999)  # 随机种子

                # 构建Pollinations API URL
                # 获取角色参考图URL（需要在提示词构建前获取）
                参考图URL = 获取角色参考图URL(项目路径, 角色名) if 项目路径 and 角色名 else ""

                # 0. 首先生成角色参考固定词（如果有参考图）
                角色参考固定词 = 生成角色参考固定词(角色名, 参考图URL)

                # 1. 处理原始提示词
                原始提示词 = 提示词

                # 2. 应用软件设置中的风格增强提示词（但不包含风格词）
                处理后提示词 = 原始提示词
                try:
                    from pyz.UI文件.软件设置UI_methods.获取Pollinations风格提示词 import 应用Pollinations风格提示词, 获取Pollinations反面提示词

                    # 应用正面风格增强（但我们需要分离出风格部分）
                    增强后提示词 = 应用Pollinations风格提示词(原始提示词, 模型名称)

                    # 如果增强后的提示词比原始提示词长，说明添加了风格词，我们需要提取出来
                    if 增强后提示词 != 原始提示词:
                        # 简单处理：如果增强后的提示词以原始提示词开头，提取后面的部分作为风格词
                        if 增强后提示词.startswith(原始提示词):
                            风格增强部分 = 增强后提示词[len(原始提示词):].strip().lstrip(',').strip()
                        else:
                            # 如果不是简单的前缀关系，使用增强后的整个提示词
                            处理后提示词 = 增强后提示词
                            风格增强部分 = ""
                    else:
                        风格增强部分 = ""

                    # 获取反面风格增强（如果没有传入反面词）
                    if not 反面词 or not 反面词.strip():
                        风格反面词 = 获取Pollinations反面提示词(模型名称)
                        if 风格反面词:
                            反面词 = 风格反面词

                except Exception as e:
                    print(f"风格增强应用失败: {e}")
                    风格增强部分 = ""

                # 3. 收集分类风格提示词
                分类风格部分 = ""
                使用风格增强 = A0_config.config.get('使用Pollinations风格增强', False)
                if not 使用风格增强 and 风格 and 风格.strip() and 风格 != "无风格":
                    # 尝试从新的分类系统获取风格提示词
                    try:
                        from pyz.UI文件.软件设置UI_methods.Pollinations风格分类 import 获取风格提示词
                        分类风格提示词 = 获取风格提示词(风格)
                        if 分类风格提示词:
                            分类风格部分 = 分类风格提示词
                        else:
                            # 回退到传统风格系统
                            传统风格提示词 = POLLINATIONS_STYLES.get(风格, 风格)
                            if 传统风格提示词:
                                分类风格部分 = 传统风格提示词
                    except Exception as e:
                        传统风格提示词 = POLLINATIONS_STYLES.get(风格, 风格)
                        if 传统风格提示词:
                            分类风格部分 = 传统风格提示词

                # 4. 按正确顺序组装完整提示词：角色参考固定词 + 原始提示词 + 风格词 + 负面词(not格式)
                完整提示词_部分 = []

                # 添加角色参考固定词（最前面）
                if 角色参考固定词:
                    完整提示词_部分.append(角色参考固定词)

                # 添加处理后的原始提示词（中间）
                if 处理后提示词:
                    完整提示词_部分.append(处理后提示词)

                # 添加风格词（最后面）
                if 风格增强部分:
                    完整提示词_部分.append(风格增强部分)
                elif 分类风格部分:
                    完整提示词_部分.append(分类风格部分)

                # 5. 处理负面词：转换为not格式并添加到提示词末尾
                if 反面词 and 反面词.strip():
                    not格式负面词 = 转换负面词为not格式(反面词)
                    if not格式负面词:
                        完整提示词_部分.append(not格式负面词)
                        print(f"✅ 添加not格式负面词: {not格式负面词}")

                # 组合最终提示词
                完整提示词 = ", ".join(完整提示词_部分)

                # 取消提示词长度限制（用户要求）
                # max_prompt_length = 1500  # 原来的保守长度限制已取消
                # if len(完整提示词) > max_prompt_length:
                #     print(f"⚠️ 提示词过长({len(完整提示词)}字符)，截取到{max_prompt_length}字符")
                #     完整提示词 = 完整提示词[:max_prompt_length]
                print(f"✅ 提示词长度: {len(完整提示词)} 字符（无限制）")

                # 对提示词进行URL编码
                编码提示词 = quote(完整提示词)

                # Pollinations API URL格式 - GET请求（官方只支持GET）
                api_urls = [
                    f"https://image.pollinations.ai/prompt/{编码提示词}",
                    f"https://pollinations.ai/p/{编码提示词}"  # 备用端点
                ]

                # 检查最终URL长度（仅作信息显示，不限制）
                test_url = f"{api_urls[0]}?model={模型名称}&width={图片宽度}&height={图片高度}"
                print(f"📊 URL长度: {len(test_url)} 字符")
                if len(test_url) > 2000:
                    print(f"ℹ️  URL较长({len(test_url)}字符)，如果遇到问题可考虑简化提示词")

                # 添加参数（GET请求参数）
                params = {}

                # 首先添加token（如果有），放在第一个位置
                api_key = A0_config.config.get('pollinations_api_key', '').strip()
                if api_key:
                    params['token'] = api_key
                    print(f"🔑 使用API密钥认证 (长度: {len(api_key)} 字符)")

                # 然后添加其他参数
                params.update({
                    'model': 模型名称,
                    'width': 图片宽度,
                    'height': 图片高度,
                    'seed': 当前种子,
                    'enhance': 'true' if 增强 else 'false',
                    'safe': 'true' if 安全模式 else 'false',
                    'private': 'true',
                    'nologo': 'true',  # 不显示logo
                    'guidance': CFG系数,  # CFG系数/引导强度
                    'steps': 采样步数,    # 采样步数
                    'strength': 重绘幅度  # 重绘强度
                })

                # 添加参考图URL（如果有）
                if 参考图URL:
                    params['image'] = 参考图URL
                    print(f"✅ 添加参考图URL到API请求: {参考图URL}")

                # 注意：负面提示词已经转换为not格式并添加到主提示词末尾，不再使用negative参数

                print(f"正在生成第 {i+1}/{图片数量} 张图片...")
                print("=" * 80)
                print("📋 完整API请求信息:")
                print("-" * 80)
                print(f"🎯 模型: {模型名称}")
                if 模型名称 == 'kontext':
                    print("   ✅ 正在使用KONTEXT模型 (非flux)")
                elif 模型名称 == 'flux':
                    print("   ⚠️ 正在使用FLUX模型")
                else:
                    print(f"   🔍 正在使用{模型名称}模型")
                print(f"📐 分辨率: {图片宽度}x{图片高度}")
                print(f"🎲 种子: {当前种子}")
                print(f"⚙️  质量参数:")
                print(f"   - CFG系数: {CFG系数}")
                print(f"   - 采样步数: {采样步数}")
                print(f"   - 重绘幅度: {重绘幅度}")
                print(f"🔧 功能开关:")
                print(f"   - 增强: {'开启' if 增强 else '关闭'}")
                print(f"   - 安全模式: {'开启' if 安全模式 else '关闭'}")
                print(f"📝 完整提示词 ({len(完整提示词)} 字符):")
                print(f"   {完整提示词}")
                if 反面词:
                    print(f"🚫 负面提示词 ({len(反面词)} 字符):")
                    print(f"   {反面词}")
                print(f"🌐 API参数:")
                for key, value in params.items():
                    if key == 'token':
                        print(f"   - {key}: {'*' * min(len(str(value)), 20)} 🔑 API密钥认证")
                    elif key == 'model':
                        print(f"   - {key}: {value} ⭐⭐⭐ 关键模型参数")
                    else:
                        print(f"   - {key}: {value}")
                print("=" * 80)

                # 发送请求 - 添加重试机制和多端点支持
                max_retries = 3
                success = False

                for api_url in api_urls:
                    if success:
                        break

                    # 构建完整的请求URL用于显示
                    param_strings = []
                    for key, value in params.items():
                        param_strings.append(f"{key}={value}")
                    完整请求URL = f"{api_url}?{'&'.join(param_strings)}"

                    print(f"\n🌐 尝试API端点: {api_url}")
                    print(f"📊 完整请求URL长度: {len(完整请求URL)} 字符")

                    # 检查URL中的模型参数
                    if 'model=kontext' in 完整请求URL:
                        print("🎯 ✅ URL中确认使用KONTEXT模型")
                    elif 'model=flux' in 完整请求URL:
                        print("🎯 ⚠️ URL中使用FLUX模型")
                    else:
                        模型参数 = [p for p in param_strings if p.startswith('model=')]
                        if 模型参数:
                            print(f"🎯 🔍 URL中使用模型: {模型参数[0]}")

                    print(f"🔗 完整请求URL:")
                    print(f"   {完整请求URL}")
                    print("-" * 40)

                    for retry in range(max_retries):
                        try:
                            # 根据重试次数调整超时时间
                            timeout = 60 + (retry * 30)  # 60s, 90s, 120s

                            if retry > 0:
                                print(f"第 {retry + 1} 次重试，超时设置: {timeout}秒")
                                time.sleep(5)  # 重试前等待5秒

                            response = requests.get(api_url, params=params, timeout=timeout)

                            if response.status_code == 200:
                                # 保存图片 - 使用连续编号
                                文件编号 = 起始编号 + i
                                文件名 = f"{当前编号}({文件编号}).png"
                                文件路径 = os.path.join(项目路径, 文件名)
                                # print(f"[调试] Pollinations保存图片: {文件名} (编号: {文件编号})")

                                with open(文件路径, 'wb') as f:
                                    f.write(response.content)

                                生成的图片.append({
                                    'path': 文件路径,
                                    'seep': 当前种子
                                })

                                print(f"🌱 返回种子值: {当前种子}")

                                print(f"图片生成成功: {文件名}")
                                success = True
                                break

                            elif response.status_code == 502:
                                print(f"服务器网关错误 (502)，第 {retry + 1} 次尝试")
                                if retry < max_retries - 1:
                                    continue
                                else:
                                    print(f"端点 {api_url} 暂时不可用")
                                    break  # 尝试下一个端点

                            elif response.status_code == 503:
                                print(f"服务不可用 (503)，第 {retry + 1} 次尝试")
                                if retry < max_retries - 1:
                                    continue
                                else:
                                    print(f"端点 {api_url} 暂时过载")
                                    break  # 尝试下一个端点

                            else:
                                print(f"图片生成失败，状态码: {response.status_code}")
                                print(f"响应内容: {response.text}")
                                break  # 尝试下一个端点

                        except requests.exceptions.Timeout:
                            print(f"请求超时，第 {retry + 1} 次尝试")
                            if retry < max_retries - 1:
                                continue
                            else:
                                print(f"端点 {api_url} 多次超时")
                                break  # 尝试下一个端点

                        except requests.exceptions.ConnectionError:
                            print(f"连接错误，第 {retry + 1} 次尝试")
                            if retry < max_retries - 1:
                                continue
                            else:
                                print(f"端点 {api_url} 连接失败")
                                break  # 尝试下一个端点

                        except Exception as e:
                            print(f"请求异常: {str(e)}")
                            break  # 尝试下一个端点

                # 如果成功生成，添加延迟避免请求过快
                if success and i < 图片数量 - 1:
                    time.sleep(2)
                elif not success:
                    print(f"第 {i+1} 张图片生成失败，所有端点都不可用")

            except Exception as e:
                print(f"生成第 {i+1} 张图片时出错: {str(e)}")
                continue

        if 生成的图片:
            print(f"Pollinations图片生成完成，共生成 {len(生成的图片)} 张图片")
            return 生成的图片
        else:
            print("Pollinations图片生成失败，没有成功生成任何图片")
            print("可能的原因：")
            print("1. Pollinations服务器暂时不可用 (502/503错误)")
            print("2. 网络连接问题")
            print("3. 请求超时")
            print("建议：稍后重试或检查网络连接")
            return []

    except Exception as e:
        print(f"Pollinations图片生成出错: {str(e)}")
        return []
