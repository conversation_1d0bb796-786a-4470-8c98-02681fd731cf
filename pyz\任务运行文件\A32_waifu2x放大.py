#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Waifu2x免费在线图片放大模块
支持动漫图片的AI超分辨率放大
"""

import os
import requests
import time
import re
from PIL import Image
from pyz.任务运行文件 import A0_config


class Waifu2xUpscaler:
    """Waifu2x在线放大器"""
    
    def __init__(self):
        self.api_url = "https://waifu2x.udp.jp/api"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        # API限制
        self.max_file_size = 5 * 1024 * 1024  # 5MB
        self.max_noise_reduction_size = (3000, 3000)  # 降噪最大尺寸
        self.max_upscaling_size = (1500, 1500)  # 放大最大尺寸
    
    def check_image_limits(self, image_path, scale_factor=2):
        """检查图片是否符合API限制"""
        try:
            # 检查文件大小
            file_size = os.path.getsize(image_path)
            if file_size > self.max_file_size:
                print(f"❌ 文件过大: {file_size / 1024 / 1024:.1f}MB > 5MB")
                return False, "文件大小超过5MB限制"
            
            # 检查图片尺寸
            with Image.open(image_path) as img:
                width, height = img.size
                
                # 检查放大后尺寸是否超限
                if scale_factor > 1:
                    if width > self.max_upscaling_size[0] or height > self.max_upscaling_size[1]:
                        print(f"❌ 图片尺寸过大: {width}x{height} > {self.max_upscaling_size}")
                        return False, f"图片尺寸超过{self.max_upscaling_size}限制"
                
                print(f"✅ 图片检查通过: {width}x{height}, {file_size / 1024:.1f}KB")
                return True, "检查通过"
                
        except Exception as e:
            print(f"❌ 图片检查失败: {e}")
            return False, f"图片检查失败: {e}"
    
    def upscale_image(self, image_path, scale_factor=2, noise_reduction=1, style="art", output_format="png"):
        """
        使用Waifu2x放大图片
        
        参数:
            image_path: 输入图片路径
            scale_factor: 放大倍数 (1, 1.6, 2)
            noise_reduction: 降噪级别 (0=None, 1=Low, 2=Medium, 3=High, 4=Highest)
            style: 图片风格 ("art"=动漫, "photo"=照片)
            output_format: 输出格式 ("png", "webp")
        
        返回:
            成功: 放大后的图片路径
            失败: None
        """
        try:
            print(f"🚀 开始Waifu2x放大: {os.path.basename(image_path)}")
            
            # 检查图片限制
            check_ok, check_msg = self.check_image_limits(image_path, scale_factor)
            if not check_ok:
                print(f"❌ {check_msg}")
                return None
            
            # 准备上传数据
            files = {}
            data = {}
            
            # 打开图片文件
            with open(image_path, 'rb') as f:
                files['file'] = (os.path.basename(image_path), f, 'image/png')
                
                # 设置参数
                data['style'] = style  # art 或 photo
                data['noise'] = str(noise_reduction)  # 0-4
                data['scale'] = str(scale_factor) if scale_factor != 1 else ""  # 1, 1.6, 2 或空字符串
                data['format'] = output_format  # png 或 webp
                
                print(f"📤 上传参数: style={style}, noise={noise_reduction}, scale={scale_factor}, format={output_format}")
                
                # 发送请求
                response = self.session.post(
                    self.api_url,
                    files=files,
                    data=data,
                    timeout=300  # 5分钟超时
                )
            
            if response.status_code == 200:
                # 检查响应内容类型
                content_type = response.headers.get('content-type', '')
                
                if 'image' in content_type:
                    # 成功返回图片数据
                    output_path = self._save_upscaled_image(image_path, response.content, scale_factor)
                    if output_path:
                        print(f"✅ Waifu2x放大成功: {os.path.basename(output_path)}")
                        return output_path
                    else:
                        print("❌ 保存放大图片失败")
                        return None
                else:
                    # 可能是错误信息
                    error_text = response.text
                    print(f"❌ Waifu2x返回错误: {error_text}")
                    return None
            else:
                print(f"❌ Waifu2x请求失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                return None
                
        except requests.Timeout:
            print("❌ Waifu2x请求超时")
            return None
        except Exception as e:
            print(f"❌ Waifu2x放大异常: {e}")
            return None
    
    def _save_upscaled_image(self, original_path, image_data, scale_factor):
        """保存放大后的图片"""
        try:
            # 生成输出文件名
            dir_path = os.path.dirname(original_path)
            base_name = os.path.splitext(os.path.basename(original_path))[0]
            
            # 移除原有的编号后缀，添加高清标识
            base_name = re.sub(r'\(\d+\)$', '', base_name)
            output_filename = f"{base_name}(高清).png"
            output_path = os.path.join(dir_path, output_filename)
            
            # 保存图片
            with open(output_path, 'wb') as f:
                f.write(image_data)
            
            # 验证保存的图片
            try:
                with Image.open(output_path) as img:
                    width, height = img.size
                    print(f"📊 放大后尺寸: {width}x{height}")
                    return output_path
            except Exception as e:
                print(f"❌ 验证放大图片失败: {e}")
                os.remove(output_path)  # 删除损坏的文件
                return None
                
        except Exception as e:
            print(f"❌ 保存放大图片异常: {e}")
            return None


def waifu2x_upscale_batch(image_paths, scale_factor=2, noise_reduction=1, style="art"):
    """
    批量使用Waifu2x放大图片
    
    参数:
        image_paths: 图片路径列表
        scale_factor: 放大倍数
        noise_reduction: 降噪级别
        style: 图片风格
    
    返回:
        成功放大的图片路径列表
    """
    upscaler = Waifu2xUpscaler()
    results = []
    
    for i, image_path in enumerate(image_paths):
        print(f"🔄 处理第 {i+1}/{len(image_paths)} 张图片")
        
        if not os.path.exists(image_path):
            print(f"❌ 图片不存在: {image_path}")
            continue
        
        # 放大图片
        result_path = upscaler.upscale_image(
            image_path, 
            scale_factor=scale_factor,
            noise_reduction=noise_reduction,
            style=style
        )
        
        if result_path:
            results.append(result_path)
        
        # 添加延迟避免请求过快
        if i < len(image_paths) - 1:
            time.sleep(2)
    
    print(f"🎉 批量放大完成: {len(results)}/{len(image_paths)} 张成功")
    return results


def waifu2x_upscale_single(image_path, scale_factor=None):
    """
    单张图片Waifu2x放大（用于集成到现有系统）
    
    参数:
        image_path: 图片路径
        scale_factor: 放大倍数（从配置读取）
    
    返回:
        成功: 放大后图片路径
        失败: None
    """
    if scale_factor is None:
        scale_factor = int(A0_config.config.get('修复放大倍数', 2))
    
    # 根据文件名判断图片类型
    filename = os.path.basename(image_path).lower()
    if any(keyword in filename for keyword in ['anime', 'manga', 'cartoon', '动漫', '漫画']):
        style = "art"
    else:
        style = "art"  # 默认使用art模式，效果通常更好
    
    upscaler = Waifu2xUpscaler()
    return upscaler.upscale_image(
        image_path,
        scale_factor=scale_factor,
        noise_reduction=1,  # 低级降噪
        style=style
    )


if __name__ == "__main__":
    # 测试代码
    test_image = "test.png"  # 替换为实际测试图片路径
    if os.path.exists(test_image):
        result = waifu2x_upscale_single(test_image, 2)
        if result:
            print(f"测试成功: {result}")
        else:
            print("测试失败")
    else:
        print("请提供测试图片路径")
