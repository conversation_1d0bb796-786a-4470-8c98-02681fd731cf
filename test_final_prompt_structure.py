#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试最终的提示词结构
"""

import os
import sys

# 添加项目路径到sys.path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from pyz.工作流.A7_Pollinations工作流 import 生成角色参考固定词

def test_提示词结构():
    """测试提示词结构是否正确"""
    print("=" * 60)
    print("测试最终提示词结构")
    print("=" * 60)
    
    # 模拟不同的角色组合
    测试案例 = [
        {
            "角色名": "露西",
            "原始提示词": "Lucy stands on the highest branch of a spruce tree",
            "期望结构": "[角色参考固定词], [原始提示词], [风格词]"
        },
        {
            "角色名": "艾米丽,露西",
            "原始提示词": "Two girls having a conversation in the garden",
            "期望结构": "[角色参考固定词], [原始提示词], [风格词]"
        },
        {
            "角色名": "母亲,父亲,挑水老人,迷路的孩子",
            "原始提示词": "A family scene in a rural village",
            "期望结构": "[角色参考固定词], [原始提示词], [风格词]"
        },
        {
            "角色名": "",
            "原始提示词": "A beautiful landscape scene",
            "期望结构": "[原始提示词], [风格词]"
        }
    ]
    
    for i, 案例 in enumerate(测试案例, 1):
        角色名 = 案例["角色名"]
        原始提示词 = 案例["原始提示词"]
        期望结构 = 案例["期望结构"]
        
        print(f"\n🧪 测试案例 {i}: {期望结构}")
        print(f"🎭 角色: {角色名 if 角色名 else '无角色'}")
        print(f"📝 原始提示词: {原始提示词}")
        
        # 生成角色参考固定词
        参考图URL = "https://example.com/reference.png" if 角色名 else ""
        角色参考固定词 = 生成角色参考固定词(角色名, 参考图URL)
        
        # 模拟风格词
        风格词 = "Chinese animation style, high quality, detailed"
        
        # 按正确顺序组装提示词
        提示词_部分 = []
        
        if 角色参考固定词:
            提示词_部分.append(角色参考固定词)
            print(f"✅ 角色参考固定词: {角色参考固定词}")
        
        if 原始提示词:
            提示词_部分.append(原始提示词)
        
        if 风格词:
            提示词_部分.append(风格词)
        
        最终提示词 = ", ".join(提示词_部分)
        
        print(f"🎯 最终提示词: {最终提示词}")
        print(f"📊 长度: {len(最终提示词)} 字符")
        
        # 验证结构
        if 角色参考固定词:
            if 最终提示词.startswith(角色参考固定词):
                print("✅ 角色参考固定词在最前面")
            else:
                print("❌ 角色参考固定词位置错误")
        
        if 最终提示词.endswith(风格词):
            print("✅ 风格词在最后面")
        else:
            print("❌ 风格词位置错误")
        
        # 检查是否包含原始提示词
        if 原始提示词 in 最终提示词:
            print("✅ 包含原始提示词")
        else:
            print("❌ 缺少原始提示词")

def test_角色翻译效果():
    """测试角色翻译效果"""
    print("\n" + "=" * 60)
    print("测试角色翻译效果")
    print("=" * 60)
    
    from pyz.工作流.A7_Pollinations工作流 import 翻译角色名
    
    测试角色 = [
        "露西",
        "艾米丽", 
        "母亲",
        "父亲",
        "挑水老人",
        "迷路的孩子"
    ]
    
    print("🌐 角色翻译结果:")
    for 角色 in 测试角色:
        翻译后 = 翻译角色名(角色)
        print(f"   {角色} → {翻译后}")

def test_URL构建():
    """测试URL构建（不实际发送请求）"""
    print("\n" + "=" * 60)
    print("测试URL构建")
    print("=" * 60)
    
    from urllib.parse import quote
    
    # 模拟一个完整的提示词
    角色参考固定词 = "Same character and costume as the reference image"
    原始提示词 = "Lucy stands on the highest branch of a spruce tree"
    风格词 = "Chinese animation style, high quality"
    
    完整提示词 = f"{角色参考固定词}, {原始提示词}, {风格词}"
    
    print(f"📝 完整提示词: {完整提示词}")
    print(f"📊 长度: {len(完整提示词)} 字符")
    
    # URL编码
    编码提示词 = quote(完整提示词)
    
    # 构建API URL
    base_url = "https://image.pollinations.ai/prompt/"
    参数 = "?model=flux&width=1280&height=720&image=https://example.com/ref.png"
    
    完整URL = f"{base_url}{编码提示词}{参数}"
    
    print(f"🌐 API URL长度: {len(完整URL)} 字符")
    print(f"🔗 URL结构正确: {'✅' if 'prompt/' in 完整URL else '❌'}")
    
    # 检查URL是否过长
    if len(完整URL) > 2000:
        print("⚠️ URL可能过长，某些浏览器可能有限制")
    else:
        print("✅ URL长度合理")

if __name__ == "__main__":
    print("🚀 开始测试最终提示词结构")
    
    # 测试提示词结构
    test_提示词结构()
    
    # 测试角色翻译
    test_角色翻译效果()
    
    # 测试URL构建
    test_URL构建()
    
    print("\n" + "=" * 60)
    print("✅ 测试完成")
    print("=" * 60)
