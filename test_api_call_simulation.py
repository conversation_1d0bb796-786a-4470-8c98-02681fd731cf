#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模拟API调用过程，检查模型参数传递
"""

import os
import sys

# 添加项目路径到sys.path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from pyz.任务运行文件 import A0_config

def test_模拟API调用():
    """模拟完整的API调用过程"""
    print("=" * 80)
    print("模拟Pollinations API调用过程")
    print("=" * 80)
    
    # 获取当前配置
    模型名称 = A0_config.config.get('pollinations_model', 'flux')
    图片宽度 = int(A0_config.config.get('图片长度', 1280))
    图片高度 = int(A0_config.config.get('图片宽度', 960))
    
    print(f"📋 从配置读取的参数:")
    print(f"   🎯 模型: {模型名称}")
    print(f"   📐 尺寸: {图片宽度}x{图片高度}")
    
    # 模拟API参数构建（按照A7_Pollinations工作流.py的逻辑）
    当前种子 = 1234567890
    CFG系数 = 4.5
    采样步数 = 20
    重绘幅度 = 0.15
    增强 = True
    安全模式 = True
    
    params = {
        'model': 模型名称,
        'width': 图片宽度,
        'height': 图片高度,
        'seed': 当前种子,
        'enhance': 'true' if 增强 else 'false',
        'safe': 'true' if 安全模式 else 'false',
        'private': 'true',
        'nologo': 'true',
        'guidance': CFG系数,
        'steps': 采样步数,
        'strength': 重绘幅度
    }
    
    print(f"\n🌐 构建的API参数:")
    for key, value in params.items():
        if key == 'model':
            print(f"   - {key}: {value} ⭐ (关键参数)")
        else:
            print(f"   - {key}: {value}")
    
    # 构建URL
    from urllib.parse import quote
    
    测试提示词 = "A beautiful landscape scene"
    编码提示词 = quote(测试提示词)
    
    base_url = "https://image.pollinations.ai/prompt/"
    api_url = f"{base_url}{编码提示词}"
    
    # 构建参数字符串
    param_strings = []
    for key, value in params.items():
        param_strings.append(f"{key}={value}")
    
    完整请求URL = f"{api_url}?{'&'.join(param_strings)}"
    
    print(f"\n🔗 构建的完整URL:")
    print(f"   基础URL: {api_url}")
    print(f"   参数: {'&'.join(param_strings)}")
    print(f"\n📊 完整URL长度: {len(完整请求URL)} 字符")
    
    # 检查模型参数是否正确
    if 'model=kontext' in 完整请求URL:
        print("✅ URL中包含正确的模型参数: model=kontext")
    elif 'model=flux' in 完整请求URL:
        print("❌ URL中仍然使用flux模型: model=flux")
    else:
        print(f"🔍 URL中的模型参数: {[p for p in param_strings if p.startswith('model=')]}")
    
    # 显示URL的关键部分
    print(f"\n🎯 URL中的模型参数:")
    模型参数 = [p for p in param_strings if p.startswith('model=')]
    if 模型参数:
        print(f"   {模型参数[0]}")
    else:
        print("   ❌ 未找到模型参数")
    
    return 完整请求URL

def test_检查URL编码():
    """检查URL编码是否影响模型参数"""
    print("\n" + "=" * 80)
    print("检查URL编码对模型参数的影响")
    print("=" * 80)
    
    from urllib.parse import quote, unquote
    
    模型名称 = A0_config.config.get('pollinations_model', 'flux')
    
    # 测试模型名称的编码
    编码前 = 模型名称
    编码后 = quote(模型名称)
    解码后 = unquote(编码后)
    
    print(f"🔤 模型名称编码测试:")
    print(f"   编码前: {编码前}")
    print(f"   编码后: {编码后}")
    print(f"   解码后: {解码后}")
    
    if 编码前 == 解码后:
        print("✅ 编码/解码正常，不会影响模型名称")
    else:
        print("❌ 编码/解码有问题，可能影响模型名称")
    
    # 测试完整参数字符串
    测试参数 = f"model={模型名称}&width=1280&height=960"
    编码参数 = quote(测试参数, safe='=&')
    
    print(f"\n🔗 参数字符串编码测试:")
    print(f"   原始: {测试参数}")
    print(f"   编码: {编码参数}")

def test_实际生成调用():
    """测试实际的生成调用（不发送请求）"""
    print("\n" + "=" * 80)
    print("测试实际生成调用参数")
    print("=" * 80)
    
    try:
        # 模拟调用生成函数的参数
        模型名称 = A0_config.config.get('pollinations_model', 'flux')
        
        print(f"🎯 准备调用生成函数，参数:")
        print(f"   模型名称: {模型名称}")
        print(f"   提示词: 'test prompt'")
        print(f"   图片宽度: 1280")
        print(f"   图片高度: 960")
        
        # 检查是否会正确传递
        if 模型名称 == 'kontext':
            print("✅ 会正确传递kontext模型到API")
        elif 模型名称 == 'flux':
            print("⚠️ 会传递flux模型到API")
        else:
            print(f"🔍 会传递{模型名称}模型到API")
            
        # 模拟函数调用（不实际执行）
        print(f"\n📞 模拟函数调用:")
        print(f"   生成Pollinations图片(")
        print(f"       提示词='test prompt',")
        print(f"       模型名称='{模型名称}',  ⭐")
        print(f"       图片宽度=1280,")
        print(f"       图片高度=960,")
        print(f"       ...)")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_配置重新加载():
    """测试配置重新加载"""
    print("\n" + "=" * 80)
    print("测试配置重新加载")
    print("=" * 80)
    
    import json
    
    # 读取配置文件
    try:
        with open(A0_config.配置文件, 'r', encoding='utf-8') as f:
            文件配置 = json.load(f)
        
        文件中的模型 = 文件配置.get('pollinations_model', '未设置')
        内存中的模型 = A0_config.config.get('pollinations_model', '未设置')
        
        print(f"📄 配置文件中的模型: {文件中的模型}")
        print(f"💾 内存中的模型: {内存中的模型}")
        
        if 文件中的模型 != 内存中的模型:
            print("⚠️ 发现不一致，重新加载配置...")
            A0_config.config['pollinations_model'] = 文件中的模型
            print(f"✅ 已更新内存中的模型为: {A0_config.config.get('pollinations_model')}")
        else:
            print("✅ 配置一致，无需重新加载")
            
    except Exception as e:
        print(f"❌ 重新加载配置失败: {e}")

if __name__ == "__main__":
    print("🚀 开始模拟API调用过程")
    
    # 模拟API调用
    完整URL = test_模拟API调用()
    
    # 检查URL编码
    test_检查URL编码()
    
    # 测试实际生成调用
    test_实际生成调用()
    
    # 测试配置重新加载
    test_配置重新加载()
    
    print("\n" + "=" * 80)
    print("测试总结")
    print("=" * 80)
    
    当前模型 = A0_config.config.get('pollinations_model', 'flux')
    print(f"🎯 当前配置的模型: {当前模型}")
    
    if 当前模型 == 'kontext':
        print("✅ 配置正确，应该会使用kontext模型")
        print("\n💡 如果实际生成时仍显示flux，请检查:")
        print("1. 生成时的控制台输出中的'🎯 模型'信息")
        print("2. 生成时的'🌐 API参数'中的model值")
        print("3. 是否有其他地方覆盖了模型设置")
    else:
        print(f"⚠️ 配置异常，当前模型是{当前模型}")
    
    print("\n🔍 调试建议:")
    print("1. 在实际生成图片时，查看控制台输出")
    print("2. 寻找'🎯 模型:'和'🌐 API参数:'的输出")
    print("3. 确认API参数中model的值是否为kontext")
    
    print("=" * 80)
