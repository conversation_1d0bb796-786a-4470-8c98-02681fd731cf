#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Pollination工作流的角色参考固定词功能
"""

import os
import sys

# 添加项目路径到sys.path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from pyz.工作流.A7_Pollinations工作流 import 生成角色参考固定词, 获取角色参考图URL

def test_生成角色参考固定词():
    """测试生成角色参考固定词功能"""
    print("=" * 60)
    print("测试生成角色参考固定词功能")
    print("=" * 60)
    
    # 模拟参考图URL
    模拟URL = "https://example.com/image.png"
    
    # 测试用例
    测试用例 = [
        ("", "", "无角色名"),
        ("全局", 模拟URL, "全局角色"),
        ("艾米丽", "", "单个角色但无参考图"),
        ("艾米丽", 模拟URL, "单个角色有参考图"),
        ("艾米丽,露西", 模拟URL, "两个角色"),
        ("艾米丽,露西,斑斑", 模拟URL, "三个角色"),
        ("艾米丽,露西,斑斑,小明", 模拟URL, "四个角色"),
        ("艾米丽,露西,斑斑,小明,小红", 模拟URL, "五个角色"),
    ]
    
    for 角色名, 参考图URL, 描述 in 测试用例:
        print(f"\n🧪 测试: {描述} - 角色:'{角色名}', URL:'{参考图URL[:30]}...' if 参考图URL else 'None'")
        try:
            固定词 = 生成角色参考固定词(角色名, 参考图URL)
            if 固定词:
                print(f"✅ 生成固定词: {固定词}")
            else:
                print(f"❌ 未生成固定词")
        except Exception as e:
            print(f"❌ 错误: {e}")

def test_完整提示词构建():
    """测试完整提示词构建流程"""
    print("\n" + "=" * 60)
    print("测试完整提示词构建流程")
    print("=" * 60)
    
    项目路径 = r"C:\Users\<USER>\Desktop\纯净wen\wen整合 - 副本 - 副本\项目文件\原创项目\蜂蝶随香\图片文件"
    
    测试案例 = [
        ("艾米丽", "a beautiful girl with long hair", "单个角色"),
        ("艾米丽,露西", "two girls standing together", "两个角色"),
        ("艾米丽,露西,斑斑", "three characters in a scene", "三个角色"),
        ("", "a landscape scene", "无角色"),
    ]
    
    for 角色名, 原始提示词, 描述 in 测试案例:
        print(f"\n🧪 测试{描述}: '{角色名}'")
        print(f"📝 原始提示词: {原始提示词}")
        
        # 获取参考图URL
        参考图URL = 获取角色参考图URL(项目路径, 角色名)
        
        # 生成角色参考固定词
        角色参考固定词 = 生成角色参考固定词(角色名, 参考图URL)
        
        # 构建完整提示词
        完整提示词 = 原始提示词
        if 角色参考固定词:
            完整提示词 = f"{角色参考固定词}, {完整提示词}"
        
        # 模拟添加风格词到末尾
        风格词 = "anime style, high quality"
        完整提示词 = f"{完整提示词}, {风格词}"
        
        print(f"🎯 最终提示词: {完整提示词}")
        print(f"📊 提示词长度: {len(完整提示词)} 字符")
        
        # 验证结构
        if 角色参考固定词:
            if 完整提示词.startswith(角色参考固定词):
                print("✅ 角色参考固定词在最前面")
            else:
                print("❌ 角色参考固定词位置错误")
            
            if 完整提示词.endswith(风格词):
                print("✅ 风格词在最后面")
            else:
                print("❌ 风格词位置错误")
        else:
            print("ℹ️  无角色参考固定词")

def test_角色位置描述():
    """测试角色位置描述的准确性"""
    print("\n" + "=" * 60)
    print("测试角色位置描述的准确性")
    print("=" * 60)
    
    模拟URL = "https://example.com/image.png"
    
    # 测试不同角色组合的位置描述
    测试组合 = [
        (["艾米丽"], "单个角色"),
        (["艾米丽", "露西"], "两个角色"),
        (["艾米丽", "露西", "斑斑"], "三个角色"),
        (["艾米丽", "露西", "斑斑", "小明"], "四个角色"),
        (["艾米丽", "露西", "斑斑", "小明", "小红"], "五个角色"),
    ]
    
    for 角色列表, 描述 in 测试组合:
        角色名 = ",".join(角色列表)
        print(f"\n🧪 测试{描述}: {角色列表}")
        
        固定词 = 生成角色参考固定词(角色名, 模拟URL)
        print(f"📝 生成的固定词: {固定词}")
        
        # 验证位置描述
        if len(角色列表) == 1:
            expected = "Same character and costume as the reference image"
            if 固定词 == expected:
                print("✅ 单个角色描述正确")
            else:
                print(f"❌ 单个角色描述错误，期望: {expected}")
        
        elif len(角色列表) == 2:
            if "left" in 固定词 and "right" in 固定词:
                if 角色列表[0] in 固定词 and 角色列表[1] in 固定词:
                    print("✅ 两个角色位置描述正确")
                else:
                    print("❌ 两个角色名称缺失")
            else:
                print("❌ 两个角色位置词缺失")
        
        elif len(角色列表) == 3:
            if "left" in 固定词 and "middle" in 固定词 and "right" in 固定词:
                所有角色都在 = all(角色 in 固定词 for 角色 in 角色列表)
                if 所有角色都在:
                    print("✅ 三个角色位置描述正确")
                else:
                    print("❌ 三个角色名称不完整")
            else:
                print("❌ 三个角色位置词不完整")

        elif len(角色列表) == 4:
            if "upper left" in 固定词 and "upper right" in 固定词 and "lower left" in 固定词 and "lower right" in 固定词:
                所有角色都在 = all(角色 in 固定词 for 角色 in 角色列表)
                if 所有角色都在:
                    print("✅ 四个角色位置描述正确")
                else:
                    print("❌ 四个角色名称不完整")
            else:
                print("❌ 四个角色位置词不完整")

        else:
            # 超过4个角色的通用描述
            所有角色都在 = all(角色 in 固定词 for 角色 in 角色列表)
            if 所有角色都在:
                print("✅ 多个角色通用描述正确")
            else:
                print("❌ 多个角色名称不完整")

if __name__ == "__main__":
    print("🚀 开始测试Pollination工作流的角色参考固定词功能")
    
    # 测试生成角色参考固定词
    test_生成角色参考固定词()
    
    # 测试完整提示词构建
    test_完整提示词构建()
    
    # 测试角色位置描述
    test_角色位置描述()
    
    print("\n" + "=" * 60)
    print("✅ 测试完成")
    print("=" * 60)
