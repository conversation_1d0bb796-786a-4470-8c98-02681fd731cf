#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试API密钥使用问题
"""

import os
import sys

# 添加项目路径到sys.path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from pyz.任务运行文件 import A0_config

def debug_API密钥配置():
    """调试API密钥配置"""
    print("=" * 80)
    print("调试API密钥配置")
    print("=" * 80)
    
    # 检查配置文件中的密钥
    api_key = A0_config.config.get('pollinations_api_key', '')
    
    print(f"📋 配置文件检查:")
    print(f"   🔑 pollinations_api_key: {'已设置' if api_key else '未设置'}")
    
    if api_key:
        print(f"   📏 密钥长度: {len(api_key)} 字符")
        print(f"   👀 密钥预览: {api_key[:4]}...{api_key[-4:] if len(api_key) > 8 else ''}")
        print(f"   🔍 密钥类型: {type(api_key)}")
        print(f"   ✂️ 去空格后: '{api_key.strip()}'")
        print(f"   📏 去空格长度: {len(api_key.strip())} 字符")
    else:
        print(f"   ❌ 密钥为空或未设置")
    
    # 检查其他相关配置
    使用Pollinations = A0_config.config.get('使用Pollinations', False)
    当前模型 = A0_config.config.get('pollinations_model', 'flux')
    
    print(f"\n📊 相关配置:")
    print(f"   ☁️ 使用Pollinations: {使用Pollinations}")
    print(f"   🎯 当前模型: {当前模型}")
    
    return api_key

def debug_参数构建逻辑():
    """调试参数构建逻辑"""
    print("\n" + "=" * 80)
    print("调试参数构建逻辑")
    print("=" * 80)
    
    # 模拟工作流中的参数构建逻辑
    print("🔧 模拟参数构建过程:")
    
    # 1. 获取API密钥
    api_key = A0_config.config.get('pollinations_api_key', '').strip()
    print(f"   1️⃣ 获取API密钥: {'已获取' if api_key else '未获取'}")
    
    if api_key:
        print(f"      🔑 密钥内容: {api_key[:4]}...{api_key[-4:]}")
        print(f"      📏 密钥长度: {len(api_key)}")
    
    # 2. 构建参数
    params = {}
    
    # 首先添加token（如果有）
    if api_key:
        params['token'] = api_key
        print(f"   2️⃣ 添加token参数: ✅")
    else:
        print(f"   2️⃣ 跳过token参数: ❌ (密钥为空)")
    
    # 添加其他参数
    params.update({
        'model': 'kontext',
        'width': 1280,
        'height': 960,
        'seed': 123456
    })
    
    print(f"   3️⃣ 添加其他参数: ✅")
    
    # 3. 显示最终参数
    print(f"\n🌐 构建的参数:")
    for key, value in params.items():
        if key == 'token':
            print(f"   - {key}: {'*' * len(str(value))} 🔑 API密钥")
        else:
            print(f"   - {key}: {value}")
    
    # 4. 检查token位置
    参数键列表 = list(params.keys())
    if 'token' in 参数键列表:
        token位置 = 参数键列表.index('token')
        print(f"\n✅ token参数位置: 第{token位置 + 1}位")
        if token位置 == 0:
            print(f"   🎯 位置正确：token在第一位")
        else:
            print(f"   ⚠️ 位置异常：token不在第一位")
    else:
        print(f"\n❌ 缺少token参数")
    
    return params

def debug_工作流代码():
    """调试工作流代码"""
    print("\n" + "=" * 80)
    print("调试工作流代码")
    print("=" * 80)
    
    try:
        # 检查工作流文件中的代码
        工作流文件 = "pyz/工作流/A7_Pollinations工作流.py"
        
        print(f"📁 检查工作流文件: {工作流文件}")
        
        # 读取文件内容，查找token相关代码
        with open(工作流文件, 'r', encoding='utf-8') as f:
            内容 = f.read()
        
        # 查找关键代码段
        关键词列表 = [
            'pollinations_api_key',
            'token',
            'api_key',
            'params = {}',
            'params.update'
        ]
        
        print(f"🔍 搜索关键代码:")
        for 关键词 in 关键词列表:
            if 关键词 in 内容:
                print(f"   ✅ 找到: {关键词}")
            else:
                print(f"   ❌ 未找到: {关键词}")
        
        # 查找具体的token添加代码
        if 'api_key = A0_config.config.get' in 内容:
            print(f"\n✅ 找到API密钥获取代码")
        else:
            print(f"\n❌ 未找到API密钥获取代码")
        
        if "params['token'] = api_key" in 内容:
            print(f"✅ 找到token参数添加代码")
        else:
            print(f"❌ 未找到token参数添加代码")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查工作流代码失败: {e}")
        return False

def debug_实际调用():
    """调试实际调用"""
    print("\n" + "=" * 80)
    print("调试实际调用")
    print("=" * 80)
    
    try:
        # 模拟实际的API调用参数构建
        from pyz.任务运行文件 import A0_config
        
        print("🧪 模拟实际API调用:")
        
        # 获取当前配置
        模型名称 = A0_config.config.get('pollinations_model', 'flux')
        api_key = A0_config.config.get('pollinations_api_key', '').strip()
        
        print(f"   📋 当前模型: {模型名称}")
        print(f"   🔑 API密钥: {'已设置' if api_key else '未设置'}")
        
        # 构建参数（按照工作流的逻辑）
        params = {}
        
        # 首先添加token（如果有），放在第一个位置
        if api_key:
            params['token'] = api_key
            print(f"   ✅ 添加token参数")
        else:
            print(f"   ❌ 跳过token参数（密钥为空）")
        
        # 然后添加其他参数
        params.update({
            'model': 模型名称,
            'width': 1280,
            'height': 960,
            'seed': 123456,
            'enhance': 'true',
            'safe': 'true',
            'private': 'true',
            'nologo': 'true'
        })
        
        print(f"\n🌐 最终API参数:")
        for key, value in params.items():
            if key == 'token':
                print(f"   - {key}: {'*' * min(len(str(value)), 20)} 🔑 API密钥认证")
            elif key == 'model':
                print(f"   - {key}: {value} ⭐⭐⭐ 关键模型参数")
            else:
                print(f"   - {key}: {value}")
        
        # 检查是否包含token
        包含token = 'token' in params
        print(f"\n📊 参数检查:")
        print(f"   🔑 包含token: {'✅ 是' if 包含token else '❌ 否'}")
        print(f"   📏 参数总数: {len(params)}")
        
        if 包含token:
            print(f"   🎯 token值长度: {len(params['token'])}")
        
        return params
        
    except Exception as e:
        print(f"❌ 实际调用调试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    print("🚀 开始调试API密钥使用问题")
    
    # 1. 检查配置
    api_key = debug_API密钥配置()
    
    # 2. 检查参数构建逻辑
    params = debug_参数构建逻辑()
    
    # 3. 检查工作流代码
    代码检查 = debug_工作流代码()
    
    # 4. 模拟实际调用
    实际参数 = debug_实际调用()
    
    print("\n" + "=" * 80)
    print("调试结果总结")
    print("=" * 80)
    
    if api_key:
        print("✅ API密钥已设置")
    else:
        print("❌ API密钥未设置")
    
    if params and 'token' in params:
        print("✅ 参数构建逻辑正确")
    else:
        print("❌ 参数构建逻辑有问题")
    
    if 代码检查:
        print("✅ 工作流代码检查通过")
    else:
        print("❌ 工作流代码有问题")
    
    if 实际参数 and 'token' in 实际参数:
        print("✅ 实际调用应该包含token")
    else:
        print("❌ 实际调用缺少token")
    
    print("\n💡 如果API密钥已设置但没有使用，可能的原因:")
    print("1. 工作流代码没有正确获取配置")
    print("2. 参数构建逻辑有bug")
    print("3. 配置文件没有正确保存")
    print("4. 代码缓存问题")
    
    print("=" * 80)
