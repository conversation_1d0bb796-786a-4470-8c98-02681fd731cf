﻿# -*- coding: utf-8 -*-
# Decompiled with PyLingual (https://pylingual.io)
# Internal filename: 任务运行文件\A4_人物设定.py
# Bytecode version: 3.10.0rc2 (3439)
# Source timestamp: 1970-01-01 00:00:00 UTC (0)

import json
import os as jduvudu312us_usjlq
import re
import shutil
import requests
import sys

# 设置默认编码
if hasattr(sys, 'setdefaultencoding'):
    sys.setdefaultencoding('utf-8')
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QIcon, QImage, QPixmap, QStandardItem
from PyQt5.QtWidgets import QWidget, QTabWidget, QVBoxLayout, QGroupBox, QGridLayout, QPushButton, QDialog, QScrollArea, QHBoxLayout, QLabel, QFileDialog, QComboBox, QMessageBox, QDialogButtonBox, QLineEdit, QCheckBox, QMenu, QAction
from tkinter import messagebox
from pyz.UI文件.多行输入框组件 import Custom_TextEdit
from pyz.任务运行文件 import A0_config, A26_角色形象
from pyz.任务运行文件.tags import tags, 人物类型, 头发, 面部, 身体及下装, 衣装, 动作, 头部饰品, 室外场景, 室内场景, 建筑, 天空, 视角, 画风
from pyz.任务运行文件.文视GPT推理 import 智普AI翻译

class SubUI(QDialog):

    def __init__(self, mood):
        super().__init__(mood)
        self.setStyleSheet(mood.设置布局())
        self.mood = mood
        # 添加防抖动定时器字典，用于延迟处理文本变化
        self.text_change_timers = {}
        try:
            lora = {}
            lora_xl = {}
            lora_kolors = {}
            lora_flux = {}
            lora_v = {}
            for k, v in A0_config.config['lora'].items():
                if 'xl' in k.lower():
                    lora_xl[k] = v
                if 'kolors' in k.lower():
                    lora_kolors[k] = v
                if 'flux' in k.lower() or 'f.1' in k.lower():
                    lora_flux[k] = v
                if 'xl' not in k.lower() and 'kolors' not in k.lower() and ('flux' not in k.lower()) and ('hunyuan' not in k.lower()) and ('wan' not in k.lower()):
                    lora[k] = v
                if 'wan' in k.lower() or 'hunyuan' in k.lower():
                    lora_v[k] = v
            self.data = {'Flux-Lora': lora_flux, 'Kolors-Lora': lora_kolors, 'SDXL-Lora': lora_xl, 'SD1.5/3-Lora': lora, '视频Lora设置': lora_v, '人物类型': 人物类型, '头发': 头发, '眼睛': 面部, '身体及下装': 身体及下装, '服装': 衣装, '动作': 动作, '头部装饰': 头部饰品, '室外': 室外场景, '室内': 室内场景, '建筑': 建筑, '天空': 天空, '视角': 视角, '画风': 画风}
            self.setWindowTitle('设置人物场景关键词')
            icon_path = A0_config.ico
            if jduvudu312us_usjlq.path.isfile(icon_path):
                icon = QIcon(icon_path)
                self.setWindowIcon(icon)
            self.setWindowFlags(self.windowFlags() & ~Qt.WindowContextHelpButtonHint)
            self.resize(self.mood.缩放(1000), self.mood.缩放(850))
            self.layout = QVBoxLayout()
            self.tab_widget = QTabWidget()
            self.tab_widget.setStyleSheet(f'\n            QTabBar::tab {{background-color: {A0_config.背景色}; color: {A0_config.文字色};{mood.文字样式}}}\n            QTabBar::tab:selected {{background-color: {A0_config.按钮色}; color: {A0_config.文字色};}}\n            QTabBar::tab:hover {{background-color: {A0_config.按钮色};}}\n            QTabWidget::pane {{border: 2px solid {A0_config.按钮色}; }}\n\n            ')
            self.layout.addWidget(self.tab_widget)
            self.tab_values = {}
            self.tabs = []
            self.任务下拉框点击事件()
            self.保存按钮 = QPushButton('保存', self)
            self.保存按钮.setFixedWidth(mood.缩放(150))
            self.保存按钮.setFixedHeight(mood.缩放(30))
            self.保存按钮.clicked.connect(self.baocun)
            self.保存角色 = QPushButton('保存到本地角色库', self)
            self.保存角色.setStyleSheet(f'QPushButton {{background-color: {A0_config.选中色}}}')
            self.保存角色.setFixedWidth(mood.缩放(150))
            self.保存角色.setFixedHeight(mood.缩放(30))
            self.保存角色.clicked.connect(self.baocunrole)
            layout = QHBoxLayout()
            layout.addStretch(1)
            layout.addWidget(self.保存按钮)
            layout.addWidget(self.保存角色)
            layout.addStretch(1)
            self.layout.addLayout(layout)
            self.setLayout(self.layout)
            self.showNormal()
            self.is_maximized = True
        except Exception as e:
            print('修改分镜标签出错:', e)

    def 任务下拉框点击事件(self):
        self.tab_widget.clear()
        while self.tab_widget.count() > 0:
            self.tab_widget.removeTab(0)
        self.tab_values = {}
        self.tabs = []
        keys = []
        for k, v in self.mood.项目配置['fenjing'].items():
            keys.append(k)
        self.tab_values = {key: '' for key in keys}
        self.tabs = keys
        self.create_tabs()

    def create_tabs(self):
        for tab_name in self.tabs:
            tab = QWidget(self)
            tab.setStyleSheet('QWidget {{border: 0px solid red;}}')
            scroll_widget = QWidget()
            layout = QVBoxLayout(scroll_widget)
            scroll_area = QScrollArea()
            scroll_area.setWidgetResizable(True)
            scroll_area.setWidget(scroll_widget)
            main_layout = QVBoxLayout(tab)
            self.create_text_edit(main_layout, tab_name)
            self.create_group_boxes(layout, tab_name)
            self.tab_widget.addTab(tab, tab_name)
            main_layout.addWidget(scroll_area)

        # 设置标签页字体
        font = self.tab_widget.font()
        font.setPointSize(16)
        font.setBold(True)
        font.setFamily("Microsoft YaHei")
        self.tab_widget.setFont(font)
        self.tab_widget.setStyleSheet("QTabWidget::tab-bar { font-size: 16px; font-weight: bold; } QTabBar::tab { font-size: 16px; font-weight: bold; padding: 8px 12px; }")

    def create_group_boxes(self, layout, tab_name):
        for i, item in enumerate(self.data.keys()):
            group_box = QGroupBox(item)
            # 设置分组框标题字体
            font = group_box.font()
            font.setPointSize(16)
            font.setBold(True)
            font.setFamily("Microsoft YaHei")
            group_box.setFont(font)
            grid_layout = QGridLayout(group_box)
            self.create_buttons(grid_layout, item, tab_name, i)
            layout.addWidget(group_box)
            group_box.setCheckable(True)
            group_box.setChecked(False)
            group_box.toggled.connect(lambda checked, buttons=grid_layout: self.toggle_buttons(buttons, checked))
            self.toggle_buttons(grid_layout, False)

    def toggle_buttons(self, layout, checked):
        for i in range(layout.count()):
            widget = layout.itemAt(i).widget()
            if isinstance(widget, QPushButton):
                widget.setVisible(checked)

    def create_buttons(self, layout, item, tab_name, i):
        row = 0
        col = 0
        设定 = ''
        for name in self.tabs:
            if tab_name == name:
                设定 = self.mood.项目配置['fenjing'][name]['guanjianci']
        for key, value in self.data[item].items():
            button = QPushButton(value)
            button.setFixedHeight(self.mood.缩放(30))
            button.setCheckable(True)
            # 设置关键词按钮字体
            font = button.font()
            font.setPointSize(14)
            font.setBold(True)
            font.setFamily("Microsoft YaHei")
            button.setFont(font)
            if key in 设定:
                button.setChecked(True)
            else:
                button.setChecked(False)
            button.clicked.connect(lambda checked, key=key, tab_name=tab_name: self.on_button_clicked(checked, key, tab_name, i))
            layout.addWidget(button, row, col)
            col += 1
            if col == 8 and 'lora' not in item.lower():
                col = 0
                row += 1
            elif col == 4 and 'lora' in item.lower():  # 从2列改为4列，缩小Lora区域
                col = 0
                row += 1

    def on_search_text_changed(self, tab_name, search_results_model):
        for i in range(self.tab_widget.count()):
            if self.tab_widget.tabText(i) == tab_name:
                widget = self.tab_widget.widget(i)
                text_edit = widget.findChild(Custom_TextEdit)
                text = text_edit.toPlainText()
                text = re.split('[,，.。 ]', text)[-1].replace(' ', '')
                if text != '':
                    search_results_model.clear()
                    filtered_data = {key: value for key, value in tags.items() if value not in text_edit.toPlainText()}
                    matches = [f'{key}: {filtered_data[key]}' for key in filtered_data.keys() if text in key]
                    search_results = matches if text else []
                    for result in search_results:
                        item = QStandardItem(result)
                        search_results_model.appendRow(item)
                    return None
                else:
                    search_results_model.clear()

    def on_item_clicked(self, event, search_results_model, tab_name):
        点击的文本 = search_results_model.itemFromIndex(event).text().split(':')[0]
        for i in range(self.tab_widget.count()):
            if self.tab_widget.tabText(i) == tab_name:
                widget = self.tab_widget.widget(i)
                text_edit = widget.findChild(Custom_TextEdit)
                输入框的文本 = text_edit.toPlainText()
                输入框的文本 = ','.join(输入框的文本.split(',')[:len(输入框的文本.split(',')) - 1])
                if tags.get(点击的文本):
                    if 输入框的文本:
                        text_edit.setPlainText(输入框的文本 + ', ' + tags.get(点击的文本) + ', ')
                    else:
                        text_edit.setPlainText(tags.get(点击的文本) + ', ')
                    search_results_model.clear()

    def create_text_edit(self, main_layout, tab_name):
        # 创建主容器
        主容器 = QWidget()
        主布局 = QVBoxLayout(主容器)
        主布局.setContentsMargins(10, 10, 10, 10)
        主布局.setSpacing(10)

        # 上方水平布局区域（主参考图 + 文本框 + 按钮）
        上方区域 = QWidget()
        上方布局 = QHBoxLayout(上方区域)
        上方布局.setContentsMargins(0, 0, 0, 0)
        上方布局.setSpacing(15)

        # 主参考图
        参考图 = QLabel()
        参考图.setFixedWidth(self.mood.缩放(150))
        参考图.setFixedHeight(self.mood.缩放(120))
        参考图.setStyleSheet(f'border: 1px solid {A0_config.边框色}; border-radius: 5px; background-color: rgba(255,255,255,0.1);')
        参考图.setAlignment(Qt.AlignCenter)
        参考图.setText("主参考图")

        setattr(self, f'{tab_name}_参考图组件', 参考图)

        # 文本框
        文本框 = Custom_TextEdit()
        文本框.setFixedHeight(self.mood.缩放(120))
        文本框.setReadOnly(False)
        font = 文本框.font()
        font.setPointSize(18)  # 设置为固定18号字体
        font.setBold(True)
        font.setFamily("Microsoft YaHei")
        文本框.setFont(font)
        文本框.setStyleSheet("""
            QTextEdit {
                padding: 8px;
                line-height: 1.4;
                font-size: 18px;
                font-weight: bold;
            }
        """)

        # 添加文本变化监听事件
        文本框.textChanged.connect(lambda: self.on_text_changed(tab_name, 文本框))

        # 按钮区域
        按钮区域 = QVBoxLayout()

        人物性别 = QComboBox()
        人物性别.setFixedHeight(self.mood.缩放(30))
        人物性别.setMinimumWidth(self.mood.缩放(130))
        # 设置下拉框字体
        font = 人物性别.font()
        font.setPointSize(16)
        font.setBold(True)
        font.setFamily("Microsoft YaHei")
        人物性别.setFont(font)
        人物性别.setStyleSheet("QComboBox { font-size: 16px; font-weight: bold; }")

        人物性别_options = ['本地角色库']
        if A0_config.role.keys():
            人物性别_options = 人物性别_options + list(A0_config.role.keys())
        for option in 人物性别_options:
            人物性别.addItem(option)

        # 添加角色描述优化按钮
        优化描述按钮 = QPushButton('AI优化描述')
        优化描述按钮.setFixedHeight(self.mood.缩放(30))
        优化描述按钮.setMinimumWidth(self.mood.缩放(130))
        优化描述按钮.setToolTip('使用AI大模型优化角色的外貌描述，生成更详细的特征描述')
        优化描述按钮.setStyleSheet(f'QPushButton {{background-color: {A0_config.选中色}; font-weight: bold;}}')
        优化描述按钮.clicked.connect(lambda: self.优化角色描述(tab_name, 文本框))

        固定种子开关 = QCheckBox('启用固定种子')
        固定种子开关.setFixedHeight(self.mood.缩放(25))
        固定种子开关.setToolTip('开启后，该角色将使用固定的种子值生成图片\n右键备选图片可设置种子值')
        # 设置复选框字体
        font = 固定种子开关.font()
        font.setPointSize(16)
        font.setBold(True)
        font.setFamily("Microsoft YaHei")
        固定种子开关.setFont(font)
        固定种子开关.setStyleSheet("QCheckBox { font-size: 16px; font-weight: bold; }")

        if hasattr(self.mood, '项目配置'):
            角色种子配置 = self.mood.项目配置.get('角色专用种子配置', {})
            if tab_name in 角色种子配置:
                固定种子开关.setChecked(角色种子配置[tab_name].get('enabled', False))

        固定种子开关.stateChanged.connect(lambda state: self.切换固定种子状态(tab_name, state == 2))

        button2 = QPushButton('生成参考图', self)
        button2.setToolTip('需要开启ComfyUI才能生成\n使用参考图专用工作流，不受软件设置影响')
        button2.setFixedSize(self.mood.缩放(130), self.mood.缩放(30))
        # 设置按钮字体
        font = button2.font()
        font.setPointSize(16)
        font.setBold(True)
        font.setFamily("Microsoft YaHei")
        button2.setFont(font)

        button = QPushButton('添加参考图', self)
        button.setToolTip('主要用于固定场景风格、统一人物形象\n下载专用SD整个包才可用, \n或者网盘下载参考图模型到controlnet文件夹\nWebUI参考图会影响整个画面\nComfyUI只会提取人物特征')
        button.setFixedSize(self.mood.缩放(130), self.mood.缩放(30))
        # 设置按钮字体
        font = button.font()
        font.setPointSize(16)
        font.setBold(True)
        font.setFamily("Microsoft YaHei")
        button.setFont(font)

        button1 = QPushButton('删除参考图', self)
        button1.setFixedSize(self.mood.缩放(130), self.mood.缩放(30))
        # 设置按钮字体
        font = button1.font()
        font.setPointSize(16)
        font.setBold(True)
        font.setFamily("Microsoft YaHei")
        button1.setFont(font)

        # 一键拼接参考图按钮
        拼接按钮 = QPushButton('一键拼接参考图', self)
        拼接按钮.setToolTip('自动拼接所有可能的角色组合参考图\n用于多角色场景的浏览器绘图方案')
        拼接按钮.setFixedSize(self.mood.缩放(130), self.mood.缩放(30))
        # 设置按钮样式，让它更显眼
        拼接按钮.setStyleSheet("""
            QPushButton {
                background-color: #FF6B6B;
                color: white;
                border: 2px solid #FF5252;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #FF5252;
            }
            QPushButton:pressed {
                background-color: #E53935;
            }
        """)
        # 设置按钮字体
        font = 拼接按钮.font()
        font.setPointSize(16)
        font.setBold(True)
        font.setFamily("Microsoft YaHei")
        拼接按钮.setFont(font)

        if tab_name == '全局':
            button2.hide()

        按钮区域.addWidget(人物性别)
        按钮区域.addWidget(优化描述按钮)
        按钮区域.addWidget(固定种子开关)
        按钮区域.addWidget(button2)
        按钮区域.addWidget(button)
        按钮区域.addWidget(button1)
        按钮区域.addWidget(拼接按钮)

        # 组装上方水平布局
        上方布局.addWidget(参考图)
        上方布局.addWidget(文本框)
        上方布局.addLayout(按钮区域)

        # 下方备选图片区域
        下方区域 = QVBoxLayout()
        下方标签 = QLabel("备选图片:")
        下方标签.setFixedHeight(self.mood.缩放(25))
        下方标签.setStyleSheet("font-weight: bold; font-size: 16px;")
        # 设置标签字体
        font = 下方标签.font()
        font.setPointSize(16)
        font.setBold(True)
        font.setFamily("Microsoft YaHei")
        下方标签.setFont(font)

        下方滚动 = QScrollArea()
        下方滚动.setMinimumHeight(self.mood.缩放(200))  # 增加最小高度
        下方滚动.setMaximumHeight(self.mood.缩放(400))  # 增加最大高度
        下方滚动.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        下方滚动.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        下方滚动.setWidgetResizable(True)  # 确保内容可以调整大小
        下方滚动.setStyleSheet(f"""
            QScrollArea {{
                border: 2px solid {A0_config.边框色};
                border-radius: 5px;
                background-color: rgba(255, 255, 255, 0.1);
            }}
            QScrollArea QWidget {{
                background-color: transparent;
            }}
        """)

        下方容器 = QWidget()
        下方备选布局 = QGridLayout(下方容器)
        下方备选布局.setContentsMargins(5, 5, 5, 5)
        下方备选布局.setSpacing(5)

        setattr(self, f'{tab_name}_备选图片主布局', 下方备选布局)
        setattr(self, f'{tab_name}_备选图片主容器', 下方容器)
        setattr(self, f'{tab_name}_备选图片滚动区域', 下方滚动)  # 保存滚动区域引用

        下方滚动.setWidget(下方容器)
        下方区域.addWidget(下方标签)
        下方区域.addWidget(下方滚动)

        # 组装主布局
        主布局.addWidget(上方区域)
        主布局.addLayout(下方区域)

        # 初始化参考图数据
        for name in self.tabs:
            if tab_name == name:
                for k, v in self.mood.项目配置['fenjing'].items():
                    if k == name:
                        关键词 = v.get('guanjianci_zh') if v.get('guanjianci_zh') else v.get('guanjianci')
                        # 临时断开信号连接，避免初始化时触发文本变化事件
                        文本框.textChanged.disconnect()
                        文本框.setText(关键词)
                        # 重新连接信号
                        文本框.textChanged.connect(lambda: self.on_text_changed(tab_name, 文本框))

                        # 加载主参考图
                        image_path = v.get('img')
                        if image_path and jduvudu312us_usjlq.path.exists(image_path):
                            image = QImage(image_path)
                            scaled_image = image.scaled(self.mood.缩放(140), self.mood.缩放(110), Qt.KeepAspectRatio)
                            参考图.setPixmap(QPixmap.fromImage(scaled_image))
                            参考图.setAlignment(Qt.AlignCenter)
                            参考图.mouseDoubleClickEvent = lambda event, extra_arg=image_path: self.image_double_clicked(event, extra_arg)

                        # 加载备选图片
                        备选图片列表 = v.get('备选图片', [])
                        # print(f"🔍 初始化加载 {tab_name} 的备选图片: {len(备选图片列表)} 张")

                        # 如果没有备选图片，尝试自动扫描角色文件夹
                        if not 备选图片列表:
                            备选图片列表 = self.自动扫描角色备选图片(tab_name)
                            if 备选图片列表:
                                v['备选图片'] = 备选图片列表
                                self.mood.保存配置()
                                # print(f"✅ 自动扫描找到 {len(备选图片列表)} 张备选图片")

                        # 如果主参考图存在但不在备选图片中，添加进去
                        if image_path and jduvudu312us_usjlq.path.exists(image_path):
                            if image_path not in 备选图片列表:
                                备选图片列表.append(image_path)
                                v['备选图片'] = 备选图片列表
                                self.mood.保存配置()
                                # print(f"✅ 将主参考图添加到备选图片列表")

                        # 最终检查
                        # print(f"📋 {tab_name} 最终备选图片列表: {len(备选图片列表)} 张")
                        # for i, img_path in enumerate(备选图片列表):
                        #     存在状态 = "✅" if jduvudu312us_usjlq.path.exists(img_path) else "❌"
                        #     print(f"   {i+1}. {存在状态} {jduvudu312us_usjlq.path.basename(img_path)}")

                        # 加载备选图片
                        if 备选图片列表:
                            self.加载主备选图片(tab_name, 备选图片列表)
                        else:
                            # print(f"⚠️ {tab_name} 没有备选图片，显示提示信息")
                            self.显示主备选图片提示(tab_name)

                # 连接事件
                button.clicked.connect(lambda: self.tihuantupian(tab_name, 参考图))
                button1.clicked.connect(lambda: self.shanchu(tab_name, 参考图))
                button2.clicked.connect(lambda: self.shengchengtupian(tab_name, 参考图, button2))
                人物性别.activated.connect(lambda: self.jiazaijuese(人物性别, tab_name, 参考图, 文本框))

        # 拼接按钮事件连接（移到外面，确保所有tab都能使用）
        def 拼接按钮点击():
            print(f"🔧 拼接按钮被点击 - tab: {tab_name}")
            try:
                print("🔧 准备调用一键拼接参考图方法...")
                self.一键拼接参考图()
                print("🔧 一键拼接参考图方法调用完成")
            except Exception as e:
                print(f"❌ 调用一键拼接参考图方法失败: {e}")
                import traceback
                traceback.print_exc()

        拼接按钮.clicked.connect(拼接按钮点击)
        print(f"✅ 拼接按钮事件已连接 - tab: {tab_name}")

        main_layout.addWidget(主容器)

    def 加载备选图片(self, tab_name, 备选图片列表):
        """已废弃：小备选图片区域已移除，转发到主备选图片加载"""
        self.加载主备选图片(tab_name, 备选图片列表)

    def 加载主备选图片(self, tab_name, 备选图片列表):
        """加载备选图片到文本框下方的主区域"""
        # print(f"🖼️ 开始加载主备选图片: {tab_name}, 图片数量: {len(备选图片列表)}")

        # 获取布局组件
        属性名 = f'{tab_name}_备选图片主布局'
        备选图片主布局 = getattr(self, 属性名, None)
        # print(f"🔧 通过getattr获取布局: {备选图片主布局}")

        if 备选图片主布局 is None:
            备选图片主布局 = self.__dict__.get(属性名, None)
            # print(f"🔧 通过__dict__获取布局: {备选图片主布局}")

        if 备选图片主布局 is None:
            # print(f"❌ 无法获取备选图片主布局: {tab_name}")
            # print(f"🔍 可用属性: {[k for k in self.__dict__.keys() if '备选图片主布局' in k]}")
            return

        # 清空现有备选图片
        while 备选图片主布局.count():
            child = 备选图片主布局.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

        # print(f"✅ 获取到备选图片主布局: {备选图片主布局}")

        # 如果没有备选图片，显示提示
        if not 备选图片列表:
            # print(f"⚠️ 备选图片列表为空，显示提示")
            self.显示主备选图片提示(tab_name)
            return

        # print(f"🔄 开始添加 {len(备选图片列表)} 张备选图片到布局")

        # 添加备选图片 - 使用网格布局
        每行数量 = 6  # 每行显示6张图片
        添加计数 = 0

        for i, 图片路径 in enumerate(备选图片列表):
            # print(f"   📷 处理图片 {i+1}: {图片路径}")

            if 图片路径 and jduvudu312us_usjlq.path.exists(图片路径):
                行号 = i // 每行数量
                列号 = i % 每行数量
                # print(f"   ✅ 图片存在，添加到位置 ({行号}, {列号})")

                备选图片标签 = CustomImageLabel(self)
                备选图片标签.setFixedSize(self.mood.缩放(120), self.mood.缩放(120))
                备选图片标签.setStyleSheet(f"""
                    QLabel {{
                        border: 2px solid {A0_config.边框色};
                        border-radius: 5px;
                        background-color: rgba(255, 255, 255, 0.1);
                    }}
                    QLabel:hover {{
                        border: 2px solid {A0_config.选中色};
                        background-color: rgba(255, 255, 255, 0.2);
                    }}
                """)

                try:
                    image = QImage(图片路径)
                    # print(f"       📸 QImage加载: isNull={image.isNull()}, 尺寸={image.width()}x{image.height()}")

                    if not image.isNull():
                        scaled_image = image.scaled(self.mood.缩放(115), self.mood.缩放(115), Qt.KeepAspectRatio)
                        pixmap = QPixmap.fromImage(scaled_image)
                        # print(f"       🖼️ QPixmap创建: isNull={pixmap.isNull()}, 尺寸={pixmap.width()}x{pixmap.height()}")

                        备选图片标签.setPixmap(pixmap)
                        备选图片标签.setAlignment(Qt.AlignCenter)

                        # 验证是否成功设置
                        current_pixmap = 备选图片标签.pixmap()
                        if current_pixmap and not current_pixmap.isNull():
                            # print(f"       ✅ 图片成功设置到QLabel")
                            pass
                        else:
                            # print(f"       ❌ 图片设置到QLabel失败")
                            备选图片标签.setText("设置\n失败")
                    else:
                        # print(f"       ❌ QImage加载失败")
                        备选图片标签.setText("图片\n加载\n失败")
                        备选图片标签.setAlignment(Qt.AlignCenter)
                except Exception as e:
                    # print(f"       ❌ 图片加载异常: {e}")
                    备选图片标签.setText("异常\n错误")
                    备选图片标签.setAlignment(Qt.AlignCenter)

                # 确保QLabel可见并强制刷新
                备选图片标签.setVisible(True)
                备选图片标签.show()
                备选图片标签.raise_()
                备选图片标签.update()
                备选图片标签.repaint()
                # print(f"       🔧 QLabel状态: 可见={备选图片标签.isVisible()}, 尺寸={备选图片标签.size()}")

                # 设置必要的属性
                备选图片标签.图片路径 = 图片路径
                备选图片标签.tab_name = tab_name
                备选图片标签.parent_widget = self

                备选图片标签.setToolTip(f"左键: 设为主参考图\n右键: 设置固定种子\n{jduvudu312us_usjlq.path.basename(图片路径)}")

                备选图片主布局.addWidget(备选图片标签, 行号, 列号)
                添加计数 += 1
                # print(f"   ✅ 成功添加图片标签到布局，总计: {添加计数}")
            else:
                # print(f"   ❌ 图片不存在或路径为空: {图片路径}")
                pass

        # print(f"🎯 备选图片加载完成: {tab_name}, 成功添加 {添加计数} 张图片")

        # 强制刷新容器和滚动区域
        if hasattr(self, f'{tab_name}_备选图片主容器'):
            容器 = getattr(self, f'{tab_name}_备选图片主容器')

            # 设置容器的最小大小，确保内容可见
            需要高度 = ((添加计数 - 1) // 每行数量 + 1) * (self.mood.缩放(120) + 5) + 10
            容器.setMinimumHeight(需要高度)

            # 强制更新和重绘
            容器.update()
            容器.repaint()
            容器.show()

            # 刷新布局
            备选图片主布局.update()

            # print(f"🔄 刷新容器: {容器}, 设置高度: {需要高度}")

            # 直接刷新滚动区域
            if hasattr(self, f'{tab_name}_备选图片滚动区域'):
                滚动区域 = getattr(self, f'{tab_name}_备选图片滚动区域')
                滚动区域.update()
                滚动区域.repaint()
                滚动区域.updateGeometry()
                # 确保滚动区域正确显示内容
                滚动区域.ensureWidgetVisible(容器)
                # print(f"🔄 直接刷新滚动区域: {滚动区域}")
            # else:
                # print(f"⚠️ 未找到滚动区域引用: {tab_name}")
        # else:
            # print(f"⚠️ 未找到备选图片主容器: {tab_name}")

    def 显示主备选图片提示(self, tab_name):
        """在主备选图片区域显示提示信息"""
        备选图片主布局 = getattr(self, f'{tab_name}_备选图片主布局', None)
        if not 备选图片主布局:
            return

        # 创建提示标签
        提示标签 = QLabel("暂无备选图片\n点击右侧'生成参考图'按钮创建备选图片")
        提示标签.setFixedSize(self.mood.缩放(400), self.mood.缩放(100))
        提示标签.setAlignment(Qt.AlignCenter)
        提示标签.setStyleSheet(f"""
            QLabel {{
                color: {A0_config.文字色};
                background-color: rgba(128, 128, 128, 0.1);
                border: 2px dashed {A0_config.边框色};
                border-radius: 10px;
                font-size: 16px;
                font-weight: bold;
                font-family: 'Microsoft YaHei';
                line-height: 1.5;
            }}
        """)

        备选图片主布局.addWidget(提示标签, 0, 0, 1, 5)  # 跨5列显示
        # print(f"   💡 已为 {tab_name} 显示主备选图片提示")

    def 自动扫描角色备选图片(self, tab_name):
        """自动扫描角色文件夹中的备选图片"""
        # print(f"🔍 自动扫描 {tab_name} 的备选图片")

        try:
            # 构建角色文件夹路径
            角色文件夹 = jduvudu312us_usjlq.path.join(
                self.mood.当前项目文件夹,
                "角色参考图片",
                tab_name
            )

            # print(f"   📁 扫描路径: {角色文件夹}")

            if not jduvudu312us_usjlq.path.exists(角色文件夹):
                # print(f"   ❌ 角色文件夹不存在")
                return []

            # 扫描图片文件
            备选图片列表 = []
            支持格式 = ['.png', '.jpg', '.jpeg', '.bmp', '.gif']

            for 文件名 in jduvudu312us_usjlq.listdir(角色文件夹):
                文件路径 = jduvudu312us_usjlq.path.join(角色文件夹, 文件名)
                if jduvudu312us_usjlq.path.isfile(文件路径):
                    文件扩展名 = jduvudu312us_usjlq.path.splitext(文件名)[1].lower()
                    if 文件扩展名 in 支持格式:
                        备选图片列表.append(文件路径)
                        # print(f"   ✅ 找到图片: {文件名}")

            # 按文件名排序
            备选图片列表.sort()

            # print(f"   📊 扫描结果: 共找到 {len(备选图片列表)} 张图片")
            return 备选图片列表

        except Exception as e:
            # print(f"   ❌ 扫描失败: {e}")
            return []

    def 显示备选图片提示(self, tab_name):
        """在备选图片区域显示提示信息"""
        备选图片布局 = getattr(self, f'{tab_name}_备选图片布局', None)
        if not 备选图片布局:
            return

        # 清空现有内容
        while 备选图片布局.count():
            child = 备选图片布局.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

        # 创建提示标签
        提示标签 = QLabel("点击'生成参考图'创建备选图片")
        提示标签.setFixedSize(self.mood.缩放(200), self.mood.缩放(80))
        提示标签.setAlignment(Qt.AlignCenter)
        提示标签.setStyleSheet(f"""
            QLabel {{
                color: {A0_config.文字色};
                background-color: rgba(128, 128, 128, 0.1);
                border: 2px dashed {A0_config.边框色};
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
                font-family: 'Microsoft YaHei';
            }}
        """)

        备选图片布局.addWidget(提示标签)
        备选图片布局.addStretch()
        # print(f"   💡 已为 {tab_name} 显示备选图片提示")

    def 设置为主参考图(self, tab_name, 图片路径):
        """将备选图片设置为主参考图"""
        # 找到对应的主参考图组件
        for i in range(self.tab_widget.count()):
            if self.tab_widget.tabText(i) == tab_name:
                widget = self.tab_widget.widget(i)
                参考图组件 = None

                # 查找参考图组件
                for child in widget.findChildren(QLabel):
                    if (child.minimumWidth() == self.mood.缩放(150) and
                        child.minimumHeight() == self.mood.缩放(120)):
                        参考图组件 = child
                        break

                if 参考图组件:
                    # 更新主参考图显示
                    image = QImage(图片路径)
                    scaled_image = image.scaled(self.mood.缩放(115), self.mood.缩放(115), Qt.KeepAspectRatio)
                    参考图组件.setPixmap(QPixmap.fromImage(scaled_image))
                    参考图组件.setAlignment(Qt.AlignCenter)

                    # 更新配置中的主参考图路径
                    for k, v in self.mood.项目配置['fenjing'].items():
                        if k == tab_name:
                            v['img'] = 图片路径
                            break

                    self.mood.保存配置()
                    print(f"✅ 已将 {jduvudu312us_usjlq.path.basename(图片路径)} 设为 {tab_name} 的主参考图")
                break

    def jiazaijuese(self, 人物性别, tab_name, 参考图, 文本框):
        选中角色 = 人物性别.currentText()
        if 选中角色 != '本地角色库' and 选中角色:
            关键词 = A0_config.role[选中角色].get('guanjianci') if A0_config.role[选中角色].get('guanjianci') else ''
            img = A0_config.参考图改文件名(A0_config.role[选中角色].get('img')) if A0_config.参考图改文件名(A0_config.role[选中角色].get('img')) else ''
            url = A0_config.role[选中角色].get('url') if A0_config.role[选中角色].get('url') else ''
            文本框.setText(关键词)
            image_path = ''
            for index, name in enumerate(self.tabs):
                if tab_name == name:
                    if img and jduvudu312us_usjlq.path.exists(img):
                        image_path = jduvudu312us_usjlq.path.join(self.mood.当前项目文件夹, f'{tab_name}.png')
                        shutil.copy(img, image_path)
                        image = QImage(image_path)
                        scaled_image = image.scaled(self.mood.缩放(145), self.mood.缩放(145), Qt.KeepAspectRatio)
                        参考图.setPixmap(QPixmap.fromImage(scaled_image))
                        参考图.setAlignment(Qt.AlignCenter)
                        参考图.mouseDoubleClickEvent = lambda event, extra_arg=image_path: self.image_double_clicked(event, extra_arg)
                    else:
                        参考图.setPixmap(QPixmap())
                    for k, v in self.mood.项目配置['fenjing'].items():
                        if tab_name == k:
                            v['img'] = image_path
                            v['url'] = url
                            # 加载备选图片
                            备选图片列表 = v.get('备选图片', [])
                            if 备选图片列表:
                                self.加载备选图片(tab_name, 备选图片列表)
                    self.mood.保存配置()

    def tihuantupian(self, tab_name, 参考图):
        try:
            options = QFileDialog.Options()
            file_dialog = QFileDialog()
            file_dialog.setNameFilter('PNG Files (*.png *.jpg *.jpeg)')
            file_dialog.setFileMode(QFileDialog.ExistingFile)
            selected_file, _ = file_dialog.getOpenFileName(self, '选择PNG图片', '', 'PNG Files (*.png *.jpg *.jpeg)', options=options)
            if selected_file != '':
                for index, name in enumerate(self.tabs):
                    if tab_name == name:
                        # 创建角色参考图片文件夹
                        角色参考图文件夹 = jduvudu312us_usjlq.path.join(self.mood.当前项目文件夹, '角色参考图片', tab_name)
                        jduvudu312us_usjlq.makedirs(角色参考图文件夹, exist_ok=True)

                        # 生成随机种子数
                        import random
                        随机种子 = random.randint(1000000000, 9999999999)

                        # 生成新的文件名：角色名_seed随机数.png
                        新文件名 = f"{tab_name}_seed{随机种子}.png"
                        image_path = jduvudu312us_usjlq.path.join(角色参考图文件夹, 新文件名)

                        # 复制文件到新位置
                        shutil.copy(selected_file, image_path)
                        print(f"✅ 参考图已保存到: {image_path}")

                        # 更新UI显示
                        image = QImage(image_path)
                        scaled_image = image.scaled(self.mood.缩放(145), self.mood.缩放(145), Qt.KeepAspectRatio)
                        参考图.setPixmap(QPixmap.fromImage(scaled_image))
                        参考图.setAlignment(Qt.AlignCenter)
                        参考图.mouseDoubleClickEvent = lambda event, extra_arg=image_path: self.image_double_clicked(event, extra_arg)

                        # 更新项目配置
                        for k, v in self.mood.项目配置['fenjing'].items():
                            if tab_name == k:
                                v['img'] = image_path
                                v['url'] = ''
                                v['id'] = ''

                                # 更新备选图片列表
                                if '备选图片' not in v:
                                    v['备选图片'] = []
                                if image_path not in v['备选图片']:
                                    v['备选图片'].append(image_path)

                        # 刷新备选图片显示
                        self.刷新备选图片列表(tab_name)

                        self.mood.保存配置()
                        break
        except Exception as e:
            print(f'替换图片错误{e}')
            messagebox.showinfo('提示', f'替换图片错误{e}')

    def shanchu(self, tab_name, 参考图):
        """删除主参考图，并自动从备选图片中选择一张作为新的主参考图"""
        # 清除UI上的主参考图显示
        参考图.setPixmap(QPixmap())

        for index, name in enumerate(self.tabs):
            if tab_name == name:
                for k, v in self.mood.项目配置['fenjing'].items():
                    if k == tab_name:
                        # 删除本地主参考图文件
                        if v['img'] and jduvudu312us_usjlq.path.exists(v['img']):
                            try:
                                jduvudu312us_usjlq.remove(v['img'])
                                print(f"✅ 已删除主参考图文件: {v['img']}")
                            except Exception as e:
                                print(f"❌ 删除主参考图文件失败: {v['img']}, 错误: {e}")

                        # 清空配置中的图片相关信息
                        v['img'] = ''
                        v['url'] = ''
                        v['id'] = ''

                        # 从备选图片中选择第一张作为新的主参考图
                        if '备选图片' in v and v['备选图片']:
                            备选图片路径 = v['备选图片'][0]
                            if jduvudu312us_usjlq.path.exists(备选图片路径):
                                # 更新配置
                                v['img'] = 备选图片路径

                                # 更新UI显示
                                image = QImage(备选图片路径)
                                scaled_image = image.scaled(self.mood.缩放(115), self.mood.缩放(115), Qt.KeepAspectRatio)
                                参考图.setPixmap(QPixmap.fromImage(scaled_image))
                                参考图.setAlignment(Qt.AlignCenter)
                                参考图.mouseDoubleClickEvent = lambda event, extra_arg=备选图片路径: self.image_double_clicked(event, extra_arg)
                                print(f"✅ 已将备选图片设为新的主参考图: {备选图片路径}")

                        # 刷新备选图片显示
                        self.刷新备选图片列表(tab_name)
                        break

        # 保存更新后的配置
        self.mood.保存配置()
        print(f"✅ 已完成删除和更新 {tab_name} 的主参考图")

    def 获取智能提示词(self, tab_name, 当前输入):
        """
        智能获取提示词，统一英文优先策略
        1. 优先使用有效的英文关键词
        2. 如果英文为空或包含中文，则翻译中文关键词并更新配置
        3. 如果都没有，使用当前输入
        """
        # 从配置中获取角色的关键词
        角色配置 = None
        for k, v in self.mood.项目配置['fenjing'].items():
            if k == tab_name:
                角色配置 = v
                break

        if not 角色配置:
            print(f"   ⚠️  未找到角色 {tab_name} 的配置，使用当前输入")
            return 当前输入

        英文关键词 = 角色配置.get('guanjianci', '')
        中文关键词 = 角色配置.get('guanjianci_zh', '')

        # 检查英文关键词是否真的是英文（不包含中文字符）
        import re
        if 英文关键词 and not re.search(r'[\u4e00-\u9fff]', 英文关键词):
            print(f"   ✅ 使用有效的英文关键词")
            return 英文关键词

        # 英文关键词无效，需要翻译中文关键词
        if 中文关键词:
            print(f"   🔤 英文关键词无效或为空，翻译中文关键词...")
            翻译结果 = self.翻译并更新英文关键词(tab_name, 中文关键词)
            if 翻译结果:
                print(f"   ✅ 翻译成功，使用翻译后的英文关键词")
                return 翻译结果
            else:
                print(f"   ⚠️  翻译失败，使用原中文关键词")
                return 中文关键词

        # 都没有有效关键词
        print(f"   ⚠️  配置中无有效关键词，使用当前输入")
        return 当前输入

    def 翻译并更新英文关键词(self, tab_name, 中文关键词, 最大重试次数=5):
        """
        翻译中文关键词为英文，并更新配置文件
        添加自动重试机制，直到翻译成功
        """
        import time

        for 重试次数 in range(最大重试次数):
            try:
                print(f"   🔄 翻译尝试 {重试次数 + 1}/{最大重试次数}")

                # 优先使用新的翻译管理器
                翻译结果 = None
                try:
                    from pyz.任务运行文件.A20_翻译管理 import 统一翻译

                    # 获取翻译配置
                    翻译平台 = A0_config.config.get('translation_platform', '')
                    if 翻译平台 and 翻译平台 != '本地翻译':
                        method = 'platform'  # 使用平台管理器
                    else:
                        # 回退到旧的配置方式
                        翻译方式 = A0_config.config.get('translation_method', '自动选择')
                        method_map = {'自动选择': 'auto', '本地翻译': 'local', 'SiliconFlow': 'siliconflow'}
                        method = method_map.get(翻译方式, 'auto')

                    翻译结果 = 统一翻译(中文关键词, 'English', method)
                    print(f"   🔤 使用翻译管理器翻译: {method}")

                except Exception as e:
                    print(f"   ⚠️  翻译管理器失败，使用智普AI翻译: {e}")
                    # 回退到智普AI翻译
                    from pyz.任务运行文件.文视GPT推理 import 智普AI翻译
                    翻译结果 = 智普AI翻译(self.mood, 中文关键词)

                # 验证翻译结果
                if 翻译结果 and 翻译结果.strip() and 翻译结果 != 中文关键词:
                    # 检查翻译结果是否包含中文（确保翻译成功）
                    import re
                    if not bool(re.search(r'[\u4e00-\u9fff]', 翻译结果)):
                        翻译后的英文 = 翻译结果.strip()

                        # 更新配置中的英文关键词
                        for k, v in self.mood.项目配置['fenjing'].items():
                            if k == tab_name:
                                v['guanjianci'] = 翻译后的英文
                                print(f"   💾 已更新角色 {tab_name} 的英文关键词到配置")
                                break

                        # 保存配置
                        self.mood.保存配置()
                        print(f"   ✅ 翻译成功！")
                        return 翻译后的英文
                    else:
                        print(f"   ⚠️  翻译结果仍包含中文，继续重试...")
                else:
                    print(f"   ⚠️  翻译结果无效，继续重试...")

            except Exception as e:
                print(f"   ❌ 翻译尝试 {重试次数 + 1} 失败: {e}")

            # 如果不是最后一次重试，等待一段时间再重试
            if 重试次数 < 最大重试次数 - 1:
                等待时间 = (重试次数 + 1) * 2  # 递增等待时间：2秒、4秒、6秒、8秒
                print(f"   ⏳ 等待 {等待时间} 秒后重试...")
                time.sleep(等待时间)

        # 所有重试都失败
        print(f"   ❌ 经过 {最大重试次数} 次重试仍然翻译失败，保持原有英文关键词不变")
        return None

    def on_text_changed(self, tab_name, 文本框):
        """
        文本编辑器内容变化时的处理函数
        使用防抖动机制，避免频繁触发
        """
        try:
            # 取消之前的定时器
            if tab_name in self.text_change_timers:
                self.text_change_timers[tab_name].cancel()

            # 创建新的延迟处理定时器
            import threading
            timer = threading.Timer(1.0, self.处理文本变化, args=[tab_name, 文本框])
            self.text_change_timers[tab_name] = timer
            timer.start()

        except Exception as e:
            print(f"   ❌ 文本变化事件处理失败: {e}")

    def 处理文本变化(self, tab_name, 文本框):
        """
        实际处理文本变化的函数
        用户只会输入中文，系统需要同时更新中英文两个字段
        """
        try:
            # 获取当前文本内容
            当前文本 = 文本框.toPlainText().strip()
            if not 当前文本:
                return

            print(f"🔄 处理文本变化: {tab_name}")
            print(f"   新内容: {当前文本[:50]}...")

            # 检查是否包含中文
            import re
            包含中文 = bool(re.search(r'[\u4e00-\u9fff]', 当前文本))

            # 更新配置
            for k, v in self.mood.项目配置['fenjing'].items():
                if k == tab_name:
                    if 包含中文:
                        # 用户输入中文，需要同时更新两个字段
                        print(f"   🔤 检测到中文输入，同时更新中英文字段")

                        # 1. 更新中文字段
                        v['guanjianci_zh'] = 当前文本
                        print(f"   ✅ 已更新中文字段")

                        # 2. 启动翻译更新英文字段
                        print(f"   🔄 启动翻译更新英文字段...")
                        self.启动后台翻译(tab_name, 当前文本)

                    else:
                        # 输入是英文（虽然用户说只会输入中文，但保留兼容性）
                        print(f"   ✅ 检测到英文输入，更新英文字段")
                        v['guanjianci'] = 当前文本

                    # 保存配置
                    self.mood.保存配置()
                    break

            # 清理定时器
            if tab_name in self.text_change_timers:
                del self.text_change_timers[tab_name]

        except Exception as e:
            print(f"   ❌ 文本变化处理失败: {e}")

    def 启动后台翻译(self, tab_name, 中文文本):
        """
        启动后台翻译任务，避免阻塞UI
        """
        try:
            import threading

            def 翻译任务():
                try:
                    print(f"   🔤 开始翻译: {中文文本[:30]}...")
                    翻译结果 = self.翻译并更新英文关键词(tab_name, 中文文本)
                    if 翻译结果:
                        print(f"   ✅ 翻译完成: {翻译结果[:30]}...")
                    else:
                        print(f"   ⚠️  翻译失败或无变化")
                except Exception as e:
                    print(f"   ❌ 后台翻译失败: {e}")

            # 启动后台线程
            翻译线程 = threading.Thread(target=翻译任务, daemon=True)
            翻译线程.start()

        except Exception as e:
            print(f"   ❌ 启动后台翻译失败: {e}")

    def shengchengtupian(self, tab_name, 参考图, button1):
        image_path = jduvudu312us_usjlq.path.join(self.mood.当前项目文件夹, f'{tab_name}.png')
        result = {}
        for i in range(self.tab_widget.count()):
            tab_name1 = self.tab_widget.tabText(i)
            widget = self.tab_widget.widget(i)
            text_edit = widget.findChild(Custom_TextEdit)
            input_text = text_edit.toPlainText()
            result[tab_name1] = input_text
        # 先获取智能提示词，避免被文本编辑器内容覆盖
        提示词 = self.获取智能提示词(tab_name, result[tab_name])

        # 更新配置 - 但不覆盖已翻译的英文关键词
        for name in self.tabs:
            for k, v in self.mood.项目配置['fenjing'].items():
                if k == name:
                    # 检查当前输入是否为中文，如果是中文则不覆盖已有的英文关键词
                    当前输入 = result[name]
                    import re
                    if re.search(r'[\u4e00-\u9fff]', 当前输入):
                        # 当前输入包含中文，检查是否已有有效英文关键词
                        现有英文 = v.get('guanjianci', '')
                        if 现有英文 and not re.search(r'[\u4e00-\u9fff]', 现有英文):
                            # 已有有效英文关键词，不覆盖，只更新中文版本
                            v['guanjianci_zh'] = 当前输入
                            print(f"   💾 保留英文关键词，更新中文版本: {name}")
                        else:
                            # 🛡️ 修复：没有有效英文关键词时，中文内容不能直接写入guanjianci字段
                            # 中文内容只保存到中文字段，启动翻译更新英文字段
                            v['guanjianci_zh'] = 当前输入
                            print(f"   🔄 启动翻译更新英文字段: {name}")
                            self.启动后台翻译(name, 当前输入)
                    else:
                        # 当前输入是英文，正常更新
                        v['guanjianci'] = 当前输入
        self.mood.保存配置()
        lora词 = re.findall('<lora:[^>]+>', 提示词)
        提示词 = re.sub('<lora:[^>]+>', '', 提示词).strip()
        print(f"🔤 使用的提示词: {提示词[:100]}...")
        print(f"🏷️  提取的LORA词: {lora词}")
        if result[tab_name] == '':
            messagebox.showinfo('提示', '请先设置分镜词')
            return
        # 检查是否启用了Pollinations云端模型
        使用云端模型 = A0_config.config.get('使用Pollinations', False)

        if not 使用云端模型:
            # 只有在未启用云端模型时才检查SD服务
            try:
                if not requests.get(A0_config.config['云端地址'], timeout=10):
                    messagebox.showinfo('提示', '请先启动SD')
                    return
            except:
                messagebox.showinfo('提示', '请先启动SD')
                return None
        else:
            print("☁️  检测到启用Pollinations云端模型，跳过SD服务检查")
        button1.setText('图片绘制中')
        button1.setStyleSheet(f'QPushButton {{background-color: {A0_config.选中色}; color: {A0_config.文字色};}}')
        # 获取角色专用种子
        角色专用种子 = ''
        if hasattr(self.mood, '获取角色专用种子'):
            角色专用种子 = self.mood.获取角色专用种子(tab_name)

        # 如果没有专用种子，尝试从fenjing配置中获取
        if not 角色专用种子:
            for k, v in self.mood.项目配置['fenjing'].items():
                if k == tab_name:
                    zhongzi = v.get('zhongzi', '')
                    if zhongzi:
                        # 处理不同类型的种子值
                        if isinstance(zhongzi, int):
                            角色专用种子 = zhongzi
                            print(f"   ⚠️  使用角色[{tab_name}]的配置种子: {角色专用种子}")
                        elif isinstance(zhongzi, str) and zhongzi.isdigit():
                            角色专用种子 = int(zhongzi)
                            print(f"   ⚠️  使用角色[{tab_name}]的配置种子: {角色专用种子}")
                        else:
                            print(f"   ❌ 种子值格式无效: {zhongzi} (类型: {type(zhongzi)})")
                    break

        # 如果仍然没有种子，使用随机种子
        if not 角色专用种子:
            import random
            角色专用种子 = random.randint(1000000000, 9999999999)
            print(f"   🎲 为角色[{tab_name}]生成随机种子: {角色专用种子}")

        表格数据 = {'当前行': 0, '当前编号': 0, '提示词': 提示词, '标签词': '', 'lora': lora词, '图片路径': image_path, '原图片路径': '', '备选图片路径': [], '随机种子': 角色专用种子, '视频路径': '', '音频路径': '', '人物分镜': [], '视频提示词': 提示词, '辅助图片': jduvudu312us_usjlq.path.join(jduvudu312us_usjlq.getcwd(), '角色辅助.png'), '视频尾帧': '', '辅助视频': '', '角色名': tab_name, '生成数量': 1}
        self.mood.tanchuang = True
        self.保存分镜()
        self.mood.worker_solo.add_task('参考图', (0, 表格数据, tab_name, image_path, 参考图, button1))

    def 画图返回(self, tab_name, image_path, 参考图, button1, 修改内容):
        print(f"📸 画图返回被调用: {tab_name}, {image_path}, 修改内容: {修改内容}")

        # 首先验证图片是否真正生成成功
        图片生成成功 = False
        if image_path and jduvudu312us_usjlq.path.exists(image_path):
            try:
                # 验证图片文件完整性
                from PIL import Image
                with Image.open(image_path) as test_img:
                    test_img.verify()  # 验证图片完整性

                # 检查文件大小（确保不是空文件或损坏文件）
                文件大小 = jduvudu312us_usjlq.path.getsize(image_path)
                if 文件大小 > 1024:  # 至少1KB
                    图片生成成功 = True
                    print(f"✅ 图片验证成功: {image_path} (大小: {文件大小} bytes)")
                else:
                    print(f"❌ 图片文件太小，可能损坏: {文件大小} bytes")
            except Exception as e:
                print(f"❌ 图片验证失败: {e}")
                图片生成成功 = False
        else:
            print(f"❌ 图片路径无效或文件不存在: {image_path}")

        # 查找界面组件
        参考图组件 = getattr(self, f'{tab_name}_参考图组件', None)
        按钮组件 = None

        # 如果没有存储的引用，则查找组件
        if not 参考图组件:
            print(f"   🔍 查找参考图组件: {tab_name}")
            # 遍历所有tab，找到对应的组件
            for index, name in enumerate(self.tabs):
                if tab_name == name:
                    tab_widget = self.tab_widget.widget(index)
                    if tab_widget:
                        # 查找参考图组件（QLabel）
                        labels = tab_widget.findChildren(QLabel)
                        for label in labels:
                            # 通过大小和样式识别参考图组件
                            if (label.minimumWidth() == self.mood.缩放(150) and
                                label.minimumHeight() == self.mood.缩放(120)):
                                参考图组件 = label
                                # 存储引用供下次使用
                                setattr(self, f'{tab_name}_参考图组件', 参考图组件)
                                print(f"   ✅ 找到并存储参考图组件: {tab_name}")
                                break

                        # 查找生成参考图按钮
                        buttons = tab_widget.findChildren(QPushButton)
                        for btn in buttons:
                            if ('图片绘制中' in btn.text() or
                                '生成参考图' in btn.text() or
                                '绘图完成' in btn.text() or
                                '绘图失败' in btn.text()):
                                按钮组件 = btn
                                break
                    break
        else:
            print(f"   ✅ 使用存储的参考图组件: {tab_name}")

            # 查找按钮组件
            for index, name in enumerate(self.tabs):
                if tab_name == name:
                    tab_widget = self.tab_widget.widget(index)
                    if tab_widget:
                        buttons = tab_widget.findChildren(QPushButton)
                        for btn in buttons:
                            if ('图片绘制中' in btn.text() or
                                '生成参考图' in btn.text() or
                                '绘图完成' in btn.text() or
                                '绘图失败' in btn.text()):
                                按钮组件 = btn
                                break
                    break

        # 更新按钮状态
        if 按钮组件:
            if 图片生成成功 and 修改内容 == '绘图完成':
                按钮组件.setText('生成参考图')
                按钮组件.setStyleSheet('')  # 恢复默认样式
                print(f"✅ 按钮状态恢复为: 生成参考图")
            else:
                按钮组件.setText('生成失败')
                按钮组件.setStyleSheet(f'QPushButton {{background-color: {A0_config.失败色}; color: {A0_config.文字色};}}')
                print(f"❌ 按钮状态更新为: 生成失败")
                # 3秒后恢复按钮状态
                from PyQt5.QtCore import QTimer
                QTimer.singleShot(3000, lambda: self.恢复按钮状态(按钮组件))
        else:
            print(f"⚠️  未找到对应的按钮组件: {tab_name}")

        # 只有在图片生成成功时才更新界面和配置
        if 图片生成成功:
            try:
                # 更新参考图显示
                if 参考图组件:
                    image = QImage(image_path)
                    if not image.isNull():
                        scaled_image = image.scaled(self.mood.缩放(115), self.mood.缩放(115), Qt.KeepAspectRatio)
                        参考图组件.setPixmap(QPixmap.fromImage(scaled_image))
                        参考图组件.setAlignment(Qt.AlignCenter)
                        参考图组件.mouseDoubleClickEvent = lambda event, extra_arg=image_path: self.image_double_clicked(event, extra_arg)
                        print(f"✅ 参考图显示更新成功: {tab_name}")

                        # 刷新备选图片列表
                        self.刷新备选图片列表(tab_name)
                    else:
                        print(f"❌ 无法加载图片到QImage: {image_path}")
                else:
                    print(f"❌ 未找到参考图组件: {tab_name}")

                # 更新配置中的参考图路径（只有成功时才更新）
                配置已更新 = False
                for k, v in self.mood.项目配置['fenjing'].items():
                    if k == tab_name:
                        # 备份原有路径
                        原有路径 = v.get('img', '')
                        v['img'] = image_path
                        配置已更新 = True
                        print(f"✅ 项目配置已更新: {tab_name} -> {image_path}")
                        if 原有路径:
                            print(f"   📋 原有路径: {原有路径}")
                        break

                if 配置已更新:
                    self.mood.保存配置()
                    print(f"✅ 配置文件已保存")

            except Exception as e:
                print(f"❌ 更新参考图显示失败: {e}")
                import traceback
                traceback.print_exc()
        else:
            print(f"⚠️  图片生成失败，不更新配置和界面，保留原有设置")

    def 恢复按钮状态(self, 按钮组件):
        """恢复按钮状态"""
        try:
            if 按钮组件:
                按钮组件.setText('生成参考图')
                按钮组件.setStyleSheet('')  # 恢复默认样式
                print(f"✅ 按钮状态已恢复")
        except Exception as e:
            print(f"❌ 恢复按钮状态失败: {e}")

    def 切换固定种子状态(self, tab_name, enabled):
        """切换角色的固定种子启用状态"""
        try:
            if not hasattr(self.mood, '项目配置'):
                return

            # 确保角色专用种子配置存在
            if '角色专用种子配置' not in self.mood.项目配置:
                self.mood.项目配置['角色专用种子配置'] = {}

            # 确保角色配置存在
            if tab_name not in self.mood.项目配置['角色专用种子配置']:
                self.mood.项目配置['角色专用种子配置'][tab_name] = {}

            # 更新启用状态
            self.mood.项目配置['角色专用种子配置'][tab_name]['enabled'] = enabled

            # 保存配置
            self.mood.保存配置()

            状态文本 = "启用" if enabled else "禁用"
            print(f"✅ 已{状态文本}角色[{tab_name}]的固定种子功能")

        except Exception as e:
            print(f"❌ 切换固定种子状态失败: {e}")

    def 从图片路径提取种子(self, 图片路径):
        """从图片路径中提取种子值"""
        try:
            # 检查图片是否存在
            if not jduvudu312us_usjlq.path.exists(图片路径):
                print(f"❌ 图片不存在: {图片路径}")
                return None

            # 获取文件名
            文件名 = jduvudu312us_usjlq.path.basename(图片路径)
            print(f"📝 正在从文件名提取种子: {文件名}")

            # 从文件名中提取种子值
            import re

            # 尝试多种种子格式匹配
            种子模式列表 = [
                r'seed(\d+)',           # 标准格式: 角色名_seed1234567890.png
                r'cloud_(\d+)',         # 云端格式: 角色名_cloud_1751624867.png
                r'_(\d{10,})',          # 通用格式: 任何10位以上数字
            ]

            for 模式 in 种子模式列表:
                种子匹配 = re.search(模式, 文件名)
                if 种子匹配:
                    种子值 = int(种子匹配.group(1))
                    # 验证种子值是否合理（10位数字）
                    if 1000000000 <= 种子值 <= 9999999999:
                        print(f"✅ 成功提取种子值: {种子值} (格式: {模式})")
                        return 种子值

            print(f"❌ 未找到有效种子值")
            return None

        except Exception as e:
            print(f"❌ 提取种子值失败: {e}")
            return None

    def 设置角色固定种子(self, 角色名, 种子值):
        """设置角色的固定种子值"""
        try:
            if 种子值 is None or 种子值 == -1:
                print(f"⚠️ 无效的种子值")
                return

            print(f"🔧 正在设置角色[{角色名}]的固定种子: {种子值}")

            # 确保配置中有角色专用种子配置部分
            if '角色专用种子配置' not in self.mood.项目配置:
                self.mood.项目配置['角色专用种子配置'] = {}

            # 更新角色的种子配置
            self.mood.项目配置['角色专用种子配置'][角色名] = {
                'enabled': True,  # 启用固定种子
                'seed': 种子值,   # 设置种子值
                'last_updated': __import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            # 保存配置
            self.mood.保存配置()
            print(f"✅ 角色[{角色名}]的固定种子已设置为: {种子值}")

            # 更新界面上的固定种子开关状态
            self.切换固定种子状态(角色名, True)

            # 刷新备选图片列表
            self.刷新备选图片列表(角色名)

        except Exception as e:
            print(f"❌ 设置固定种子失败: {e}")

    def 优化角色描述(self, tab_name, 文本框):
        """优化角色描述"""
        try:
            # 导入优化模块
            from pyz.任务运行文件.角色描述优化 import show_character_optimization_dialog

            # 获取角色信息
            character_info = {}
            character_key = None

            # print(f"[A4_人物设定] 优化角色描述调试信息:")
            # print(f"  传入的tab_name: '{tab_name}'")
            # print(f"  配置中的角色列表: {list(self.mood.项目配置['fenjing'].keys())}")

            for k, v in self.mood.项目配置['fenjing'].items():
                if k == tab_name:
                    character_info = {
                        '性别': v.get('性别', ''),
                        '称呼': v.get('称呼', ''),
                        'guanjianci': v.get('guanjianci', ''),
                        'guanjianci_zh': v.get('guanjianci_zh', ''),
                        'tab_name': tab_name  # 添加角色名称
                    }
                    character_key = k  # 保存角色键名
                    # print(f"  找到匹配的角色配置: '{k}'")
                    # print(f"  角色配置内容: {v}")
                    break

            # print(f"  构建的character_info: {character_info}")
            # print(f"  character_key: '{character_key}'")

            # 获取当前描述，如果为空则基于角色信息生成
            current_description = 文本框.toPlainText().strip()
            if not current_description:
                # 基于角色配置信息生成基础描述
                current_description = self.生成基础角色描述(character_info)
                if not current_description:
                    QMessageBox.warning(self, '提示', '无法获取角色信息，请先配置角色性别等基本信息')
                    return

            # 显示优化对话框
            optimized_description = show_character_optimization_dialog(
                self, character_info, current_description, character_key
            )

            if optimized_description:
                # 更新文本框内容
                文本框.setText(optimized_description)

                # 保存到配置并自动翻译
                self.应用优化描述并翻译(tab_name, optimized_description)

                QMessageBox.information(self, '成功', '角色描述已优化、翻译并保存！')

        except ImportError as e:
            QMessageBox.critical(self, '错误', f'优化功能模块加载失败: {str(e)}')
        except Exception as e:
            print(f"❌ 优化角色描述失败: {e}")
            QMessageBox.critical(self, '错误', f'优化角色描述失败: {str(e)}')

    def 应用优化描述并翻译(self, tab_name, optimized_description):
        """
        应用优化描述并自动翻译更新关键词
        """
        try:
            print(f"🔄 应用优化描述并翻译: {tab_name}")
            print(f"   优化描述: {optimized_description[:50]}...")

            # 检查描述是否包含中文
            import re
            包含中文 = bool(re.search(r'[\u4e00-\u9fff]', optimized_description))

            # 更新配置
            for k, v in self.mood.项目配置['fenjing'].items():
                if k == tab_name:
                    if 包含中文:
                        # 优化描述包含中文，需要翻译
                        print(f"   🔤 检测到中文描述，开始翻译...")

                        # 1. 保存中文版本
                        v['guanjianci_zh'] = optimized_description
                        print(f"   ✅ 已保存中文版本")

                        # 2. 翻译为英文并保存
                        翻译结果 = self.翻译并更新英文关键词(tab_name, optimized_description)
                        if 翻译结果:
                            print(f"   ✅ 翻译成功，已更新英文关键词")
                        else:
                            # 翻译失败，不修改guanjianci字段，保持原有英文内容
                            print(f"   ⚠️  翻译失败，保持原有英文关键词不变")
                            # 不要将中文内容写入guanjianci字段
                            # v['guanjianci'] = optimized_description  # 注释掉这行
                    else:
                        # 优化描述是英文，直接保存
                        print(f"   ✅ 检测到英文描述，直接保存")
                        v['guanjianci'] = optimized_description
                        # 如果没有中文版本，也保存一份
                        if not v.get('guanjianci_zh'):
                            v['guanjianci_zh'] = optimized_description

                    break

            # 保存配置
            self.mood.保存配置()
            print(f"   💾 配置已保存")

        except Exception as e:
            print(f"❌ 应用优化描述并翻译失败: {e}")
            # 🛡️ 修复：即使翻译失败，也不能将中文内容写入guanjianci字段
            import re
            包含中文 = bool(re.search(r'[\u4e00-\u9fff]', optimized_description))

            for k, v in self.mood.项目配置['fenjing'].items():
                if k == tab_name:
                    if 包含中文:
                        # 中文内容只保存到中文字段，不修改英文字段
                        v['guanjianci_zh'] = optimized_description
                        print(f"   ⚠️  异常处理：中文内容已保存到guanjianci_zh，保持guanjianci字段不变")
                    else:
                        # 英文内容可以保存到英文字段
                        v['guanjianci'] = optimized_description
                        v['guanjianci_zh'] = optimized_description
                        print(f"   ✅ 异常处理：英文内容已保存到两个字段")
                    break
            self.mood.保存配置()

    def 生成基础角色描述(self, character_info):
        """
        基于角色配置信息生成基础描述
        """
        try:
            print(f"🔄 生成基础角色描述...")

            # 获取角色基本信息
            性别 = character_info.get('性别', '').strip()
            称呼 = character_info.get('称呼', '').strip()
            tab_name = character_info.get('tab_name', '角色')

            # 解析称呼信息，提取主要名称
            主要名称 = tab_name
            if 称呼:
                称呼列表 = [name.strip() for name in 称呼.split('\n') if name.strip()]
                if 称呼列表:
                    主要名称 = 称呼列表[0]  # 使用第一个称呼作为主要名称

            print(f"   角色名称: {主要名称}")
            print(f"   角色性别: {性别}")

            # 根据性别和称呼生成基础描述（不包含角色名称）
            if 性别 == '女性':
                基础描述 = "女性角色"
                if '女孩' in 称呼 or '少女' in 称呼:
                    基础描述 = "年轻女性"
                elif '女人' in 称呼 or '女士' in 称呼:
                    基础描述 = "成熟女性"
            elif 性别 == '男性':
                基础描述 = "男性角色"
                if '男孩' in 称呼 or '少年' in 称呼:
                    基础描述 = "年轻男性"
                elif '男人' in 称呼 or '先生' in 称呼:
                    基础描述 = "成熟男性"
            else:
                # 性别未知或其他
                基础描述 = "角色"

            # 检查是否有现有的中文描述可以参考
            现有中文描述 = character_info.get('guanjianci_zh', '').strip()
            if 现有中文描述 and len(现有中文描述) > 10:
                print(f"   ✅ 使用现有中文描述作为基础")
                return 现有中文描述

            print(f"   ✅ 生成基础描述: {基础描述}")
            return 基础描述

        except Exception as e:
            print(f"❌ 生成基础角色描述失败: {e}")
            return ""

    def 获取角色种子值(self, 角色名, 是否参考图=False):
        """获取角色的种子值
        参数:
            角色名: 角色的名称
            是否参考图: 是否是参考图专用工作流
        返回:
            种子值: 如果是参考图则返回-1（由工作流自行处理），否则返回配置的种子值
        """
        try:
            # 参考图专用工作流使用自己的随机种子机制
            if 是否参考图:
                print(f"⚠️ 参考图专用工作流，返回-1由工作流处理种子")
                return -1

            # 获取角色的种子配置
            角色配置 = self.mood.项目配置.get('角色专用种子配置', {}).get(角色名, {})

            # 如果启用了固定种子且有种子值，返回配置的种子值
            if 角色配置.get('enabled', False) and 角色配置.get('seed') is not None:
                种子值 = 角色配置['seed']
                print(f"✅ 使用角色[{角色名}]的配置种子: {种子值}")
                return 种子值

            # 否则返回-1表示使用随机种子
            print(f"⚠️ 角色[{角色名}]未启用固定种子或无种子值，返回-1")
            return -1

        except Exception as e:
            print(f"❌ 获取角色种子值失败: {e}")
            return -1

    def 切换固定种子状态(self, 角色名, 启用状态):
        """切换角色的固定种子启用状态"""
        try:
            print(f"🔄 切换角色[{角色名}]的固定种子状态: {'启用' if 启用状态 else '禁用'}")

            # 确保配置中有角色专用种子配置部分
            if '角色专用种子配置' not in self.mood.项目配置:
                self.mood.项目配置['角色专用种子配置'] = {}

            # 获取当前种子配置
            当前配置 = self.mood.项目配置['角色专用种子配置'].get(角色名, {})

            if not 当前配置:
                print(f"⚠️ 角色[{角色名}]没有种子配置，无法切换状态")
                return

            # 更新启用状态
            当前配置['enabled'] = 启用状态
            self.mood.项目配置['角色专用种子配置'][角色名] = 当前配置

            # 保存配置
            self.mood.保存配置()
            print(f"✅ 角色[{角色名}]的固定种子状态已更新: {'启用' if 启用状态 else '禁用'}")

        except Exception as e:
            print(f"❌ 切换固定种子状态失败: {e}")

    def 添加到备选图片(self, tab_name, image_path):
        """将新生成的图片添加到备选图片列表"""
        try:
            # 获取当前备选图片列表
            备选图片列表 = []
            for k, v in self.mood.项目配置['fenjing'].items():
                if k == tab_name:
                    备选图片列表 = v.get('备选图片', [])
                    break

            # 避免重复添加
            if image_path not in 备选图片列表:
                备选图片列表.append(image_path)

                # 限制备选图片数量（最多保留10张）
                if len(备选图片列表) > 10:
                    # 删除最旧的图片文件
                    旧图片 = 备选图片列表.pop(0)
                    if jduvudu312us_usjlq.path.exists(旧图片) and 旧图片 != image_path:
                        try:
                            jduvudu312us_usjlq.remove(旧图片)
                            # print(f"🗑️  删除旧备选图片: {jduvudu312us_usjlq.path.basename(旧图片)}")
                        except:
                            pass

                # 更新配置
                for k, v in self.mood.项目配置['fenjing'].items():
                    if k == tab_name:
                        v['备选图片'] = 备选图片列表
                        break

                # 刷新备选图片显示
                self.加载备选图片(tab_name, 备选图片列表)
                self.加载主备选图片(tab_name, 备选图片列表)  # 同时刷新主区域
                # print(f"✅ 已添加到 {tab_name} 的备选图片列表")

        except Exception as e:
            print(f"❌ 添加备选图片失败: {e}")

    def image_double_clicked(self, event, image_path):
        try:
            print(image_path)
            jduvudu312us_usjlq.startfile(image_path)
        except:
            return None

    def on_button_clicked(self, checked, value, tab_name, i):
        button = self.sender()
        if value in A0_config.config['lora']:
            value = f'<lora:{value}:1>'
        text_edit = button.parent().parent().parent().parent().parent().findChild(Custom_TextEdit)
        if checked:
            text = text_edit.toPlainText()
            if '<lora' in value:
                text_edit.setPlainText(text + value + ',')
            else:
                text_edit.setPlainText(text + value + ',')
            text_edit.setReadOnly(False)
        else:
            text = text_edit.toPlainText()
            lines = text.split(',')
            new_lines = [line for line in lines if line.strip() != value]
            new_text = ','.join(new_lines)
            text_edit.setPlainText(new_text.strip())
        self.tab_values[tab_name] = text_edit.toPlainText()

    def baocun(self):
        self.保存分镜()
        QMessageBox.information(self, '提示', '保存成功', QMessageBox.Ok)

    def 保存分镜(self):
        result = {}
        for i in range(self.tab_widget.count()):
            tab_name = self.tab_widget.tabText(i)
            widget = self.tab_widget.widget(i)
            text_edit = widget.findChild(Custom_TextEdit)
            input_text = text_edit.toPlainText()
            result[tab_name] = input_text if input_text else ''

        # 🛡️ 修复：安全保存分镜，区分中英文内容
        import re
        for name in self.tabs:
            for k, v in self.mood.项目配置['fenjing'].items():
                if k == name:
                    当前内容 = result[name]
                    包含中文 = bool(re.search(r'[\u4e00-\u9fff]', 当前内容))

                    if 包含中文:
                        # 中文内容只保存到中文字段
                        v['guanjianci_zh'] = 当前内容
                        print(f"   🔤 角色 {name} 检测到中文内容，已保存到guanjianci_zh字段")

                        # 检查是否已有有效英文关键词
                        现有英文 = v.get('guanjianci', '')
                        if not 现有英文 or re.search(r'[\u4e00-\u9fff]', 现有英文):
                            # 没有有效英文关键词，启动翻译
                            print(f"   🔄 启动翻译更新英文字段...")
                            self.启动后台翻译(name, 当前内容)
                        else:
                            print(f"   ✅ 保持现有英文关键词不变")
                    else:
                        # 英文内容可以保存到英文字段
                        v['guanjianci'] = 当前内容
                        # 如果没有中文版本，也保存一份
                        if not v.get('guanjianci_zh'):
                            v['guanjianci_zh'] = 当前内容
                        print(f"   ✅ 角色 {name} 检测到英文内容，已保存到guanjianci字段")

        self.mood.保存配置()

    def baocunrole(self):
        if A0_config.NOVIP:
            QMessageBox.information(self, '提示', 'VIP会员专用功能, 请点击软件左侧"会员充值"升级', QMessageBox.Ok)
            return
        current_index = self.tab_widget.currentIndex()
        分镜名称 = self.tab_widget.tabText(current_index)
        dialog = CustomInputDialog(self, 分镜名称)
        if dialog.exec_() == QDialog.Accepted:
            text = dialog.input_field.text()
            self.保存分镜()
            A0_config.role[text] = self.mood.项目配置['fenjing'][分镜名称]
            A0_config.role[text]['性别'] = self.mood.项目配置['fenjing'][分镜名称].get('性别') if self.mood.项目配置['fenjing'][分镜名称].get('性别') else ''
            A0_config.role[text]['称呼'] = self.mood.项目配置['fenjing'][分镜名称].get('称呼') if self.mood.项目配置['fenjing'][分镜名称].get('称呼') else ''
            A0_config.role[text]['guanjianci'] = self.mood.项目配置['fenjing'][分镜名称].get('guanjianci') if self.mood.项目配置['fenjing'][分镜名称].get('guanjianci') else ''
            A0_config.role[text]['img'] = self.mood.项目配置['fenjing'][分镜名称].get('img') if self.mood.项目配置['fenjing'][分镜名称].get('img') else ''
            A0_config.role[text]['url'] = self.mood.项目配置['fenjing'][分镜名称].get('url') if self.mood.项目配置['fenjing'][分镜名称].get('url') else ''
            img = self.mood.项目配置['fenjing'][分镜名称].get('img') if self.mood.项目配置['fenjing'][分镜名称].get('img') else ''
            role_img = jduvudu312us_usjlq.path.join(A0_config.config文件夹, f'{text}.png')
            print(img, role_img)
            if img and jduvudu312us_usjlq.path.exists(img):
                shutil.copy(img, role_img)
                A0_config.role[text]['img'] = role_img
            A0_config.增加角色()
            QMessageBox.information(self, '提示', '添加成功, 后续可在增加角色中使用', QMessageBox.Ok)

    def 刷新备选图片列表(self, tab_name):
        """刷新指定角色的备选图片列表"""
        try:
            # 获取角色的参考图文件夹路径
            if hasattr(self.mood, '当前项目文件夹') and self.mood.当前项目文件夹:
                参考图文件夹 = jduvudu312us_usjlq.path.join(self.mood.当前项目文件夹, '角色参考图片', tab_name)
            else:
                参考图文件夹 = jduvudu312us_usjlq.path.join(self.mood.项目文件夹, '角色参考图片', tab_name)

            if not jduvudu312us_usjlq.path.exists(参考图文件夹):
                # print(f"⚠️ 参考图文件夹不存在: {参考图文件夹}")
                return

            # 获取文件夹中的所有png图片
            图片列表 = []
            for f in jduvudu312us_usjlq.listdir(参考图文件夹):
                if f.lower().endswith('.png'):
                    完整路径 = jduvudu312us_usjlq.path.join(参考图文件夹, f)
                    图片列表.append(完整路径)

            图片列表.sort(reverse=True)  # 按文件名倒序排列，最新的在前面

            # 更新项目配置中的备选图片列表
            for k, v in self.mood.项目配置['fenjing'].items():
                if k == tab_name:
                    v['备选图片'] = 图片列表
                    break

            # 使用新的布局系统刷新显示
            self.加载主备选图片(tab_name, 图片列表)
            # print(f"✅ 备选图片列表已更新: {tab_name} ({len(图片列表)}张)")

        except Exception as e:
            # print(f"❌ 刷新备选图片列表失败: {e}")
            pass

    def 显示右键菜单(self, event, 图片路径, 角色名):
        """显示右键菜单"""
        try:
            menu = QMenu()

            # 从图片路径提取种子值
            种子值 = self.从图片路径提取种子(图片路径)
            if 种子值 is not None:
                # 设置为当前角色的固定种子
                action = QAction(f"设为 {角色名} 的固定种子", menu)
                action.triggered.connect(lambda: self.设置角色固定种子(角色名, 种子值))
                menu.addAction(action)

                # 显示菜单
                menu.exec_(event.globalPos())
            else:
                # print(f"❌ 无法从图片路径提取种子值: {图片路径}")
                pass

        except Exception as e:
            # print(f"❌ 显示右键菜单失败: {e}")
            pass

    def 一键拼接参考图(self):
        """一键拼接所有可能的角色组合参考图"""
        try:
            print("🎨 开始一键拼接参考图功能...")

            # 获取所有角色名称
            角色列表 = []
            for tab_name in self.tabs:
                if tab_name != '全局':  # 排除全局tab
                    角色列表.append(tab_name)

            if len(角色列表) < 2:
                QMessageBox.information(self, '提示', '至少需要2个角色才能进行拼接！\n请先添加更多角色。', QMessageBox.Ok)
                return

            print(f"📋 找到角色: {角色列表}")

            # 检查是否有现有拼接图需要更新
            需要更新的组合 = []
            现有组合数量 = 0

            # 生成所有可能的角色组合
            组合列表 = self.生成角色组合(角色列表)

            if not 组合列表:
                QMessageBox.information(self, '提示', '无法生成角色组合！', QMessageBox.Ok)
                return

            # 导入浏览器绘图方案的图片拼接器
            try:
                from pyz.任务运行文件.浏览器绘图方案.utils import 图片拼接器
                图片拼接器实例 = 图片拼接器(self.mood)

                # 检查角色参考图片目录
                角色参考图片目录 = 图片拼接器实例.角色参考图片目录
                print(f"📁 角色参考图片目录: {角色参考图片目录}")

                # 检查每个角色的参考图是否存在
                缺失角色 = []
                for 角色名 in 角色列表:
                    角色目录 = jduvudu312us_usjlq.path.join(角色参考图片目录, 角色名)
                    if not jduvudu312us_usjlq.path.exists(角色目录):
                        缺失角色.append(f"{角色名} (目录不存在)")
                        print(f"❌ 角色目录不存在: {角色目录}")
                    else:
                        # 检查目录中是否有图片文件
                        图片文件 = [f for f in jduvudu312us_usjlq.listdir(角色目录) if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
                        if not 图片文件:
                            缺失角色.append(f"{角色名} (无图片文件)")
                            print(f"❌ 角色目录中无图片文件: {角色目录}")
                        else:
                            print(f"✅ 角色 {角色名} 有参考图: {图片文件}")

                if 缺失角色:
                    缺失信息 = '\n'.join([f"• {角色}" for 角色 in 缺失角色])
                    QMessageBox.warning(self, '角色参考图缺失',
                                      f'以下角色缺少参考图:\n\n{缺失信息}\n\n'
                                      f'请确保在以下目录中为每个角色添加参考图片:\n{角色参考图片目录}',
                                      QMessageBox.Ok)
                    return

                # 检查现有拼接图和参考图的时间
                for 组合 in 组合列表:
                    现有图片 = 图片拼接器实例.检查拼接图片是否存在(组合)
                    if 现有图片:
                        现有组合数量 += 1
                        # 检查是否需要更新
                        if self.检查拼接图是否需要更新(组合, 现有图片):
                            需要更新的组合.append(组合)

                # 如果有现有拼接图，询问用户处理方式
                强制重新生成 = False
                if 现有组合数量 > 0:
                    if 需要更新的组合:
                        # 有过时的拼接图，询问是否更新
                        过时组合信息 = '\n'.join([f"• {'、'.join(组合)}" for 组合 in 需要更新的组合])
                        回复 = QMessageBox.question(self, '检测到参考图更新',
                                                  f'检测到以下组合的参考图已更新:\n\n{过时组合信息}\n\n'
                                                  f'是否重新生成这些拼接图？\n\n'
                                                  f'点击"Yes"重新生成过时的拼接图\n'
                                                  f'点击"No"跳过所有现有拼接图\n'
                                                  f'点击"Cancel"强制重新生成所有拼接图',
                                                  QMessageBox.Yes | QMessageBox.No | QMessageBox.Cancel)

                        if 回复 == QMessageBox.Cancel:
                            强制重新生成 = True
                        elif 回复 == QMessageBox.No:
                            需要更新的组合 = []  # 不更新任何组合
                    else:
                        # 所有拼接图都是最新的，询问是否强制重新生成
                        回复 = QMessageBox.question(self, '拼接图已存在',
                                                  f'发现 {现有组合数量} 个现有拼接图，且都是最新的。\n\n'
                                                  f'是否强制重新生成所有拼接图？\n\n'
                                                  f'点击"Yes"强制重新生成所有拼接图\n'
                                                  f'点击"No"跳过现有拼接图，只生成缺失的',
                                                  QMessageBox.Yes | QMessageBox.No)

                        if 回复 == QMessageBox.Yes:
                            强制重新生成 = True

                成功计数 = 0
                失败计数 = 0
                失败详情 = []
                跳过计数 = 0

                # 为每个组合创建拼接图片
                for 组合 in 组合列表:
                    print(f"🎭 处理组合: {组合}")

                    # 检查是否已存在拼接图片
                    现有图片 = 图片拼接器实例.检查拼接图片是否存在(组合)

                    # 决定是否需要重新生成
                    需要生成 = False
                    if not 现有图片:
                        需要生成 = True  # 不存在，需要生成
                    elif 强制重新生成:
                        需要生成 = True  # 强制重新生成
                    elif 组合 in 需要更新的组合:
                        需要生成 = True  # 需要更新

                    if not 需要生成:
                        print(f"⏭️ 跳过组合 {组合}，拼接图片已存在且最新: {现有图片}")
                        跳过计数 += 1
                        成功计数 += 1  # 算作成功
                        continue

                    # 创建新的拼接图片
                    print(f"🎨 {'重新生成' if 现有图片 else '创建新的'}拼接图片: {组合}")
                    拼接结果 = 图片拼接器实例.拼接图片(组合, 强制重新生成=True)
                    if 拼接结果:
                        print(f"✅ 成功{'重新生成' if 现有图片 else '创建'}拼接图片: {拼接结果}")
                        成功计数 += 1
                    else:
                        print(f"❌ 拼接失败: {组合}")
                        失败计数 += 1
                        失败详情.append(f"{'、'.join(组合)}")

                # 显示结果
                总数 = len(组合列表)
                # 显示结果
                总数 = len(组合列表)
                消息 = f"拼接完成！\n\n总组合数: {总数}\n成功: {成功计数}\n失败: {失败计数}\n跳过: {跳过计数}"

                if 失败计数 > 0:
                    消息 += f"\n\n失败的组合:\n"
                    for 失败组合 in 失败详情[:5]:  # 最多显示5个失败组合
                        消息 += f"• {失败组合}\n"
                    if len(失败详情) > 5:
                        消息 += f"... 还有 {len(失败详情) - 5} 个失败组合"

                    消息 += f"\n失败原因可能是:\n- 图片文件损坏或格式不支持\n- 权限问题\n- 内存不足"

                if 跳过计数 > 0:
                    消息 += f"\n\n💡 跳过了 {跳过计数} 个现有的最新拼接图"

                QMessageBox.information(self, '拼接结果', 消息, QMessageBox.Ok)

            except ImportError as e:
                print(f"❌ 导入图片拼接器失败: {e}")
                QMessageBox.warning(self, '错误', '无法导入浏览器绘图方案模块！\n请确保相关文件存在。', QMessageBox.Ok)

        except Exception as e:
            print(f"❌ 一键拼接参考图失败: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, '错误', f'拼接过程中发生错误:\n{str(e)}', QMessageBox.Ok)

    def 检查拼接图是否需要更新(self, 角色列表, 拼接图路径):
        """检查拼接图是否需要更新（比较参考图和拼接图的修改时间）"""
        try:
            import os

            # 获取拼接图的修改时间
            if not os.path.exists(拼接图路径):
                return True  # 拼接图不存在，需要生成

            拼接图时间 = os.path.getmtime(拼接图路径)

            # 检查所有参考图的修改时间
            for 角色名 in 角色列表:
                # 获取角色参考图路径
                try:
                    from pyz.任务运行文件.浏览器绘图方案.utils import 图片拼接器
                    图片拼接器实例 = 图片拼接器(self.mood)
                    参考图路径 = 图片拼接器实例.获取角色参考图(角色名)

                    if 参考图路径 and os.path.exists(参考图路径):
                        参考图时间 = os.path.getmtime(参考图路径)
                        if 参考图时间 > 拼接图时间:
                            print(f"🔍 检测到角色 {角色名} 的参考图已更新")
                            return True  # 参考图更新了，需要重新生成拼接图
                except Exception as e:
                    print(f"⚠️ 检查角色 {角色名} 参考图时间失败: {e}")
                    continue

            return False  # 所有参考图都没有更新

        except Exception as e:
            print(f"❌ 检查拼接图更新状态失败: {e}")
            return True  # 出错时默认需要更新

    def 生成角色组合(self, 角色列表):
        """生成所有可能的角色组合（2-4个角色）"""
        try:
            from itertools import combinations

            组合列表 = []

            # 2个角色的组合
            for 组合 in combinations(角色列表, 2):
                组合列表.append(list(组合))

            # 3个角色的组合
            if len(角色列表) >= 3:
                for 组合 in combinations(角色列表, 3):
                    组合列表.append(list(组合))

            # 4个角色的组合
            if len(角色列表) >= 4:
                for 组合 in combinations(角色列表, 4):
                    组合列表.append(list(组合))

            print(f"📊 生成了 {len(组合列表)} 种角色组合:")
            for i, 组合 in enumerate(组合列表):
                print(f"  {i+1}. {组合}")

            return 组合列表

        except Exception as e:
            print(f"❌ 生成角色组合失败: {e}")
            return []

class CustomInputDialog(QDialog):

    def __init__(self, parent=None, 分镜名称=None):
        super().__init__(parent)
        self.setWindowTitle('请输入预设名称')
        self.input_field = QLineEdit(self)
        self.input_field.setPlaceholderText('请输入预设名称')
        if 分镜名称 != '请输入工作流名称':
            self.input_field.setText(分镜名称)
        else:
            self.input_field.setPlaceholderText(分镜名称)
        self.buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel, self)
        self.buttons.button(QDialogButtonBox.Cancel).setText('取消')
        self.buttons.button(QDialogButtonBox.Ok).setText('保存')
        self.buttons.accepted.connect(self.baocun)
        self.buttons.rejected.connect(self.reject)
        layout = QVBoxLayout(self)
        layout.addWidget(self.input_field)
        layout.addWidget(self.buttons)

    def baocun(self):
        text = self.input_field.text()
        if not text:
            QMessageBox.information(self, '提示', '名称不能为空')
            return
        if text in A0_config.role.keys():
            reply = QMessageBox.question(self, '确认', '角色名存在,讲覆盖原角色, \n是否继续', QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
            if reply == QMessageBox.Yes:
                self.accept()
        else:
            self.accept()

class zengjia(QDialog):

    def __init__(self, mood):
        # 简化初始化，避免编码问题
        super().__init__(mood)
        self.mood = mood
        self.参考图路径 = ''

        # 设置基本属性
        try:
            self.setStyleSheet(mood.设置布局())
            self.setWindowFlags(self.windowFlags() & ~Qt.WindowContextHelpButtonHint)
            self.setWindowTitle('增加分镜')
            icon_path = A0_config.ico
            if jduvudu312us_usjlq.path.isfile(icon_path):
                icon = QIcon(icon_path)
                self.setWindowIcon(icon)
        except Exception as e:
            print(f"zengjia窗口设置错误: {e}")
            # 使用最基本的设置
            self.setWindowTitle('添加角色')
        layout = QVBoxLayout()
        性别行 = QHBoxLayout()
        self.text_edit = Custom_TextEdit(self)
        try:
            self.text_edit.setPlaceholderText('*请输入标签，例如角色名、场景等')
        except UnicodeEncodeError:
            self.text_edit.setPlaceholderText('*请输入标签，例如角色名、场景等')
        self.text_edit.setFixedHeight(mood.缩放(30))
        性别行.addWidget(self.text_edit)
        self.时代物种 = QComboBox()
        self.时代物种.setFixedHeight(mood.缩放(30))
        try:
            时代物种_options = ['本地角色库', '系统预设', '古代', '现代', '未来', '机器人', '动物', '神兽']
            for option in 时代物种_options:
                self.时代物种.addItem(option)
        except UnicodeEncodeError as e:
            print(f"添加时代物种选项时编码错误: {e}")
            # 使用英文选项作为备选
            fallback_options = ['Local', 'System', 'Ancient', 'Modern', 'Future', 'Robot', 'Animal', 'Mythical']
            for option in fallback_options:
                self.时代物种.addItem(option)
        self.时代物种.activated.connect(lambda: self.时代物种点击事件())
        try:
            self.人物性别_label = QLabel('人物预设')
        except UnicodeEncodeError:
            self.人物性别_label = QLabel('Character Preset')
        self.人物性别 = QComboBox()
        self.人物性别_label.setFixedHeight(mood.缩放(30))
        self.人物性别.setFixedHeight(mood.缩放(30))
        self.人物性别.setMinimumWidth(mood.缩放(150))
        try:
            人物性别_options = A0_config.role.keys()
            for option in 人物性别_options:
                # 确保选项是字符串并处理编码
                option_str = str(option) if option else ''
                if option_str:
                    self.人物性别.addItem(option_str)
        except UnicodeEncodeError as e:
            print(f"添加人物性别选项时编码错误: {e}")
            # 添加一个默认选项
            self.人物性别.addItem('默认角色')
        except Exception as e:
            print(f"添加人物性别选项时出错: {e}")
            self.人物性别.addItem('默认角色')
        self.人物性别.activated.connect(lambda: self.人物性别点击事件())
        性别行.addWidget(self.时代物种)
        性别行.addWidget(self.人物性别_label)
        性别行.addWidget(self.人物性别)
        layout.addLayout(性别行)
        self.text_edit1 = Custom_TextEdit(self)
        try:
            self.text_edit1.setPlaceholderText('*请输入别名，角色的多个称呼，一行一个')
        except UnicodeEncodeError:
            self.text_edit1.setPlaceholderText('*请输入别名，角色的多个称呼，一行一个')
        self.text_edit1.setFixedHeight(5 * self.text_edit1.fontMetrics().lineSpacing())
        layout.addWidget(self.text_edit1)
        self.角色形象 = Custom_TextEdit(self)
        try:
            self.角色形象.setPlaceholderText('*请输入角色的外貌形象')
        except UnicodeEncodeError:
            self.角色形象.setPlaceholderText('*请输入角色的外貌形象')
        self.角色形象.setFixedHeight(5 * self.角色形象.fontMetrics().lineSpacing())
        layout.addWidget(self.角色形象)
        垂直布局 = QVBoxLayout()
        try:
            button = QPushButton('添加参考图', self)
            button.setToolTip('主要用于固定场景风格、统一人物形象\n下载专用SD整个包才可用, \n或者网盘下载参考图模型到controlnet文件夹\nWebUI参考图会影响整个画面\nComfyUI只会提取人物特征')
        except UnicodeEncodeError:
            button = QPushButton('添加参考图', self)
            button.setToolTip('添加角色参考图片')
        button.setFixedSize(self.mood.缩放(150), self.mood.缩放(30))
        try:
            button1 = QPushButton('删除参考图', self)
        except UnicodeEncodeError:
            button1 = QPushButton('删除参考图', self)
        button1.setFixedSize(self.mood.缩放(150), self.mood.缩放(30))
        button.clicked.connect(lambda: self.tihuantupian())
        button1.clicked.connect(lambda: self.shanchu())
        self.参考图 = QLabel()
        self.参考图.setFixedWidth(self.mood.缩放(150))
        self.参考图.setFixedHeight(self.mood.缩放(150))
        self.参考图.setStyleSheet(f'border-radius: {self.mood.缩放(10)}px; border: 1px solid {A0_config.边框色};')
        垂直布局.addWidget(self.参考图)
        垂直布局.addWidget(button)
        垂直布局.addWidget(button1)
        水平布局 = QHBoxLayout()
        水平布局.addLayout(layout)
        水平布局.addLayout(垂直布局)
        垂直布局1 = QVBoxLayout()
        垂直布局1.addLayout(水平布局)
        layout1 = QHBoxLayout()
        layout1.addSpacing(30)
        try:
            确定按钮 = QPushButton('确定', self)
        except UnicodeEncodeError:
            确定按钮 = QPushButton('OK', self)
        确定按钮.setFixedWidth(mood.缩放(150))
        确定按钮.setFixedHeight(mood.缩放(30))
        layout1.addWidget(确定按钮)

        try:
            self.一键智能设置 = QPushButton('一键智能提取', self)
        except UnicodeEncodeError:
            self.一键智能设置 = QPushButton('Smart Extract', self)
        self.一键智能设置.setFixedWidth(mood.缩放(150))
        self.一键智能设置.setFixedHeight(mood.缩放(30))
        self.一键智能设置.setStyleSheet(f'QPushButton {{background-color: {A0_config.选中色}}}')
        layout1.addWidget(self.一键智能设置)

        # 添加一键提取提示词按钮
        try:
            self.一键提取提示词 = QPushButton('一键提取提示词', self)
        except UnicodeEncodeError:
            self.一键提取提示词 = QPushButton('Extract Prompts', self)
        self.一键提取提示词.setFixedWidth(mood.缩放(150))
        self.一键提取提示词.setFixedHeight(mood.缩放(30))
        self.一键提取提示词.setStyleSheet(f'QPushButton {{background-color: {A0_config.警告色}}}')
        layout1.addWidget(self.一键提取提示词)
        # 绑定事件
        确定按钮.clicked.connect(self.queren)
        self.一键智能设置.clicked.connect(self.yijiantiqu)
        self.一键提取提示词.clicked.connect(self._启动一键提取提示词)
        垂直布局1.addLayout(layout1)
        self.setLayout(垂直布局1)
        self.人物性别点击事件()
        self.showNormal()
        self.is_maximized = True

    def _启动一键提取提示词(self):
        """启动一键提取提示词功能"""
        try:
            from pyz.任务运行文件.一键提取提示词 import 一键提取提示词处理器

            # 创建处理器并执行
            processor = 一键提取提示词处理器(self.mood, self)
            processor.执行提取()

        except ImportError as e:
            print(f"❌ 导入一键提取提示词模块失败: {e}")
            self._显示置顶错误提示('功能模块加载失败，请检查安装')
        except Exception as e:
            print(f"❌ 启动一键提取提示词失败: {e}")
            self._显示置顶错误提示(f'启动失败：{str(e)}')

    def _显示置顶错误提示(self, message):
        """显示置顶的错误提示窗口"""
        try:
            from PyQt5.QtWidgets import QMessageBox
            from PyQt5.QtCore import Qt

            # 创建消息框
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle('错误')
            msg_box.setText(message)
            msg_box.setIcon(QMessageBox.Critical)
            msg_box.setStandardButtons(QMessageBox.Ok)

            # 设置窗口置顶
            msg_box.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.Dialog)

            # 显示消息框
            msg_box.exec_()

        except Exception as e:
            print(f"显示置顶错误失败: {e}")
            # 如果PyQt5方式失败，回退到tkinter方式
            try:
                import tkinter as tk
                from tkinter import messagebox

                # 创建一个隐藏的根窗口
                root = tk.Tk()
                root.withdraw()  # 隐藏主窗口
                root.attributes('-topmost', True)  # 设置置顶

                # 显示消息框
                messagebox.showerror('错误', message, parent=root)

                # 销毁根窗口
                root.destroy()

            except Exception as e2:
                print(f"备用错误提示方式也失败: {e2}")
                # 最后的备用方案
                from tkinter import messagebox
                messagebox.showerror('错误', message)

    def yijiantiqu(self):
        # 检查是否为离线模式，离线模式跳过VIP检查
        离线模式 = (A0_config.config.get('激活码', '').strip() == '' or
                  A0_config.API == 'http://wsai.b50.cc' or
                  '离线' in A0_config.config.get('激活码', ''))

        if A0_config.NOVIP and not 离线模式:
            messagebox.showinfo('提示', 'VIP专属功能, 请先升级为VIP')
            return

        # 获取原文
        原文 = []
        for data in self.mood.项目配置['data']:
            原文.append(data['txt'])
        原文 = '。\n'.join(原文)

        if not 原文.strip():
            messagebox.showinfo('提示', '没有找到可分析的文本内容')
            return

        # 确认是否进行智能提取
        reply = QMessageBox.question(self, '确认', '将使用智谱AI分析文本中的角色信息，是否继续？', QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        if reply != QMessageBox.Yes:
            return

        # 开始智能提取
        try:
            from pyz.任务运行文件.A8_角色提取器 import 角色提取器

            # 显示进度提示
            self.一键智能设置.setText('分析中...')
            self.一键智能设置.setEnabled(False)

            # 强制刷新UI
            from PyQt5.QtWidgets import QApplication
            QApplication.processEvents()

            # 创建角色提取器并分析
            extractor = 角色提取器()
            characters = extractor.提取文本角色(原文, use_zhipu=True)

            if characters:
                # 生成角色信息文本
                角色信息文本 = self._生成角色信息文本(characters)

                # 更新UI界面显示角色信息（仅用于查看）
                self.text_edit1.setText(角色信息文本)

                # 清空角色名称字段，防止误操作
                self.text_edit.setText("")

                # 自动将角色保存到项目配置中
                self._保存角色到项目配置(characters, 原文)

                # 创建置顶的成功提示窗口
                self._显示置顶成功提示(f'智能提取完成！\n成功识别 {len(characters)} 个角色\n角色已自动添加到分镜配置中\n\n注意：角色已自动保存，请直接关闭此窗口')
                # print(f"[一键智能提取] 成功提取 {len(characters)} 个角色并保存到项目配置")

                # 智能提取完成后自动关闭对话框
                self.accept()

            else:
                self._显示置顶警告提示('未能从文本中提取到角色信息，请检查文本内容或手动添加')
                print(f"[一键智能提取] 未能提取到角色信息")

        except Exception as e:
            print(f"[一键智能提取] 错误: {e}")
            self._显示置顶错误提示(f'智能提取失败：{str(e)}')

        finally:
            # 恢复按钮状态
            self.一键智能设置.setText('一键智能提取')
            self.一键智能设置.setEnabled(True)

    def _显示置顶成功提示(self, message):
        """显示置顶的成功提示窗口"""
        try:
            from PyQt5.QtWidgets import QMessageBox
            from PyQt5.QtCore import Qt

            # 创建消息框
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle('成功')
            msg_box.setText(message)
            msg_box.setIcon(QMessageBox.Information)
            msg_box.setStandardButtons(QMessageBox.Ok)

            # 设置窗口置顶
            msg_box.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.Dialog)

            # 显示消息框
            msg_box.exec_()

        except Exception as e:
            print(f"显示置顶提示失败: {e}")
            # 如果PyQt5方式失败，回退到tkinter方式
            try:
                import tkinter as tk
                from tkinter import messagebox

                # 创建一个隐藏的根窗口
                root = tk.Tk()
                root.withdraw()  # 隐藏主窗口
                root.attributes('-topmost', True)  # 设置置顶

                # 显示消息框
                messagebox.showinfo('成功', message, parent=root)

                # 销毁根窗口
                root.destroy()

            except Exception as e2:
                print(f"备用提示方式也失败: {e2}")
                # 最后的备用方案
                from tkinter import messagebox
                messagebox.showinfo('成功', message)

    def _显示置顶警告提示(self, message):
        """显示置顶的警告提示窗口"""
        try:
            from PyQt5.QtWidgets import QMessageBox
            from PyQt5.QtCore import Qt

            # 创建消息框
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle('提示')
            msg_box.setText(message)
            msg_box.setIcon(QMessageBox.Warning)
            msg_box.setStandardButtons(QMessageBox.Ok)

            # 设置窗口置顶
            msg_box.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.Dialog)

            # 显示消息框
            msg_box.exec_()

        except Exception as e:
            print(f"显示置顶警告失败: {e}")
            from tkinter import messagebox
            messagebox.showwarning('提示', message)

    def _显示置顶错误提示(self, message):
        """显示置顶的错误提示窗口"""
        try:
            from PyQt5.QtWidgets import QMessageBox
            from PyQt5.QtCore import Qt

            # 创建消息框
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle('错误')
            msg_box.setText(message)
            msg_box.setIcon(QMessageBox.Critical)
            msg_box.setStandardButtons(QMessageBox.Ok)

            # 设置窗口置顶
            msg_box.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.Dialog)

            # 显示消息框
            msg_box.exec_()

        except Exception as e:
            print(f"显示置顶错误失败: {e}")
            from tkinter import messagebox
            messagebox.showerror('错误', message)

    def _生成角色信息文本(self, characters: dict) -> str:
        """
        根据提取的角色信息生成格式化文本

        Args:
            characters: 角色信息字典

        Returns:
            格式化的角色信息文本
        """
        角色信息行 = []

        for name, info in characters.items():
            # 角色名
            角色信息行.append(f"[角色名]{name}")

            # 别名
            aliases = info.get('aliases', [])
            if aliases:
                别名列表 = [name] + aliases  # 包含角色名本身
            else:
                别名列表 = [name]
            角色信息行.append(f"[别名]{', '.join(别名列表)}")

            # 角色形象
            appearance = info.get('appearance', '')
            personality = info.get('personality', '')
            age_range = info.get('age_range', '')
            gender = info.get('gender', '')

            # 组合形象描述
            形象描述 = []
            if gender and gender != 'unknown':
                性别描述 = '男性' if gender == 'male' else '女性' if gender == 'female' else ''
                if 性别描述:
                    形象描述.append(性别描述)

            if age_range and age_range != '未知':
                形象描述.append(age_range)

            if appearance:
                形象描述.append(appearance)

            if personality:
                形象描述.append(f"性格{personality}")

            if 形象描述:
                角色信息行.append(f"[角色形象]{', '.join(形象描述)}")
            else:
                角色信息行.append(f"[角色形象]{name}")

            # 添加空行分隔
            角色信息行.append("")

        return '\n'.join(角色信息行)

    def _保存角色到项目配置(self, characters: dict, 原文: str):
        """
        将提取的角色信息保存到项目配置中

        Args:
            characters: 角色信息字典
            原文: 完整的原文文本
        """
        try:
            # 确保角色专用种子配置字段存在
            if '角色专用种子配置' not in self.mood.项目配置:
                self.mood.项目配置['角色专用种子配置'] = {}
                print(f"[一键智能提取] 创建角色专用种子配置字段")

            for name, info in characters.items():
                # 跳过叙述者角色和系统设置项
                if name in ['我', '叙述者', '作者', '全局']:
                    continue

                # 生成别名列表
                aliases = info.get('aliases', [])
                if aliases:
                    别名列表 = [name] + aliases
                else:
                    别名列表 = [name]
                识别词 = '\n'.join(别名列表)

                # 生成角色形象描述
                appearance = info.get('appearance', '')
                personality = info.get('personality', '')
                age_range = info.get('age_range', '')
                gender = info.get('gender', '')

                形象描述_parts = []
                if gender and gender != 'unknown':
                    性别描述 = '男性' if gender == 'male' else '女性' if gender == 'female' else ''
                    if 性别描述:
                        形象描述_parts.append(性别描述)

                if age_range and age_range != '未知':
                    形象描述_parts.append(age_range)

                if appearance:
                    形象描述_parts.append(appearance)

                if personality:
                    形象描述_parts.append(f"性格{personality}")

                角色形象 = ', '.join(形象描述_parts) if 形象描述_parts else name

                # 确定性别
                性别 = '男性' if gender == 'male' else '女性' if gender == 'female' else ''

                # 保存到项目配置
                self.mood.项目配置['fenjing'][name] = {
                    '性别': 性别,
                    '称呼': 识别词,
                    'guanjianci': 角色形象,
                    'guanjianci_zh': 角色形象,
                    'zhongzi': '',
                    'img': '',
                    'url': '',
                    '备选图片': []
                }

                # 创建角色专用种子配置
                import random
                import datetime

                # 生成随机种子值（10位数字）
                随机种子 = random.randint(1000000000, 9999999999)
                当前时间 = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                # 如果角色种子配置不存在，创建一个
                if name not in self.mood.项目配置['角色专用种子配置']:
                    self.mood.项目配置['角色专用种子配置'][name] = {
                        'seed': 随机种子,
                        'last_updated': 当前时间,
                        'enabled': False
                    }
                    print(f"[一键智能提取] 已创建角色[{name}]的种子配置: seed={随机种子}")
                else:
                    # 如果已存在但seed为null，更新它
                    现有配置 = self.mood.项目配置['角色专用种子配置'][name]
                    if 现有配置.get('seed') is None:
                        现有配置['seed'] = 随机种子
                        现有配置['last_updated'] = 当前时间
                        print(f"[一键智能提取] 已更新角色[{name}]的种子配置: seed={随机种子}")

                print(f"[一键智能提取] 已保存角色: {name}")

            # 创建或更新default_seed_config字段
            if '角色专用种子配置' in self.mood.项目配置 and self.mood.项目配置['角色专用种子配置']:
                # 使用第一个有效的角色作为默认种子配置
                for 角色名, 配置 in self.mood.项目配置['角色专用种子配置'].items():
                    if 配置.get('seed') is not None:
                        self.mood.项目配置['default_seed_config'] = {
                            'seed': 配置['seed'],
                            'fenjing': 角色名
                        }
                        print(f"[一键智能提取] 已设置默认种子配置: {角色名} = {配置['seed']}")
                        break

            # 更新每行数据的分镜字段（根据文本内容匹配角色）
            import re

            # 使用AI驱动的智能角色匹配（支持上下文和代词识别）
            self.mood._AI智能角色匹配(characters, 原文)

            # 检查并补充缺失的角色配置
            self.mood._补充缺失角色配置()

            # 保存配置文件
            self.mood.保存配置()

            # 刷新UI - 重新创建标签页
            if hasattr(self.mood, 'xiugaifenjing') and hasattr(self.mood.xiugaifenjing, '任务下拉框点击事件'):
                self.mood.xiugaifenjing.任务下拉框点击事件()
                print(f"[一键智能提取] 人物设定UI已刷新")

            # 刷新主界面右侧分镜区域
            try:
                for row in range(self.mood.table_widget.rowCount()):
                    if row < len(self.mood.项目配置['data']):
                        current_fenjing = self.mood.项目配置['data'][row].get('fenjing', '')
                        self.mood.fenjing(current_fenjing, row)
                print(f"[一键智能提取] 主界面分镜区域已刷新")
            except Exception as e:
                print(f"[一键智能提取] 刷新主界面分镜区域时出错: {e}")

        except Exception as e:
            print(f"[一键智能提取] 保存角色到项目配置时出错: {e}")

    def queren(self):
        if not self.text_edit1.toPlainText():
            messagebox.showinfo('提示', '请填入文案中对应角色的称呼, 一行一个')
            return
        self.accept()

    def tihuantupian(self):
        try:
            options = QFileDialog.Options()
            file_dialog = QFileDialog()
            file_dialog.setNameFilter('PNG Files (*.png *.jpg *.jpeg)')
            file_dialog.setFileMode(QFileDialog.ExistingFile)
            selected_file, _ = file_dialog.getOpenFileName(self, '选择PNG图片', '', 'PNG Files (*.png *.jpg *.jpeg)', options=options)
            self.参考图路径 = selected_file
            if selected_file != '':
                image_path = selected_file
                image = QImage(image_path)
                scaled_image = image.scaled(self.mood.缩放(145), self.mood.缩放(145), Qt.KeepAspectRatio)
                self.参考图.setPixmap(QPixmap.fromImage(scaled_image))
                self.参考图.setAlignment(Qt.AlignCenter)
                self.参考图.mouseDoubleClickEvent = lambda event, extra_arg=image_path: self.image_double_clicked(event, extra_arg)
        except Exception as e:
            print(f'替换图片错误{e}')
            messagebox.showinfo('提示', f'替换图片错误{e}')

    def shanchu(self):
        self.参考图.setPixmap(QPixmap())
        self.参考图路径 = ''

    def image_double_clicked(self, event, image_path):
        try:
            print(image_path)
            jduvudu312us_usjlq.startfile(image_path)
        except:
            return None

    def 人物性别点击事件(self):
        if self.时代物种.currentText() == '本地角色库':
            if self.人物性别.currentText() and A0_config.role.get(self.人物性别.currentText()):
                self.角色形象.setText(A0_config.role.get(self.人物性别.currentText()).get('guanjianci'))
                self.text_edit1.setText(A0_config.role.get(self.人物性别.currentText()).get('称呼'))
                self.text_edit.setText(self.人物性别.currentText())
                img = A0_config.role.get(self.人物性别.currentText(), {}).get('img')
                self.参考图路径 = img
                if img and jduvudu312us_usjlq.path.exists(img):
                    image_path = img
                    image = QImage(image_path)
                    scaled_image = image.scaled(self.mood.缩放(145), self.mood.缩放(145), Qt.KeepAspectRatio)
                    self.参考图.setPixmap(QPixmap.fromImage(scaled_image))
                    self.参考图.setAlignment(Qt.AlignCenter)
                    self.参考图.mouseDoubleClickEvent = lambda event, extra_arg=image_path: self.image_double_clicked(event, extra_arg)
                else:
                    self.参考图.setPixmap(QPixmap())
        else:
            self.角色形象.setText(A26_角色形象.性别词.get(self.人物性别.currentText()))

    def 时代物种点击事件(self):
        self.人物性别.clear()
        if self.时代物种.currentText() == '本地角色库':
            人物性别_options = A0_config.role.keys()
            for option in 人物性别_options:
                self.人物性别.addItem(option)
        else:
            人物性别_options = A26_角色形象.性别词.keys()
            for option in 人物性别_options:
                if self.时代物种.currentText() == '系统预设' or self.时代物种.currentText() in option:
                    self.人物性别.addItem(option)
        self.人物性别点击事件()



class CustomImageLabel(QLabel):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.图片路径 = None
        self.tab_name = None
        self.parent_widget = None

    def mousePressEvent(self, event):
        if not self.parent_widget or not self.图片路径 or not self.tab_name:
            print(f"❌ 缺少必要属性: parent_widget={bool(self.parent_widget)}, 图片路径={bool(self.图片路径)}, tab_name={bool(self.tab_name)}")
            return

        if event.button() == Qt.RightButton:
            print(f"🖱️ 右键点击: {self.图片路径}")
            self.parent_widget.显示右键菜单(event, self.图片路径, self.tab_name)
        else:
            print(f"🖱️ 左键点击: {self.图片路径}")
            self.parent_widget.设置为主参考图(self.tab_name, self.图片路径)



