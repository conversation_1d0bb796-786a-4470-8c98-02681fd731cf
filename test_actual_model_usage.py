#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试实际生成图片时使用的模型
"""

import os
import sys

# 添加项目路径到sys.path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from pyz.任务运行文件 import A0_config

def test_配置读取():
    """测试配置读取"""
    print("=" * 60)
    print("测试配置读取")
    print("=" * 60)
    
    # 直接读取配置
    模型名称 = A0_config.config.get('pollinations_model', 'flux')
    使用Pollinations = A0_config.config.get('使用Pollinations', False)
    
    print(f"📋 配置中的模型: {模型名称}")
    print(f"☁️ 是否启用Pollinations: {使用Pollinations}")
    
    # 模拟main_window.py中的逻辑
    if 使用Pollinations:
        实际模型名称 = A0_config.config.get('pollinations_model', 'flux')
        print(f"🎯 实际会使用的模型: {实际模型名称}")
        
        if 实际模型名称 == 'kontext':
            print("✅ 会使用kontext模型")
        elif 实际模型名称 == 'flux':
            print("⚠️ 仍然会使用flux模型")
        else:
            print(f"🔍 会使用其他模型: {实际模型名称}")
    else:
        print("❌ Pollinations未启用")

def test_模拟生成过程():
    """模拟图片生成过程中的模型获取"""
    print("\n" + "=" * 60)
    print("模拟图片生成过程中的模型获取")
    print("=" * 60)
    
    # 模拟main_window.py中的逻辑
    if A0_config.config.get('使用Pollinations', False):
        print("✅ Pollinations已启用")
        
        # 获取Pollinations配置（完全按照main_window.py的逻辑）
        模型名称 = A0_config.config.get('pollinations_model', 'flux')
        图片宽度 = int(A0_config.config.get('图片长度', 800))
        图片高度 = int(A0_config.config.get('图片宽度', 600))
        图片数量 = int(A0_config.config.get('每张图片数量', 2))
        
        print(f"🎯 模型名称: {模型名称}")
        print(f"📐 图片尺寸: {图片宽度}x{图片高度}")
        print(f"🔢 图片数量: {图片数量}")
        
        # 检查是否会传递正确的模型名称
        print(f"\n🔍 检查模型名称:")
        if 模型名称 == 'kontext':
            print("✅ 正确：会传递kontext模型")
        elif 模型名称 == 'flux':
            print("❌ 错误：仍然会传递flux模型")
        else:
            print(f"🔍 其他：会传递{模型名称}模型")
            
        return 模型名称
    else:
        print("❌ Pollinations未启用")
        return None

def test_Pollinations工作流():
    """测试Pollinations工作流中的模型使用"""
    print("\n" + "=" * 60)
    print("测试Pollinations工作流中的模型使用")
    print("=" * 60)
    
    try:
        from pyz.工作流.A7_Pollinations工作流 import 生成Pollinations图片
        
        # 模拟调用参数
        测试提示词 = "A beautiful landscape"
        模型名称 = A0_config.config.get('pollinations_model', 'flux')
        
        print(f"📝 测试提示词: {测试提示词}")
        print(f"🎯 传入的模型名称: {模型名称}")
        
        # 不实际生成图片，只检查参数传递
        print(f"\n🔍 如果调用生成函数，会传递的参数:")
        print(f"   模型名称: {模型名称}")
        print(f"   提示词: {测试提示词}")
        
        if 模型名称 == 'kontext':
            print("✅ 会正确传递kontext模型")
        else:
            print(f"⚠️ 会传递{模型名称}模型")
            
    except Exception as e:
        print(f"❌ 导入Pollinations工作流失败: {e}")

def test_配置文件内容():
    """直接检查配置文件内容"""
    print("\n" + "=" * 60)
    print("直接检查配置文件内容")
    print("=" * 60)
    
    try:
        import json
        
        with open(A0_config.配置文件, 'r', encoding='utf-8') as f:
            配置内容 = json.load(f)
        
        文件中的模型 = 配置内容.get('pollinations_model', '未设置')
        文件中的启用状态 = 配置内容.get('使用Pollinations', False)
        
        print(f"📄 配置文件中的模型: {文件中的模型}")
        print(f"📄 配置文件中的启用状态: {文件中的启用状态}")
        
        # 检查内存中的配置是否与文件一致
        内存中的模型 = A0_config.config.get('pollinations_model', '未设置')
        内存中的启用状态 = A0_config.config.get('使用Pollinations', False)
        
        print(f"💾 内存中的模型: {内存中的模型}")
        print(f"💾 内存中的启用状态: {内存中的启用状态}")
        
        # 检查一致性
        模型一致 = (文件中的模型 == 内存中的模型)
        启用状态一致 = (文件中的启用状态 == 内存中的启用状态)
        
        print(f"\n🔍 一致性检查:")
        print(f"   模型配置一致: {'✅' if 模型一致 else '❌'}")
        print(f"   启用状态一致: {'✅' if 启用状态一致 else '❌'}")
        
        if not 模型一致:
            print(f"⚠️ 发现不一致：文件={文件中的模型}, 内存={内存中的模型}")
        
        return 模型一致 and 启用状态一致
        
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return False

def test_强制刷新配置():
    """强制刷新配置"""
    print("\n" + "=" * 60)
    print("强制刷新配置")
    print("=" * 60)
    
    try:
        # 重新加载配置文件
        import json
        
        print("🔄 重新加载配置文件...")
        with open(A0_config.配置文件, 'r', encoding='utf-8') as f:
            新配置 = json.load(f)
        
        # 更新内存中的配置
        A0_config.config.update(新配置)
        
        # 检查更新后的配置
        更新后的模型 = A0_config.config.get('pollinations_model', 'flux')
        print(f"✅ 更新后的模型: {更新后的模型}")
        
        return 更新后的模型
        
    except Exception as e:
        print(f"❌ 刷新配置失败: {e}")
        return None

if __name__ == "__main__":
    print("🚀 开始测试实际模型使用情况")
    
    # 测试配置读取
    test_配置读取()
    
    # 模拟生成过程
    实际模型 = test_模拟生成过程()
    
    # 测试Pollinations工作流
    test_Pollinations工作流()
    
    # 检查配置文件内容
    配置一致性 = test_配置文件内容()
    
    # 强制刷新配置
    刷新后模型 = test_强制刷新配置()
    
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    if 实际模型:
        print(f"🎯 实际会使用的模型: {实际模型}")
        if 实际模型 == 'kontext':
            print("✅ 模型设置正确，应该会使用kontext")
        else:
            print("⚠️ 模型设置可能有问题")
    
    print(f"📄 配置一致性: {'✅ 正常' if 配置一致性 else '❌ 异常'}")
    
    if 刷新后模型:
        print(f"🔄 刷新后模型: {刷新后模型}")
    
    print("\n💡 建议:")
    if 实际模型 != 'kontext':
        print("1. 检查UI中的模型选择是否正确保存")
        print("2. 重启软件试试")
        print("3. 手动检查config.json文件中的pollinations_model值")
    else:
        print("配置看起来正确，如果仍然使用flux，可能是:")
        print("1. 浏览器缓存问题")
        print("2. Pollinations API的默认行为")
        print("3. URL构建过程中的问题")
    
    print("=" * 60)
