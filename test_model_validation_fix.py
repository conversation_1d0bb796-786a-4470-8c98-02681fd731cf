#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的模型验证逻辑
"""

import os
import sys

# 添加项目路径到sys.path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from pyz.任务运行文件 import A0_config

def test_云端模型列表():
    """测试云端模型列表获取"""
    print("=" * 60)
    print("测试云端模型列表获取")
    print("=" * 60)
    
    try:
        from pyz.任务运行文件.云端模型管理器 import 云端模型管理器
        
        模型管理器 = 云端模型管理器()
        可用模型列表 = 模型管理器.获取云端模型列表('Pollinations')
        
        print(f"📋 获取到 {len(可用模型列表)} 个模型:")
        for i, 模型 in enumerate(可用模型列表, 1):
            if 模型 == 'kontext':
                print(f"   {i:2d}. {模型} ⭐ (目标模型)")
            elif 模型 == 'flux':
                print(f"   {i:2d}. {模型} (默认模型)")
            else:
                print(f"   {i:2d}. {模型}")
        
        # 检查kontext是否在列表中
        if 'kontext' in 可用模型列表:
            print("\n✅ kontext模型在云端支持列表中")
        else:
            print("\n⚠️ kontext模型不在云端支持列表中")
        
        return 可用模型列表
        
    except Exception as e:
        print(f"❌ 获取云端模型列表失败: {e}")
        return []

def test_模型验证逻辑():
    """测试新的模型验证逻辑"""
    print("\n" + "=" * 60)
    print("测试新的模型验证逻辑")
    print("=" * 60)
    
    # 模拟模型验证过程
    测试模型 = ['kontext', 'flux', 'unknown-model', 'flux-anime']
    
    for 模型名称 in 测试模型:
        print(f"\n🧪 测试模型: {模型名称}")
        
        try:
            from pyz.任务运行文件.云端模型管理器 import 云端模型管理器
            模型管理器 = 云端模型管理器()
            可用模型列表 = 模型管理器.获取云端模型列表('Pollinations')
            
            if 模型名称 in 可用模型列表:
                print(f"   ✅ 模型 {模型名称} 在云端支持列表中")
                验证结果 = "通过"
            else:
                print(f"   🔍 模型 {模型名称} 不在已知列表中，但仍尝试使用")
                验证结果 = "允许尝试"
                
        except Exception as e:
            print(f"   ❌ 验证失败: {e}")
            验证结果 = "直接使用"
        
        print(f"   📊 验证结果: {验证结果}")

def test_配置和实际使用():
    """测试配置和实际使用"""
    print("\n" + "=" * 60)
    print("测试配置和实际使用")
    print("=" * 60)
    
    # 获取当前配置
    当前模型 = A0_config.config.get('pollinations_model', 'flux')
    print(f"📋 当前配置的模型: {当前模型}")
    
    # 模拟生成过程中的模型获取
    if A0_config.config.get('使用Pollinations', False):
        模型名称 = A0_config.config.get('pollinations_model', 'flux')
        print(f"🎯 实际会使用的模型: {模型名称}")
        
        # 模拟验证过程
        print(f"\n🔍 模拟验证过程:")
        try:
            from pyz.任务运行文件.云端模型管理器 import 云端模型管理器
            模型管理器 = 云端模型管理器()
            可用模型列表 = 模型管理器.获取云端模型列表('Pollinations')
            
            if 模型名称 in 可用模型列表:
                print(f"   ✅ 模型 {模型名称} 验证通过")
                最终模型 = 模型名称
            else:
                print(f"   🔍 模型 {模型名称} 不在列表中，但允许尝试")
                最终模型 = 模型名称  # 新逻辑：不强制回退
                
        except Exception as e:
            print(f"   ⚠️ 验证异常: {e}")
            最终模型 = 模型名称
        
        print(f"🎯 最终会传递给API的模型: {最终模型}")
        
        if 最终模型 == 'kontext':
            print("✅ 成功！会使用kontext模型")
        elif 最终模型 == 'flux':
            print("⚠️ 仍然使用flux模型")
        else:
            print(f"🔍 使用其他模型: {最终模型}")
            
    else:
        print("❌ Pollinations未启用")

def test_API参数构建():
    """测试API参数构建"""
    print("\n" + "=" * 60)
    print("测试API参数构建")
    print("=" * 60)
    
    # 模拟API参数构建
    模型名称 = A0_config.config.get('pollinations_model', 'flux')
    
    # 构建参数
    params = {
        'model': 模型名称,
        'width': 1280,
        'height': 960,
        'seed': 123456,
        'enhance': 'true',
        'safe': 'true'
    }
    
    print(f"🌐 构建的API参数:")
    for key, value in params.items():
        if key == 'model':
            print(f"   - {key}: {value} ⭐⭐⭐ 关键模型参数")
        else:
            print(f"   - {key}: {value}")
    
    # 检查模型参数
    if params['model'] == 'kontext':
        print("\n✅ API参数中使用kontext模型")
    elif params['model'] == 'flux':
        print("\n⚠️ API参数中使用flux模型")
    else:
        print(f"\n🔍 API参数中使用{params['model']}模型")

def test_强制刷新模型列表():
    """测试强制刷新模型列表"""
    print("\n" + "=" * 60)
    print("测试强制刷新模型列表")
    print("=" * 60)
    
    try:
        from pyz.任务运行文件.云端模型管理器 import 云端模型管理器
        
        模型管理器 = 云端模型管理器()
        
        print("🔄 强制刷新模型列表...")
        新模型列表 = 模型管理器.获取云端模型列表('Pollinations', 强制刷新=True)
        
        print(f"📋 刷新后获取到 {len(新模型列表)} 个模型")
        
        if 'kontext' in 新模型列表:
            print("✅ kontext模型在刷新后的列表中")
        else:
            print("⚠️ kontext模型不在刷新后的列表中")
            
        return 新模型列表
        
    except Exception as e:
        print(f"❌ 强制刷新失败: {e}")
        return []

if __name__ == "__main__":
    print("🚀 开始测试修复后的模型验证逻辑")
    
    # 测试云端模型列表
    云端模型列表 = test_云端模型列表()
    
    # 测试模型验证逻辑
    test_模型验证逻辑()
    
    # 测试配置和实际使用
    test_配置和实际使用()
    
    # 测试API参数构建
    test_API参数构建()
    
    # 测试强制刷新
    刷新后模型列表 = test_强制刷新模型列表()
    
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    当前模型 = A0_config.config.get('pollinations_model', 'flux')
    print(f"🎯 当前配置模型: {当前模型}")
    
    if 当前模型 == 'kontext':
        if 'kontext' in 云端模型列表:
            print("✅ 完美！kontext模型在支持列表中，应该可以正常使用")
        else:
            print("🔍 kontext模型不在已知列表中，但新逻辑允许尝试使用")
        
        print("\n💡 下次生成图片时应该看到:")
        print("   🔍 模型 kontext 不在已知列表中，但仍尝试使用")
        print("   🎯 ✅ 正在使用KONTEXT模型 (非flux)")
        print("   🎯 ✅ URL中确认使用KONTEXT模型")
    else:
        print(f"⚠️ 当前模型是{当前模型}，不是kontext")
    
    print("=" * 60)
