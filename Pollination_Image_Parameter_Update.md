# Pollination工作流Image参数与角色参考固定词功能更新

## 📋 更新概述

为Pollination工作流增加了完整的角色参考图功能，包括：
1. **Image参数支持**：从项目的`upload_records.json`文件中自动获取角色参考图片URL
2. **角色参考固定词**：根据角色信息自动生成标准化的角色描述词
3. **提示词结构优化**：角色参考固定词置于最前，风格词置于最后

## 🎯 功能特性

### 1. 自动角色参考图获取
- **无角色**: 不添加image参数，不添加角色参考固定词
- **单个角色**: 从对应角色的single图片中获取第一张图片URL
- **多个角色**: 自动查找包含所有角色的拼接图片URL（不区分角色顺序）

### 2. 角色参考固定词生成
- **单个角色**: `Same character and costume as the reference image`
- **两个角色**: `Refer to the picture for introduction. The character on the left is 角色1, and the character on the right is 角色2.`
- **三个角色**: `Refer to the picture for introduction. The character on the left is 角色1, the character in the middle is 角色2, and the character on the right is 角色3.`
- **四个角色**: `Refer to the picture for introduction. The character in the upper left is 角色1, the character in the upper right is 角色2, the character in the lower left is 角色3, and the character in the lower right is 角色4.`
- **多个角色**: `Refer to the picture for introduction. The characters are 角色1, 角色2, 角色3, ...`

### 3. 提示词结构优化
- **角色参考固定词**：放在提示词最前面
- **原始提示词**：保持在中间位置
- **风格提示词**：移动到提示词最后面

### 4. 精确匹配逻辑
- 支持角色名用逗号分隔的多角色场景
- 自动跳过"全局"等系统保留字段
- 对于多角色，**只匹配名字完全一样的拼接图片**（不区分顺序）
- 使用正则表达式精确解析文件名中的角色信息
- 支持多种位置格式：`左边/中间/右边`（2-3个角色）和`左上角/右上角/左下角/右下角`（4个角色）

### 5. URL来源
从项目目录下的`角色参考图片/upload_records.json`文件获取已上传的图片URL：
```
项目路径/角色参考图片/upload_records.json
```

## 🔧 技术实现

### 新增函数

#### `生成角色参考固定词(角色名, 参考图URL)`
```python
def 生成角色参考固定词(角色名, 参考图URL):
    """
    根据角色信息生成参考图固定描述词

    参数:
        角色名: 角色名称，支持多角色用逗号分隔
        参考图URL: 参考图URL

    返回:
        str: 角色参考固定词，无参考图时返回空字符串
    """
```

#### `获取角色参考图URL(项目路径, 角色名)`
```python
def 获取角色参考图URL(项目路径, 角色名):
    """
    从upload_records.json获取角色参考图的URL

    参数:
        项目路径: 项目路径（可能是图片文件夹路径）
        角色名: 角色名称，支持多角色用逗号分隔

    返回:
        str: 参考图URL，无角色或找不到时返回空字符串
    """
```

### 修改的函数

#### `获取Pollinations工作流()`
- 新增`项目路径`和`角色名`参数
- 自动获取参考图URL并添加到工作流配置中

#### `生成Pollinations图片()`
- 新增`角色名`参数
- 在API请求中自动添加`image`参数

## 📊 测试结果

### ✅ 角色参考固定词生成测试
- **单个角色**: `Same character and costume as the reference image`
- **两个角色**: `Refer to the picture for introduction. The character on the left is 艾米丽, and the character on the right is 露西.`
- **三个角色**: `Refer to the picture for introduction. The character on the left is 艾米丽, the character in the middle is 露西, and the character on the right is 斑斑.`
- **四个角色**: `Refer to the picture for introduction. The characters are 艾米丽, 露西, 斑斑, 小明.`

### ✅ 提示词结构测试
- **单个角色完整提示词**: `Same character and costume as the reference image, a beautiful girl with long hair, anime style, high quality`
- **两个角色完整提示词**: `Refer to the picture for introduction. The character on the left is 艾米丽, and the character on the right is 露西., two girls standing together, anime style, high quality`
- **无角色完整提示词**: `a landscape scene, anime style, high quality`

### ✅ 单个角色测试
- **艾米丽**: 成功获取URL `https://raw.githubusercontent.com/dfdfou/myimage/main/72343aface114151b8a984d9380c3f5b.png`
- **露西**: 成功获取URL `https://raw.githubusercontent.com/dfdfou/myimage/main/15034b854b8b401aa70947acd0d88e77.png`
- **斑斑**: 成功获取URL `https://raw.githubusercontent.com/dfdfou/myimage/main/212f457fa66c4cef9a79062efb55ce29.png`

### ✅ 多角色精确匹配测试
- **艾米丽,露西**: 找到完全匹配的拼接图片 `左边艾米丽_右边露西.png`
- **艾米丽,露西,斑斑**: 找到完全匹配的拼接图片 `左边艾米丽_中间露西_右边斑斑.png`
- **露西,斑斑**: 找到完全匹配的拼接图片 `左边露西_右边斑斑.png`
- **艾米丽,斑斑**: 找到完全匹配的拼接图片 `左边艾米丽_右边斑斑.png`
- **露西,艾米丽**: 找到完全匹配的拼接图片 `左边艾米丽_右边露西.png`（顺序不同但正确匹配）

### ✅ 边界情况测试
- **无角色名**: 正确不添加image参数和角色参考固定词
- **全局角色**: 正确不添加image参数和角色参考固定词
- **不存在的角色**: 正确处理，不添加image参数和角色参考固定词
- **包含不存在角色的组合**: 正确处理，不添加image参数和角色参考固定词

## 🔄 调用链更新

### 主要调用位置已更新：

1. **main_window.py** (第6291-6322行)
   ```python
   # 获取角色名用于参考图
   角色名 = json_data.get('人物分镜', '')
   if isinstance(角色名, list):
       角色名 = ','.join(角色名)  # 多角色用逗号连接
   
   所有图片 = 生成Pollinations图片(
       # ... 其他参数 ...
       角色名=角色名
   )
   ```

2. **A19_sd文生图.py** (第313-334行)
   ```python
   生成结果 = 生成Pollinations图片(
       # ... 其他参数 ...
       角色名=角色名
   )
   ```

3. **云端参考图专用工作流.py** (第220-240行)
   ```python
   生成结果 = 生成Pollinations图片(
       # ... 其他参数 ...
       角色名=角色名
   )
   ```

## 📝 使用示例

### 单个角色示例
**原始提示词**: `a beautiful girl with long hair`
**最终提示词**: `Same character and costume as the reference image, a beautiful girl with long hair, anime style, high quality`

### 多个角色示例
**原始提示词**: `two girls standing together`
**最终提示词**: `Refer to the picture for introduction. The character on the left is 艾米丽, and the character on the right is 露西., two girls standing together, anime style, high quality`

### 工作流配置示例
```json
{
  "1": {
    "inputs": {
      "prompt": "Same character and costume as the reference image, a beautiful girl with purple hair, anime style",
      "model": "flux",
      "width": 1280,
      "height": 720,
      "num_images": 2,
      "negative_prompt": "low quality, blurry",
      "seed": 0,
      "enhance": true,
      "safe_mode": true,
      "private": true,
      "nologo": false,
      "image": "https://raw.githubusercontent.com/dfdfou/myimage/main/72343aface114151b8a984d9380c3f5b.png"
    },
    "class_type": "PollinationsImageGen",
    "_meta": {
      "title": "Pollinations图像生成"
    }
  }
}
```

### API请求示例
```
https://image.pollinations.ai/prompt/Same%20character%20and%20costume%20as%20the%20reference%20image%2C%20a%20beautiful%20girl%20with%20purple%20hair%2C%20anime%20style?model=flux&width=1280&height=720&seed=1234567890&enhance=true&safe=true&private=true&nologo=true&image=https%3A//raw.githubusercontent.com/dfdfou/myimage/main/72343aface114151b8a984d9380c3f5b.png
```

## 🎉 总结

此次更新成功为Pollination工作流添加了完整的角色参考图功能，能够：

1. **自动识别角色**: 根据角色名自动获取对应的参考图URL
2. **智能角色翻译**: 自动将中文角色名翻译为英文，提升AI理解质量
3. **智能生成固定词**: 根据角色数量和位置自动生成标准化英文描述词
4. **优化提示词结构**: 角色参考固定词置于最前，风格词置于最后
5. **精确匹配多角色**: 只匹配名字完全一样的拼接图片，确保角色组合的准确性
6. **智能解析文件名**: 使用正则表达式精确解析拼接图片文件名中的角色信息
7. **无缝集成**: 与现有工作流完美兼容，不影响原有功能
8. **错误处理**: 完善的异常处理，确保系统稳定性

### 🌐 角色翻译功能
- **自动翻译**: 使用translate库自动将中文角色名翻译为英文
- **翻译示例**:
  - `母亲` → `Mother`
  - `挑水老人` → `Elderly Water Picker`
  - `迷路的孩子` → `Lost child`
  - `艾米丽` → `Emily`
- **提升质量**: 英文角色名让AI更好地理解角色信息

### 🔍 核心改进
- **角色参考固定词**: 为有参考图的场景自动添加标准化的英文角色描述
- **提示词结构优化**: 确保角色信息在最前面，风格信息在最后面
- **位置关系描述**: 根据拼接图片的命名自动生成准确的位置描述
- **四个角色支持**: 支持左上角、右上角、左下角、右下角的四角位置描述

### 📋 匹配规则
- **单个角色**: 直接从对应角色的single图片中获取，使用"Same character and costume"描述
- **两个角色**: 使用"left/right"位置描述
- **三个角色**: 使用"left/middle/right"位置描述
- **四个角色**: 使用"upper left/upper right/lower left/lower right"位置描述
- **多个角色**: 只匹配包含完全相同角色集合的拼接图片（不区分顺序）
- **文件名解析**: 支持多种位置格式的智能解析

### 🎯 最终效果示例
**四个角色提示词**:
```
Refer to the picture for introduction. The character in the upper left is Mother, the character in the upper right is Father, the character in the lower left is Elderly Water Picker, and the character in the lower right is Lost child., A family scene in a rural village, Chinese animation style, high quality, detailed
```

现在用户在使用Pollination生成图片时，系统会：
1. 自动将中文角色名**翻译为英文**
2. 根据角色信息获取**完全匹配**的参考图
3. 在提示词最前面添加**标准化的英文角色描述词**
4. 将风格词移到提示词最后面
5. 大大提升生成图片的角色一致性和质量！
