#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
云端模型管理器

实时获取和更新云端AI模型列表，支持多个服务商
"""

import requests
import json
import time
import threading
from typing import Dict, List, Optional
from pyz.任务运行文件 import A0_config


class 云端模型管理器:
    """云端模型管理器，负责获取和管理各种云端AI模型"""

    def __init__(self):
        self.模型缓存 = {}
        self.缓存时间 = {}
        self.缓存有效期 = 3600  # 1小时缓存
        self.更新中 = False

    def 获取Pollinations模型列表(self, 强制刷新=False):
        """
        获取Pollinations平台的可用模型列表

        Args:
            强制刷新: 是否强制刷新缓存

        Returns:
            list: 可用模型列表
        """
        cache_key = 'pollinations'

        # 检查缓存
        if not 强制刷新 and self._检查缓存有效性(cache_key):
            print("[缓存] 使用Pollinations模型缓存")
            return self.模型缓存.get(cache_key, [])

        print("[更新] 正在获取Pollinations模型列表...")

        try:
            # Pollinations官方API端点
            api_endpoints = [
                "https://image.pollinations.ai/models",
                "https://pollinations.ai/api/models",
                "https://api.pollinations.ai/models"
            ]

            模型列表 = []

            for endpoint in api_endpoints:
                try:
                    response = requests.get(endpoint, timeout=10)
                    if response.status_code == 200:
                        data = response.json()
                        if isinstance(data, list):
                            模型列表 = data
                            break
                        elif isinstance(data, dict) and 'models' in data:
                            模型列表 = data['models']
                            break
                except Exception as e:
                    print(f"[警告] 端点 {endpoint} 请求失败: {e}")
                    continue

            # 如果API获取失败，使用已知的稳定模型列表
            if not 模型列表:
                print("[回退] 使用预设的稳定模型列表")
                模型列表 = self._获取预设Pollinations模型()

            # 验证和清理模型列表
            有效模型 = self._验证Pollinations模型(模型列表)

            # 更新缓存
            self.模型缓存[cache_key] = 有效模型
            self.缓存时间[cache_key] = time.time()

            print(f"[成功] 获取到 {len(有效模型)} 个Pollinations模型")
            return 有效模型

        except Exception as e:
            print(f"[错误] 获取Pollinations模型失败: {e}")
            # 返回预设模型作为备选
            return self._获取预设Pollinations模型()

    def _获取预设Pollinations模型(self):
        """获取预设的稳定Pollinations模型列表"""
        return [
            'flux',
            'flux-realism',
            'flux-anime',
            'flux-3d',
            'flux-pro',
            'kontext',  # 新增kontext模型
            'dall-e-3',
            'stable-diffusion-3',
            'midjourney',
            'turbo',
            'any-dark',
            'playground-v2.5',
            'dreamshaper-xl',
            'juggernaut-xl',
            'realistic-vision',
            'absolute-reality',
            'deliberate',
            'anything-v5',
            'counterfeit',
            'openjourney',
            'stable-diffusion-xl',
            'stable-diffusion-2.1',
            'waifu-diffusion',
            'protogen',
            'vintedois-diffusion'
        ]

    def _验证Pollinations模型(self, 模型列表):
        """验证Pollinations模型的可用性"""
        有效模型 = []

        for 模型 in 模型列表:
            if isinstance(模型, str):
                模型名 = 模型.strip()
            elif isinstance(模型, dict):
                模型名 = 模型.get('name', 模型.get('id', '')).strip()
            else:
                continue

            if 模型名 and len(模型名) > 0:
                有效模型.append(模型名)

        # 去重并排序
        有效模型 = sorted(list(set(有效模型)))

        # 确保flux在第一位（默认模型）
        if 'flux' in 有效模型:
            有效模型.remove('flux')
            有效模型.insert(0, 'flux')

        return 有效模型

    def 获取其他云端模型(self, 平台名称):
        """
        获取其他云端平台的模型列表

        Args:
            平台名称: 平台名称，如 'OpenAI', 'Anthropic' 等

        Returns:
            list: 模型列表
        """
        # 预留接口，可以扩展支持其他平台
        平台配置 = {
            'OpenAI': {
                'api_url': 'https://api.openai.com/v1/models',
                'models': ['dall-e-3', 'dall-e-2']
            },
            'Midjourney': {
                'models': ['midjourney', 'niji']
            }
        }

        return 平台配置.get(平台名称, {}).get('models', [])

    def _检查缓存有效性(self, cache_key):
        """检查缓存是否有效"""
        if cache_key not in self.缓存时间:
            return False

        缓存时间 = self.缓存时间[cache_key]
        当前时间 = time.time()

        return (当前时间 - 缓存时间) < self.缓存有效期

    def 清除缓存(self):
        """清除所有缓存"""
        self.模型缓存.clear()
        self.缓存时间.clear()
        print("[缓存] 已清除所有模型缓存")

    def 异步更新模型列表(self, 回调函数=None):
        """
        异步更新模型列表

        Args:
            回调函数: 更新完成后的回调函数
        """
        if self.更新中:
            print("[跳过] 模型列表更新已在进行中")
            return

        def 更新任务():
            try:
                self.更新中 = True
                print("[异步] 开始更新云端模型列表...")

                # 更新Pollinations模型
                pollinations_models = self.获取Pollinations模型列表(强制刷新=True)

                # 更新配置
                A0_config.config['pollinations_models'] = pollinations_models

                # 保存配置
                try:
                    A0_config.修改配置()
                    print("[成功] 云端模型列表更新完成")
                except Exception as e:
                    print(f"[警告] 保存配置失败: {e}")

                # 执行回调
                if 回调函数:
                    回调函数(pollinations_models)

            except Exception as e:
                print(f"[错误] 异步更新模型列表失败: {e}")
            finally:
                self.更新中 = False

        # 启动后台线程
        thread = threading.Thread(target=更新任务, daemon=True)
        thread.start()

    def 获取模型详情(self, 模型名称, 平台='Pollinations'):
        """
        获取特定模型的详细信息

        Args:
            模型名称: 模型名称
            平台: 平台名称

        Returns:
            dict: 模型详情
        """
        模型详情 = {
            'name': 模型名称,
            'platform': 平台,
            'description': '',
            'capabilities': [],
            'recommended_settings': {}
        }

        # Pollinations模型的预设信息
        if 平台 == 'Pollinations':
            pollinations_info = {
                'flux': {
                    'description': 'Flux模型，高质量通用图像生成',
                    'capabilities': ['高质量', '快速生成', '多风格'],
                    'recommended_settings': {'width': 1024, 'height': 1024}
                },
                'flux-realism': {
                    'description': 'Flux写实模型，专注于真实感图像',
                    'capabilities': ['写实风格', '人像优化', '细节丰富'],
                    'recommended_settings': {'width': 1024, 'height': 1024}
                },
                'flux-anime': {
                    'description': 'Flux动漫模型，二次元风格专用',
                    'capabilities': ['动漫风格', '二次元', '角色设计'],
                    'recommended_settings': {'width': 832, 'height': 1216}
                },
                'dall-e-3': {
                    'description': 'OpenAI DALL-E 3，高质量AI绘画',
                    'capabilities': ['高质量', '创意生成', '文字理解'],
                    'recommended_settings': {'width': 1024, 'height': 1024}
                }
            }

            if 模型名称 in pollinations_info:
                模型详情.update(pollinations_info[模型名称])

        return 模型详情


# 全局实例
云端模型管理器实例 = 云端模型管理器()


def 获取云端模型列表(平台='Pollinations', 强制刷新=False):
    """
    获取云端模型列表的便捷函数

    Args:
        平台: 平台名称
        强制刷新: 是否强制刷新

    Returns:
        list: 模型列表
    """
    if 平台 == 'Pollinations':
        return 云端模型管理器实例.获取Pollinations模型列表(强制刷新)
    else:
        return 云端模型管理器实例.获取其他云端模型(平台)


def 异步更新云端模型(回调函数=None):
    """异步更新云端模型列表"""
    云端模型管理器实例.异步更新模型列表(回调函数)


def 获取模型信息(模型名称, 平台='Pollinations'):
    """获取模型详细信息"""
    return 云端模型管理器实例.获取模型详情(模型名称, 平台)
