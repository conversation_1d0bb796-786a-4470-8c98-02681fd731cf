#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Pollination工作流的image参数功能
"""

import os
import sys
import json

# 添加项目路径到sys.path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from pyz.工作流.A7_Pollinations工作流 import 获取角色参考图URL, 获取Pollinations工作流

def test_获取角色参考图URL():
    """测试获取角色参考图URL功能"""
    print("=" * 60)
    print("测试获取角色参考图URL功能")
    print("=" * 60)
    
    # 测试项目路径
    项目路径 = r"C:\Users\<USER>\Desktop\纯净wen\wen整合 - 副本 - 副本\项目文件\原创项目\蜂蝶随香"
    
    # 测试用例
    测试用例 = [
        ("", "无角色名"),
        ("全局", "全局角色"),
        ("艾米丽", "单个角色"),
        ("露西", "单个角色"),
        ("斑斑", "单个角色"),
        ("艾米丽,露西", "两个角色"),
        ("艾米丽,露西,斑斑", "三个角色"),
        ("露西,斑斑", "两个角色"),
        ("艾米丽,斑斑", "两个角色"),
        ("露西,艾米丽", "两个角色（顺序不同）"),
        ("不存在的角色", "不存在的角色"),
        ("艾米丽,不存在的角色", "包含不存在角色的组合"),
    ]
    
    for 角色名, 描述 in 测试用例:
        print(f"\n🧪 测试: {描述} - '{角色名}'")
        try:
            url = 获取角色参考图URL(项目路径, 角色名)
            if url:
                print(f"✅ 成功获取URL: {url}")
            else:
                print(f"❌ 未获取到URL")
        except Exception as e:
            print(f"❌ 错误: {e}")

def test_工作流配置():
    """测试工作流配置中的image参数"""
    print("\n" + "=" * 60)
    print("测试工作流配置中的image参数")
    print("=" * 60)
    
    项目路径 = r"C:\Users\<USER>\Desktop\纯净wen\wen整合 - 副本 - 副本\项目文件\原创项目\蜂蝶随香"
    
    # 测试单个角色
    print("\n🧪 测试单个角色工作流配置")
    workflow = 获取Pollinations工作流(
        模型名称="flux",
        图片宽度=1280,
        图片高度=720,
        图片数量=2,
        项目路径=项目路径,
        角色名="艾米丽"
    )
    
    print("工作流配置:")
    print(json.dumps(workflow, indent=2, ensure_ascii=False))
    
    # 检查是否包含image参数
    if "image" in workflow["1"]["inputs"]:
        print(f"✅ 成功添加image参数: {workflow['1']['inputs']['image']}")
    else:
        print("❌ 未添加image参数")
    
    # 测试多个角色
    print("\n🧪 测试多个角色工作流配置")
    workflow = 获取Pollinations工作流(
        模型名称="flux",
        图片宽度=1280,
        图片高度=720,
        图片数量=2,
        项目路径=项目路径,
        角色名="艾米丽,露西"
    )
    
    # 检查是否包含image参数
    if "image" in workflow["1"]["inputs"]:
        print(f"✅ 成功添加image参数: {workflow['1']['inputs']['image']}")
    else:
        print("❌ 未添加image参数")
    
    # 测试无角色
    print("\n🧪 测试无角色工作流配置")
    workflow = 获取Pollinations工作流(
        模型名称="flux",
        图片宽度=1280,
        图片高度=720,
        图片数量=2,
        项目路径=项目路径,
        角色名=""
    )
    
    # 检查是否不包含image参数
    if "image" not in workflow["1"]["inputs"]:
        print("✅ 正确：无角色时不添加image参数")
    else:
        print(f"❌ 错误：无角色时仍添加了image参数: {workflow['1']['inputs']['image']}")

def test_API参数构建():
    """测试API参数构建中的image参数"""
    print("\n" + "=" * 60)
    print("测试API参数构建中的image参数")
    print("=" * 60)

    项目路径 = r"C:\Users\<USER>\Desktop\纯净wen\wen整合 - 副本 - 副本\项目文件\原创项目\蜂蝶随香\图片文件"

    # 模拟API参数构建过程
    from pyz.工作流.A7_Pollinations工作流 import 获取角色参考图URL
    from urllib.parse import quote

    测试案例 = [
        ("艾米丽", "单个角色"),
        ("艾米丽,露西", "两个角色"),
        ("", "无角色")
    ]

    for 角色名, 描述 in 测试案例:
        print(f"\n🧪 测试{描述}: '{角色名}'")

        # 获取参考图URL
        参考图URL = 获取角色参考图URL(项目路径, 角色名)

        # 构建API参数
        params = {
            'model': 'flux',
            'width': 1280,
            'height': 720,
            'seed': 12345,
            'enhance': 'true',
            'safe': 'true',
            'private': 'true',
            'nologo': 'true'
        }

        # 如果有参考图URL，添加image参数
        if 参考图URL:
            params['image'] = 参考图URL
            print(f"✅ 添加image参数: {参考图URL}")
        else:
            print("❌ 无image参数")

        # 构建完整URL
        base_url = "https://image.pollinations.ai/prompt/"
        提示词 = "a beautiful girl"
        encoded_prompt = quote(提示词)

        param_string = "&".join([f"{k}={quote(str(v)) if k == 'image' else v}" for k, v in params.items()])
        完整URL = f"{base_url}{encoded_prompt}?{param_string}"

        print(f"🌐 API URL长度: {len(完整URL)} 字符")
        if 'image=' in 完整URL:
            print("✅ URL包含image参数")
        else:
            print("❌ URL不包含image参数")

def test_upload_records_结构():
    """测试upload_records.json文件结构"""
    print("\n" + "=" * 60)
    print("测试upload_records.json文件结构")
    print("=" * 60)

    upload_records_path = r"C:\Users\<USER>\Desktop\纯净wen\wen整合 - 副本 - 副本\项目文件\原创项目\蜂蝶随香\角色参考图片\upload_records.json"

    try:
        with open(upload_records_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        print("📋 upload_records.json结构:")
        for 角色名, 角色数据 in data.items():
            print(f"\n角色: {角色名}")
            for 类型, 图片列表 in 角色数据.items():
                print(f"  {类型}: {len(图片列表)}张图片")
                for i, 图片信息 in enumerate(图片列表[:2]):  # 只显示前2张
                    print(f"    [{i+1}] {图片信息.get('original_name', 'N/A')}")
                    print(f"        URL: {图片信息.get('upload_url', 'N/A')}")
                if len(图片列表) > 2:
                    print(f"    ... 还有{len(图片列表)-2}张图片")

    except Exception as e:
        print(f"❌ 读取upload_records.json失败: {e}")

if __name__ == "__main__":
    print("🚀 开始测试Pollination工作流的image参数功能")

    # 测试获取角色参考图URL
    test_获取角色参考图URL()

    # 测试工作流配置
    test_工作流配置()

    # 测试API参数构建
    test_API参数构建()

    # 测试upload_records结构
    test_upload_records_结构()

    print("\n" + "=" * 60)
    print("✅ 测试完成")
    print("=" * 60)
