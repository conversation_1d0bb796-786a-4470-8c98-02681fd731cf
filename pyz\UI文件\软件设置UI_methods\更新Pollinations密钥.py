# 更新Pollinations密钥方法
# 处理Pollinations API密钥的保存和验证

from pyz.任务运行文件 import A0_config
from PyQt5.QtWidgets import QLineEdit

def 更新Pollinations密钥(self):
    """更新Pollinations API密钥"""
    try:
        # 获取输入的密钥
        密钥 = self.Pollinations密钥.text().strip()
        
        # 保存到配置
        A0_config.config['pollinations_api_key'] = 密钥
        
        # 立即保存配置到文件
        A0_config.修改配置()
        
        # 简单验证密钥格式（可选）
        if 密钥:
            if len(密钥) < 10:
                print("⚠️ API密钥可能过短，请检查是否正确")
            else:
                print(f"✅ Pollinations API密钥已保存 (长度: {len(密钥)} 字符)")
        else:
            print("🔍 Pollinations API密钥已清空")
            
    except Exception as e:
        print(f"❌ 保存Pollinations API密钥失败: {e}")

def 切换Pollinations密钥显示(self):
    """切换Pollinations密钥的显示/隐藏状态"""
    try:
        if self.Pollinations密钥.echoMode() == QLineEdit.Password:
            # 当前是隐藏状态，切换为显示
            self.Pollinations密钥.setEchoMode(QLineEdit.Normal)
            self.Pollinations密钥显示.setText('🙈')
            self.Pollinations密钥显示.setToolTip('隐藏API密钥')
        else:
            # 当前是显示状态，切换为隐藏
            self.Pollinations密钥.setEchoMode(QLineEdit.Password)
            self.Pollinations密钥显示.setText('👁')
            self.Pollinations密钥显示.setToolTip('显示API密钥')
            
    except Exception as e:
        print(f"❌ 切换密钥显示状态失败: {e}")
