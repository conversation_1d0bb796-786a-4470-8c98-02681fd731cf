# 绘图模型按钮方法
# 从软件设置UI.py拆分出来的方法

from PyQt5.QtWidgets import QLabel, QComboBox, QCheckBox, QPushButton, QLineEdit
from PyQt5 import QtCore
from pyz.任务运行文件 import A0_config

def 绘图模型按钮(self):
    """创建绘图模型相关控件"""

    self.更换SD模型_label = QLabel('绘图模型')
    self.更换SD模型_label.setToolTip('决定图片的风格和质量, \n每个模型的训练元素不一样, \n画不出想要的图片尝试更换模型')
    self.更换SD模型 = QComboBox()
    self.更换SD模型.setMaximumSize(QtCore.QSize(self.缩放(500), self.缩放(9999)))
    self.更换SD模型.setToolTip('决定图片的风格和质量, \n每个模型的训练元素不一样, \n画不出想要的图片尝试更换模型')
    更换SD模型_options = A0_config.config['更换SD模型']
    for option in 更换SD模型_options:
        self.更换SD模型.addItem(option)
    self.更换SD模型.setCurrentText(A0_config.config['当前选中模型'])
    self.更换SD模型.activated.connect(lambda: self.更换模型())

    # 多GPU加速选项
    self.启用多GPU加速 = QCheckBox('启用多GPU加速 (集成显卡+独立显卡)')
    self.启用多GPU加速.setToolTip('同时使用集成显卡和独立显卡加速图像生成，提升生成速度')
    self.启用多GPU加速.setChecked(A0_config.config.get('启用多GPU加速', False))
    self.启用多GPU加速.stateChanged.connect(lambda: self.多GPU点击事件())

    # Pollinations云端模型选项
    self.使用Pollinations = QCheckBox('使用Pollinations云端模型')
    self.使用Pollinations.setToolTip('启用后将使用Pollinations免费云端AI模型，无需本地GPU和模型文件')

    # 设置复选框状态
    config_value = A0_config.config.get('使用Pollinations', False)
    self.使用Pollinations.setChecked(config_value)

    # 绑定点击事件
    self.使用Pollinations.stateChanged.connect(lambda: self.Pollinations点击事件())

    self.Pollinations模型_label = QLabel('云端模型')
    self.Pollinations模型 = QComboBox()
    self.Pollinations模型.setToolTip('选择Pollinations云端AI模型，支持多种风格和质量\n模型列表会自动更新，确保使用最新可用模型')

    # 使用云端模型管理器获取最新模型列表
    self._初始化Pollinations模型列表()

    self.Pollinations模型.activated.connect(lambda: self.更换Pollinations模型())

    self.Pollinations管理 = QPushButton('模型管理')
    self.Pollinations管理.setToolTip('管理Pollinations云端模型：刷新模型列表、查看模型信息')
    self.Pollinations管理.clicked.connect(lambda: self.open_pollinations_model_manager())

    self.Pollinations刷新 = QPushButton('刷新模型')
    self.Pollinations刷新.setToolTip('立即刷新云端模型列表，获取最新可用模型')
    self.Pollinations刷新.clicked.connect(lambda: self.刷新Pollinations模型列表())

    # Pollinations API密钥设置
    self.Pollinations密钥_label = QLabel('API密钥')
    self.Pollinations密钥_label.setToolTip('Pollinations API密钥，用于访问高级模型如kontext\n获取密钥：https://auth.pollinations.ai')

    self.Pollinations密钥 = QLineEdit()
    self.Pollinations密钥.setToolTip('输入您的Pollinations API密钥\n某些模型（如kontext）需要认证才能使用\n获取密钥：https://auth.pollinations.ai')
    self.Pollinations密钥.setPlaceholderText('输入API密钥（可选，用于高级模型）')
    self.Pollinations密钥.setEchoMode(QLineEdit.Password)  # 密码模式显示

    # 从配置加载密钥
    当前密钥 = A0_config.config.get('pollinations_api_key', '')
    self.Pollinations密钥.setText(当前密钥)

    # 连接信号
    self.Pollinations密钥.textChanged.connect(lambda: self.更新Pollinations密钥())

    # 显示/隐藏密钥按钮
    self.Pollinations密钥显示 = QPushButton('👁')
    self.Pollinations密钥显示.setToolTip('显示/隐藏API密钥')
    self.Pollinations密钥显示.setMaximumWidth(self.缩放(30))
    self.Pollinations密钥显示.clicked.connect(lambda: self.切换Pollinations密钥显示())

    # Pollinations风格设置
    self.Pollinations风格_label = QLabel('云端风格')
    self.Pollinations风格 = QComboBox()
    self.Pollinations风格.setToolTip('选择Pollinations专用的图片风格，会自动添加到提示词中')

    # 🎨 使用分类风格系统填充下拉框
    try:
        from pyz.UI文件.软件设置UI_methods.Pollinations风格分类 import (
            POLLINATIONS_STYLE_CATEGORIES, 初始化风格配置
        )

        # 初始化风格配置
        初始化风格配置()

        # 清空并重新添加选项（按分类组织）
        self.Pollinations风格.clear()
        self.Pollinations风格.addItem('无风格')  # 默认选项

        # 按分类添加风格选项
        for 分类名称, 分类数据 in POLLINATIONS_STYLE_CATEGORIES.items():
            # 添加分类分隔符（不可选择）
            self.Pollinations风格.addItem(f"━━━ {分类名称} ━━━")
            分隔符索引 = self.Pollinations风格.count() - 1

            # 设置分隔符样式（不可选择）
            item = self.Pollinations风格.model().item(分隔符索引)
            item.setEnabled(False)  # 禁用选择
            item.setData("color: #888888; font-weight: bold; background-color: #f0f0f0;", 3)  # 设置样式

            # 添加该分类下的具体风格
            for 风格名称 in 分类数据["styles"].keys():
                self.Pollinations风格.addItem(f"  {风格名称}")  # 缩进显示

        print("✅ 已使用分类风格系统填充云端风格下拉框")

    except Exception as e:
        print(f"⚠️ 分类风格系统加载失败，使用传统风格列表: {e}")
        # 回退到传统风格列表
        Pollinations风格_options = A0_config.config.get('pollinations_styles', [
            '无风格', '日式动漫', '国漫风格', '韩漫风格', '美式卡通', '二次元',
            '照片写实', '电影级', '人像摄影', '古典油画', '印象派', '水彩画',
            '素描风格', '版画风格', '赛博朋克', '蒸汽朋克', '波普艺术', '极简主义',
            '奇幻风格', '暗黑风格', '童话风格', '中国风', '日式和风', '欧式古典',
            '美式复古', '霓虹风格', '像素艺术', '低多边形', '毛玻璃'
        ])

        self.Pollinations风格.clear()
        for option in Pollinations风格_options:
            self.Pollinations风格.addItem(option)

    # 设置当前选择
    当前风格 = A0_config.config.get('pollinations_style', '无风格')
    # 处理缩进的风格名称
    if 当前风格 != '无风格':
        缩进风格名 = f"  {当前风格}"
        index = self.Pollinations风格.findText(缩进风格名)
        if index >= 0:
            self.Pollinations风格.setCurrentIndex(index)
        else:
            # 如果找不到缩进版本，尝试原名称
            index = self.Pollinations风格.findText(当前风格)
            if index >= 0:
                self.Pollinations风格.setCurrentIndex(index)
    else:
        self.Pollinations风格.setCurrentText('无风格')

    self.Pollinations风格.activated.connect(lambda: self.更换Pollinations风格())

    # 🎯 Pollinations风格设置（直接集成在云端模型下方）
    self.创建Pollinations风格控件()

    # 工作流选择
    self.工作流选择_label = QLabel('角色一致性工作流')
    self.工作流选择_label.setToolTip('选择角色一致性工作流，支持跨模型使用\nIP-Adapter FaceID支持SD1.5/SDXL/Flux/Kolors')
    self.工作流选择 = QComboBox()
    self.工作流选择.setToolTip('选择角色一致性工作流：\n• IP-Adapter FaceID: 跨模型支持，真正的模型无关方案\n• InstantID: SDXL专用，效果优秀\n• Pollinations: 云端方案，无需本地GPU')

    # 加载可用工作流
    self.加载工作流选项()

    # ✅ 删除多余的修复代码，保持简洁

    # 初始化UI状态
    self.更新Pollinations显示状态()
    self.动画模式_label = QLabel('动画模型')
    self.动画模式_label.setToolTip('文本或图片生成视频, "腾讯混元"速度快, 但对提示词理解会差一些,\n"阿里万相" 为Wan2.1, 对提示词理解更准确, 但速度慢, 适合要求比较高的场景')
    self.动画模式 = QComboBox()
    self.动画模式.setToolTip('文本或图片生成视频, "腾讯混元"速度快, 但对提示词理解会差一些,\n"阿里万相" 为Wan2.1, 对提示词理解更准确, 但速度慢, 适合要求比较高的场景')
    options = ['万相图生视频-480', '万相图生视频-720', '万相文生视频-1.3B', '万相文生视频-14B', 'FramePack图生视频', '混元文生视频', '混元文生视频-快速', '混元图生视频', f'{A0_config.name}会员专享-免费', f'{A0_config.name}专用-快速(0.1元/次)', f'{A0_config.name}专用-优质(0.5元/次)']
    for option in options:
        self.动画模式.addItem(option)
    self.动画模式.setCurrentText(A0_config.config['当前选中动画模型'])
    self.万相加速 = QCheckBox('万相加速')
    self.万相加速.setToolTip('万相视频加速技术, 损失少量质量, 大幅提高视频制作效率')
    self.万相加速.setChecked(A0_config.config['万相加速'])
    self.图片风格_label = QLabel('图片风格')
    self.图片风格_label.setToolTip('在模型的基础上微调图片风格, \n部分1.5的模型不生效, \n对XL模型友好')
    self.图片风格 = QComboBox()
    self.图片风格.setToolTip('在模型的基础上微调图片风格, \n部分1.5的模型不生效, \n对XL模型友好')
