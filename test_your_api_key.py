#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试您设置的API密钥
"""

import os
import sys

# 添加项目路径到sys.path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from pyz.任务运行文件 import A0_config

def test_您的API密钥():
    """测试您设置的API密钥"""
    print("=" * 80)
    print("检查您设置的API密钥")
    print("=" * 80)
    
    # 检查当前配置
    api_key = A0_config.config.get('pollinations_api_key', '')
    使用Pollinations = A0_config.config.get('使用Pollinations', False)
    当前模型 = A0_config.config.get('pollinations_model', 'flux')
    
    print(f"📋 当前配置:")
    print(f"   ☁️ 使用Pollinations: {使用Pollinations}")
    print(f"   🎯 当前模型: {当前模型}")
    print(f"   🔑 API密钥: {'已设置' if api_key else '未设置'}")
    
    if api_key:
        print(f"   📏 密钥长度: {len(api_key)} 字符")
        print(f"   👀 密钥预览: {api_key[:4]}...{api_key[-4:] if len(api_key) > 8 else ''}")
    
    return api_key

def test_API参数构建():
    """测试API参数构建"""
    print("\n" + "=" * 80)
    print("测试API参数构建")
    print("=" * 80)
    
    # 模拟工作流中的参数构建
    api_key = A0_config.config.get('pollinations_api_key', '').strip()
    模型名称 = A0_config.config.get('pollinations_model', 'flux')
    
    print(f"🔧 构建API参数:")
    print(f"   📋 模型: {模型名称}")
    print(f"   🔑 密钥: {'已获取' if api_key else '未获取'}")
    
    # 构建参数
    params = {}
    
    # 首先添加token（如果有）
    if api_key:
        params['token'] = api_key
        print(f"   ✅ 添加token参数 (长度: {len(api_key)})")
    else:
        print(f"   ❌ 跳过token参数 (密钥为空)")
    
    # 添加其他参数
    params.update({
        'model': 模型名称,
        'width': 1280,
        'height': 960,
        'seed': 123456789,
        'enhance': 'true',
        'safe': 'true',
        'private': 'true',
        'nologo': 'true'
    })
    
    print(f"\n🌐 最终API参数:")
    for key, value in params.items():
        if key == 'token':
            print(f"   - {key}: {'*' * min(len(str(value)), 25)} 🔑 API密钥认证")
        elif key == 'model':
            print(f"   - {key}: {value} ⭐⭐⭐ 关键模型参数")
        else:
            print(f"   - {key}: {value}")
    
    # 检查token位置
    参数键列表 = list(params.keys())
    if 'token' in 参数键列表:
        token位置 = 参数键列表.index('token')
        if token位置 == 0:
            print(f"\n✅ token参数在第一位 (位置: {token位置 + 1})")
        else:
            print(f"\n⚠️ token参数不在第一位 (位置: {token位置 + 1})")
    else:
        print(f"\n❌ 缺少token参数")
    
    return params

def test_URL构建():
    """测试URL构建"""
    print("\n" + "=" * 80)
    print("测试URL构建")
    print("=" * 80)
    
    try:
        from urllib.parse import quote
        
        # 获取参数
        api_key = A0_config.config.get('pollinations_api_key', '').strip()
        模型名称 = A0_config.config.get('pollinations_model', 'flux')
        
        # 构建参数
        params = {}
        if api_key:
            params['token'] = api_key
        
        params.update({
            'model': 模型名称,
            'width': 1280,
            'height': 960,
            'seed': 123456789
        })
        
        # 构建URL
        提示词 = "A beautiful landscape scene"
        编码提示词 = quote(提示词)
        base_url = f"https://image.pollinations.ai/prompt/{编码提示词}"
        
        # 构建参数字符串
        param_strings = []
        for key, value in params.items():
            param_strings.append(f"{key}={value}")
        
        完整URL = f"{base_url}?{'&'.join(param_strings)}"
        
        print(f"🔗 URL构建:")
        print(f"   📝 基础URL: {base_url}")
        print(f"   🔧 参数数量: {len(params)}")
        print(f"   📊 URL长度: {len(完整URL)} 字符")
        
        # 检查token在URL中的位置
        if 'token=' in 完整URL:
            token_pos = 完整URL.find('token=')
            model_pos = 完整URL.find('model=')
            
            if token_pos < model_pos:
                print(f"   ✅ token参数在model参数之前")
            else:
                print(f"   ⚠️ token参数在model参数之后")
        else:
            print(f"   ❌ URL中缺少token参数")
        
        # 显示URL（隐藏token值）
        if api_key and 'token=' in 完整URL:
            显示URL = 完整URL.replace(api_key, '*' * len(api_key))
            print(f"   🔗 完整URL (隐藏token): {显示URL}")
        else:
            print(f"   🔗 完整URL: {完整URL}")
        
        return 完整URL
        
    except Exception as e:
        print(f"❌ URL构建失败: {e}")
        return None

def test_kontext模型认证():
    """测试kontext模型认证状态"""
    print("\n" + "=" * 80)
    print("测试kontext模型认证状态")
    print("=" * 80)
    
    api_key = A0_config.config.get('pollinations_api_key', '')
    当前模型 = A0_config.config.get('pollinations_model', 'flux')
    使用Pollinations = A0_config.config.get('使用Pollinations', False)
    
    print(f"🎯 模型认证分析:")
    print(f"   📋 当前模型: {当前模型}")
    print(f"   🔑 API密钥: {'已设置' if api_key else '未设置'}")
    print(f"   ☁️ Pollinations启用: {使用Pollinations}")
    
    if 当前模型 == 'kontext':
        if api_key:
            print(f"\n✅ 完美配置！")
            print(f"   🎯 使用kontext模型")
            print(f"   🔑 已设置API密钥")
            print(f"   💡 应该可以正常生成图片")
            状态 = "完美"
        else:
            print(f"\n⚠️ 配置不完整！")
            print(f"   🎯 使用kontext模型")
            print(f"   ❌ 缺少API密钥")
            print(f"   💡 需要设置API密钥才能使用kontext")
            状态 = "缺少密钥"
    else:
        print(f"\n🔍 使用其他模型: {当前模型}")
        if api_key:
            print(f"   🔑 已设置API密钥，可以使用高级功能")
            状态 = "其他模型有密钥"
        else:
            print(f"   ⚪ 未设置API密钥，使用免费功能")
            状态 = "其他模型无密钥"
    
    return 状态

if __name__ == "__main__":
    print("🚀 开始检查您的API密钥设置")
    
    # 检查API密钥
    api_key = test_您的API密钥()
    
    # 测试参数构建
    params = test_API参数构建()
    
    # 测试URL构建
    url = test_URL构建()
    
    # 测试认证状态
    状态 = test_kontext模型认证()
    
    print("\n" + "=" * 80)
    print("检查结果总结")
    print("=" * 80)
    
    if api_key:
        print("🎉 太好了！您已经设置了API密钥")
        
        if params and 'token' in params:
            print("✅ API参数构建正确，包含token")
        else:
            print("❌ API参数构建有问题")
        
        if url and 'token=' in url:
            print("✅ URL构建正确，包含token参数")
        else:
            print("❌ URL构建有问题")
        
        if 状态 == "完美":
            print("🎯 配置完美！现在生成图片应该会包含token参数")
            print("\n💡 下次生成图片时，您应该看到:")
            print("   🔑 使用API密钥认证 (长度: XX 字符)")
            print("   - token: ******************** 🔑 API密钥认证")
            print("   - model: kontext ⭐⭐⭐ 关键模型参数")
        else:
            print(f"⚠️ 配置状态: {状态}")
    else:
        print("❌ 您还没有设置API密钥")
        print("\n💡 请在软件设置中:")
        print("1. 启用'使用Pollinations云端模型'")
        print("2. 在'API密钥'输入框中输入您的密钥")
        print("3. 获取密钥：https://auth.pollinations.ai")
    
    print("=" * 80)
