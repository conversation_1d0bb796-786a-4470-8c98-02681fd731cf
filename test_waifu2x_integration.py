#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Waifu2x集成测试脚本
用于验证Waifu2x放大功能是否正常工作
"""

import os
import sys
import time
from PIL import Image, ImageDraw, ImageFont

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_image(width=400, height=300, filename="test_image.png"):
    """创建测试图片"""
    try:
        # 创建一个简单的测试图片
        img = Image.new('RGB', (width, height), color='white')
        draw = ImageDraw.Draw(img)
        
        # 绘制一些图形
        draw.rectangle([50, 50, width-50, height-50], outline='blue', width=3)
        draw.ellipse([100, 100, width-100, height-100], fill='lightblue', outline='darkblue', width=2)
        
        # 添加文字
        try:
            # 尝试使用系统字体
            font = ImageFont.truetype("arial.ttf", 20)
        except:
            # 如果没有找到字体，使用默认字体
            font = ImageFont.load_default()
        
        draw.text((width//2-50, height//2-10), "Test Image", fill='black', font=font)
        draw.text((width//2-60, height//2+20), f"{width}x{height}", fill='gray', font=font)
        
        # 保存图片
        img.save(filename)
        print(f"✅ 测试图片已创建: {filename} ({width}x{height})")
        return filename
        
    except Exception as e:
        print(f"❌ 创建测试图片失败: {e}")
        return None

def test_waifu2x_module():
    """测试Waifu2x模块导入和基本功能"""
    try:
        print("🔍 测试Waifu2x模块导入...")
        from pyz.任务运行文件.A32_waifu2x放大 import Waifu2xUpscaler, waifu2x_upscale_single
        print("✅ Waifu2x模块导入成功")
        
        # 测试类实例化
        upscaler = Waifu2xUpscaler()
        print("✅ Waifu2xUpscaler实例化成功")
        print(f"📊 API地址: {upscaler.api_url}")
        print(f"📊 文件大小限制: {upscaler.max_file_size / 1024 / 1024:.1f}MB")
        print(f"📊 放大尺寸限制: {upscaler.max_upscaling_size}")
        
        return True
        
    except Exception as e:
        print(f"❌ Waifu2x模块测试失败: {e}")
        return False

def test_image_limits():
    """测试图片限制检查"""
    try:
        print("\n🔍 测试图片限制检查...")
        from pyz.任务运行文件.A32_waifu2x放大 import Waifu2xUpscaler
        
        upscaler = Waifu2xUpscaler()
        
        # 创建测试图片
        test_image = create_test_image(400, 300, "test_limits.png")
        if not test_image:
            return False
        
        # 测试限制检查
        check_ok, check_msg = upscaler.check_image_limits(test_image, 2)
        print(f"📊 限制检查结果: {check_ok} - {check_msg}")
        
        # 清理测试文件
        if os.path.exists(test_image):
            os.remove(test_image)
        
        return check_ok
        
    except Exception as e:
        print(f"❌ 图片限制检查测试失败: {e}")
        return False

def test_waifu2x_upscale():
    """测试实际的Waifu2x放大功能"""
    try:
        print("\n🚀 测试Waifu2x在线放大功能...")
        from pyz.任务运行文件.A32_waifu2x放大 import waifu2x_upscale_single
        
        # 创建测试图片
        test_image = create_test_image(300, 200, "test_upscale.png")
        if not test_image:
            return False
        
        print(f"📤 开始上传并放大图片: {test_image}")
        print("⏳ 请等待，这可能需要几十秒...")
        
        # 执行放大
        start_time = time.time()
        result = waifu2x_upscale_single(test_image, 2)
        end_time = time.time()
        
        if result:
            print(f"✅ Waifu2x放大成功!")
            print(f"📊 耗时: {end_time - start_time:.1f}秒")
            print(f"📁 原图: {test_image}")
            print(f"📁 放大图: {result}")
            
            # 检查放大后的图片
            if os.path.exists(result):
                with Image.open(test_image) as orig_img:
                    orig_size = orig_img.size
                with Image.open(result) as upscaled_img:
                    upscaled_size = upscaled_img.size
                
                print(f"📊 原图尺寸: {orig_size}")
                print(f"📊 放大后尺寸: {upscaled_size}")
                print(f"📊 放大倍数: {upscaled_size[0]/orig_size[0]:.1f}x")
                
                # 清理测试文件
                os.remove(test_image)
                print(f"🗑️ 测试完成，可以删除放大后的图片: {result}")
                
                return True
            else:
                print("❌ 放大后的图片文件不存在")
                return False
        else:
            print("❌ Waifu2x放大失败")
            # 清理测试文件
            if os.path.exists(test_image):
                os.remove(test_image)
            return False
            
    except Exception as e:
        print(f"❌ Waifu2x放大测试失败: {e}")
        return False

def test_config_integration():
    """测试配置集成"""
    try:
        print("\n🔍 测试配置集成...")
        from pyz.任务运行文件 import A0_config
        
        # 检查放大模式配置
        放大模式 = A0_config.config.get('放大模式', '未设置')
        print(f"📊 当前放大模式: {放大模式}")
        
        # 检查放大倍数配置
        放大倍数 = A0_config.config.get('修复放大倍数', '未设置')
        print(f"📊 当前放大倍数: {放大倍数}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🎯 Waifu2x集成测试开始")
    print("=" * 50)
    
    tests = [
        ("模块导入测试", test_waifu2x_module),
        ("图片限制检查测试", test_image_limits),
        ("配置集成测试", test_config_integration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        if test_func():
            print(f"✅ {test_name} 通过")
            passed += 1
        else:
            print(f"❌ {test_name} 失败")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有基础测试通过！")
        
        # 询问是否进行实际放大测试
        try:
            user_input = input("\n❓ 是否进行实际的在线放大测试？(需要网络连接，耗时较长) [y/N]: ")
            if user_input.lower() in ['y', 'yes']:
                print("\n🚀 开始实际放大测试...")
                if test_waifu2x_upscale():
                    print("🎉 实际放大测试也通过了！Waifu2x集成完全正常！")
                else:
                    print("⚠️ 实际放大测试失败，可能是网络问题或API暂时不可用")
            else:
                print("ℹ️ 跳过实际放大测试")
        except KeyboardInterrupt:
            print("\n⏹️ 测试被用户中断")
    else:
        print("❌ 部分测试失败，请检查集成代码")

if __name__ == "__main__":
    main()
